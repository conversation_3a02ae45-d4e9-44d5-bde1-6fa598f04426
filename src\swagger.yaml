openapi: 3.0.0
info:
  title: Sensory Analysis Panel Management API
  description: >-
    API for managing sensory analysis panels, user registration, responses and
    panel data
  version: 1.0.0
servers:
  - url: https://sensei.neuralresonance.icu
    description: Remote development server
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Group:
      type: object
      required:
        - name
        - description
        - createdBy
        - users
        - panels
        - samples
        - organization_id
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: Display name of the group
        description:
          type: string
          minLength: 2
          maxLength: 500
          description: Description of the group
        createdBy:
          type: string
          description: ID of the user that created the group
        users:
          type: array
          items:
            type: string
          description: List of user IDs that are in this group
        panels:
          type: array
          items:
            type: string
          description: List of panel IDs that are in this group
        samples:
          type: array
          items:
            type: string
          description: List of sample IDs that are in this group
        organization_id:
          type: string
          description: ID of the organization that owns this group
    Option:
      type: object
      required:
        - label
        - instructions
        - organization_id
        - question_id
        - option_id
      properties:
        label:
          type: string
          description: The label of the option
        instructions:
          type: string
          description: Instructions for the option
        sliderCenter:
          type: string
          nullable: true
          description: The center value of the slider option
        sliderLeft:
          type: string
          nullable: true
          description: The left value of the slider option
        sliderRight:
          type: string
          nullable: true
          description: The right value of the slider option
        organization_id:
          type: string
          description: The organization ID of the option
        question_id:
          type: string
          description: The question ID the option belongs to
        option_id:
          type: string
          description: Unique identifier for the option
      example:
        label: Aroma Intensity
        instructions: Rate the intensity of the aroma
        sliderLeft: Very weak
        sliderCenter: Moderate
        sliderRight: Very intense
        organization_id: org123456789
        question_id: question123456789
        option_id: option123456789
    Organization:
      type: object
      required:
        - organization_id
        - name
        - website
        - affiliate_code
      properties:
        organization_id:
          type: string
          description: Unique identifier for the organization
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: Display name of the organization
        website:
          type: string
          format: uri
          description: Organization website URL
        affiliate_code:
          type: string
          pattern: ^[A-Z0-9]{4,20}$
          description: Unique affiliate code for the organization
    Panel:
      type: object
      required:
        - active
        - allowedOrganizations
        - description
        - invite_code
        - isPublic
        - name
        - organization_id
        - panel_id
        - product_id
        - value
        - budget
        - minutes
        - start_date
        - end_date
        - steps
        - consumption_option
        - minimum_certification_level
      properties:
        active:
          type: boolean
          description: Whether the panel is currently active
        allowedOrganizations:
          type: array
          items:
            type: string
          description: List of organization IDs that can access this panel
        description:
          type: string
          minLength: 2
          maxLength: 500
          description: Description of the panel
        invite_code:
          type: string
          pattern: ^[A-Z0-9]{4,20}$
          description: Unique invite code for the panel
        isPublic:
          type: boolean
          description: Whether the panel is publicly accessible
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: Display name of the panel
        organization_id:
          type: string
          description: ID of the organization that owns this panel
        panel_id:
          type: string
          description: ID of the panel
        product_id:
          type: string
          description: ID of the product that this panel is associated with
        value:
          type: number
          description: Value of the panel
        budget:
          type: number
          description: Budget of the panel
        minutes:
          type: number
          description: Number of minutes to complete the panel
        start_date:
          type: string
          format: date-time
          description: Date and time when the panel will be available
        end_date:
          type: string
          format: date-time
          description: Date and time when the panel will no longer be available
        steps:
          type: array
          items:
            type: string
          description: List of step IDs that make up the panel
        consumption_option:
          type: string
          description: Consumption option of the panel
        minimum_certification_level:
          type: number
          default: 0
          description: Minimum certification level required to access the panel
    Product:
      type: object
      required:
        - description
        - image
        - name
        - lot_number
        - manufacturing_date
        - product_type
        - packaging_option
        - packaging_date
        - producer
      properties:
        description:
          type: string
          description: Description of the product
        external_id:
          type: string
          description: External identifier for the product
        image:
          type: string
          format: uri
          description: Product image URL
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: Display name of the product
        organization_id:
          type: string
          description: ID of the organization that owns this product
        product_id:
          type: string
          description: ID of the product
        lot_number:
          type: string
          description: Lot number of the product
        manufacturing_date:
          type: string
          format: date
          description: Manufacturing date of the product
        notes:
          type: string
          description: Notes about the product
        product_type:
          type: string
          description: Type of the product
        product_subtype:
          type: string
          description: Subtype of the product
        packaging_option:
          type: string
          description: Packaging option of the product
        packaging_date:
          type: string
          format: date
          description: Packaging date of the product
        producer:
          type: string
          description: Producer of the product
      example:
        description: Premium vaporizer cartridge with full-spectrum distillate
        external_id: VPC001
        image: https://example.com/images/product1.jpg
        name: Premium Vape Cartridge
        organization_id: org123456
        product_id: prod789012
        lot_number: L2023101
        manufacturing_date: '2023-10-01'
        notes: Bestselling product in Q4 2023
        product_type: Vaporizer
        product_subtype: Cartridge
        packaging_option: Glass cartridge with box
        packaging_date: '2023-10-05'
        producer: Abstrax Labs
    Project:
      type: object
      required:
        - name
        - owner
        - organization_id
        - organization_id
        - status
        - start_date
        - end_date
        - created_by
        - owner
      properties:
        project_id:
          type: string
          description: Unique identifier for the project
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: Name of the project
        description:
          type: string
          minLength: 2
          maxLength: 500
          description: Description of the project
        organization_id:
          type: string
          description: Organization ID the project belongs to
        assigned_panels:
          type: array
          items:
            type: string
          description: List of panel IDs directly assigned to this project
        assigned_users:
          type: array
          items:
            type: string
          description: List of user IDs directly assigned to this project
        assigned_samples:
          type: array
          items:
            type: string
          description: List of sample/product IDs directly assigned to this project
        assigned_groups:
          type: object
          properties:
            user_groups:
              type: array
              items:
                type: string
            panel_groups:
              type: array
              items:
                type: string
            sample_groups:
              type: array
              items:
                type: string
          description: Groups assigned to this project
        collaborators:
          type: array
          items:
            type: string
          description: List of user IDs who can collaborate on the project
        status:
          type: string
          enum:
            - draft
            - ongoing
            - scheduled
            - completed
            - archived
          description: Current status of the project
        is_public:
          type: boolean
          default: false
          description: Whether the project is public
        completion_percentage:
          type: number
          minimum: 0
          maximum: 100
          default: 0
          description: Percentage of project completion
        total_responses:
          type: number
          default: 0
          description: Total number of responses across all panels in the project
        start_date:
          type: string
          format: date-time
          description: Project start date
        end_date:
          type: string
          format: date-time
          description: Project end date
        created_by:
          type: string
          description: ID of the user who created the project
        owner:
          type: string
          description: User ID of the project owner
        users:
          type: array
          items:
            type: string
          description: List of user IDs with access to the project
        panels:
          type: array
          items:
            type: string
          description: List of panel IDs associated with the project
        samples:
          type: array
          items:
            type: string
          description: List of sample/product IDs associated with the project
    Question:
      type: object
      required:
        - name
        - organization_id
        - question_id
        - isDefault
        - stepType
        - stepInstructions
        - optionIds
      properties:
        name:
          type: string
          description: Name of the question
        organization_id:
          type: string
          description: ID of the organization this question belongs to
        question_id:
          type: string
          description: Unique identifier for the question
        isDefault:
          type: boolean
          description: Whether this is a default question
        required:
          type: boolean
          default: false
          description: Whether this question is required to be answered
        stepType:
          type: string
          description: The type of question (e.g., range-slider, multiple-choice)
        stepInstructions:
          type: string
          description: Instructions for the question
        optionIds:
          type: array
          items:
            type: string
          description: Array of option IDs belonging to this question
        options:
          type: array
          items:
            $ref: '#/components/schemas/Option'
          description: Array of hydrated option objects (when requested with hydration)
      example:
        name: Aroma Evaluation
        organization_id: org123456789
        question_id: question123456789
        isDefault: false
        required: true
        stepType: range-slider
        stepInstructions: Please evaluate the aroma of the product
        optionIds:
          - option123
          - option456
          - option789
    Response:
      type: object
      required:
        - question_id
        - panel_id
        - product_id
        - organization_id
        - user_id
        - data
      properties:
        question_id:
          type: string
          description: Unique identifier for the question being answered
        panel_id:
          type: string
          description: Unique identifier for the panel being answered
        product_id:
          type: string
          description: Unique identifier for the product being answered
        organization_id:
          type: string
          description: Unique identifier for the organization being answered
        user_id:
          type: string
          description: Unique identifier for the user who submitted the response
        data:
          type: object
          description: >-
            Map of option_id to response value, contains the actual response
            data
          additionalProperties: true
    User:
      type: object
      required:
        - email
        - first_name
        - last_name
        - organizations
        - uid
        - roles
        - zipCode
        - firstLogin
      properties:
        email:
          type: string
          format: email
          description: User email address
        first_name:
          type: string
          minLength: 2
          maxLength: 50
          description: User first name
        last_name:
          type: string
          minLength: 2
          maxLength: 50
          description: User last name
        organizations:
          type: array
          items:
            type: string
          description: List of organization IDs the user belongs to
        uid:
          type: string
          description: Unique identifier for the user
        roles:
          type: array
          items:
            type: string
            enum:
              - survey
              - admin
              - insights
          description: List of roles the user has
        zipCode:
          type: string
          description: User zip code
        firstLogin:
          type: boolean
          description: Whether the user is logging in for the first time
paths:
  /auth/session:
    get:
      summary: Create a session
      description: Creates a session cookie for the authenticated user
      tags:
        - Authentication
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Session created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          description: Failed to create session
  /auth/logout:
    get:
      summary: Logout user
      description: Revokes refresh tokens and clears the session cookie
      tags:
        - Authentication
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Logged out successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
  /auth/session-validity:
    get:
      summary: Check session validity
      description: Validates if the current session is still valid
      tags:
        - Authentication
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Session is valid
          content:
            application/json:
              schema:
                type: boolean
        '401':
          description: Unauthorized - session is invalid
  /auth/register:
    post:
      summary: Register new user
      description: Creates a new user account with the provided information
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - first_name
                - last_name
                - zipCode
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
                first_name:
                  type: string
                last_name:
                  type: string
                zipCode:
                  type: string
                affiliateCode:
                  type: string
                marketingOptIn:
                  type: boolean
                isBrand:
                  type: boolean
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  userId:
                    type: string
        '400':
          description: Invalid user data or affiliate code
        '500':
          description: Server error
  /constants/{type}:
    get:
      summary: Get constants by type
      description: >-
        Retrieves constants of a specific type (e.g., demographics, flavors,
        effects)
      tags:
        - Constants
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: type
          required: true
          schema:
            type: string
          description: Type of constants to retrieve (e.g., demographics, flavors, effects)
      responses:
        '200':
          description: Constants retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /groups:
    post:
      summary: Create a new group
      description: Creates a new group with the current user as creator
      tags:
        - Groups
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Group'
      responses:
        '201':
          description: Group created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Group'
                  - type: object
                    properties:
                      group_id:
                        type: string
                      created:
                        type: object
                      updated:
                        type: object
        '400':
          description: Invalid group data
        '401':
          description: Unauthorized
        '500':
          description: Server error
    get:
      summary: Get all groups for the current user
      description: >-
        Retrieves all groups that the authenticated user has created or has
        access to
      tags:
        - Groups
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Groups retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /groups/{id}:
    get:
      summary: Get a group by ID
      description: >-
        Retrieves details for a specific group, with optional reference
        hydration
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: ID of the group to retrieve
        - in: query
          name: includeReferences
          schema:
            type: boolean
          description: Whether to include hydrated references
        - in: query
          name: includeFullDetails
          schema:
            type: boolean
          description: Whether to include full details for all references
      responses:
        '200':
          description: Group details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
  /groups/user/{userId}:
    get:
      summary: Get groups by user ID
      description: >-
        Retrieves all groups associated with a specific user, with optional
        reference hydration
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: string
          description: ID of the user
        - in: query
          name: includeReferences
          schema:
            type: boolean
          description: Whether to include hydrated references
        - in: query
          name: includeFullDetails
          schema:
            type: boolean
          description: Whether to include full details for all references
      responses:
        '200':
          description: Groups retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /groups/organization/{organizationId}:
    get:
      summary: Get groups by organization ID
      description: >-
        Retrieves all groups associated with a specific organization, with
        optional reference hydration
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: organizationId
          required: true
          schema:
            type: string
          description: ID of the organization
        - in: query
          name: includeReferences
          schema:
            type: boolean
          description: Whether to include hydrated references
        - in: query
          name: includeFullDetails
          schema:
            type: boolean
          description: Whether to include full details for all references
      responses:
        '200':
          description: Groups retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /groups/{group_id}:
    put:
      summary: Update a group
      description: Updates an existing group with the provided data
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Group'
      responses:
        '200':
          description: Group updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
    delete:
      summary: Delete a group
      description: Deletes an existing group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group to delete
      responses:
        '200':
          description: Group deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
  /groups/{group_id}/users/{user_id}:
    post:
      summary: Add a user to a group
      description: Associates a user with a group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group
        - in: path
          name: user_id
          required: true
          schema:
            type: string
          description: ID of the user to add
      responses:
        '200':
          description: User added to group successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
    delete:
      summary: Remove a user from a group
      description: Removes the association between a user and a group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group
        - in: path
          name: user_id
          required: true
          schema:
            type: string
          description: ID of the user to remove
      responses:
        '200':
          description: User removed from group successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
  /groups/{group_id}/panels/{panel_id}:
    post:
      summary: Add a panel to a group
      description: Associates a panel with a group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to add
      responses:
        '200':
          description: Panel added to group successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
    delete:
      summary: Remove a panel from a group
      description: Removes the association between a panel and a group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to remove
      responses:
        '200':
          description: Panel removed from group successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
  /groups/{group_id}/samples/{sample_id}:
    post:
      summary: Add a sample to a group
      description: Associates a sample with a group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group
        - in: path
          name: sample_id
          required: true
          schema:
            type: string
          description: ID of the sample to add
      responses:
        '200':
          description: Sample added to group successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
    delete:
      summary: Remove a sample from a group
      description: Removes the association between a sample and a group
      tags:
        - Groups
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group
        - in: path
          name: sample_id
          required: true
          schema:
            type: string
          description: ID of the sample to remove
      responses:
        '200':
          description: Sample removed from group successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have access to this group
        '500':
          description: Server error
  /insights/overview:
    get:
      summary: Get insights overview
      description: Retrieves overview data for insights dashboard
      tags:
        - Insights
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Overview data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                description: Insights overview data
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have insights permission
        '500':
          description: Server error
  /insights/panels:
    get:
      summary: Get panels for insights
      description: Retrieves all panels available for insights analysis with pagination
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter panels
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: Panels retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  panels:
                    type: array
                    items:
                      $ref: '#/components/schemas/Panel'
                  currentPage:
                    type: integer
                  totalPages:
                    type: integer
                  hasMore:
                    type: boolean
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have insights permission
        '500':
          description: Server error
  /insights/products:
    get:
      summary: Get products for insights
      description: Retrieves all products available for insights analysis with pagination
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter products
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  currentPage:
                    type: integer
                  totalPages:
                    type: integer
                  hasMore:
                    type: boolean
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have insights permission
        '500':
          description: Server error
  /insights/count/panels-using-product/{product_id}:
    get:
      summary: Count panels using a product
      description: Returns the count of panels that use a specific product
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product
      responses:
        '200':
          description: Count retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have insights permission
        '500':
          description: Server error
  /insights/panels/count/{panel_id}:
    get:
      summary: Get panel response count
      description: Returns the count of responses for a specific panel
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel
      responses:
        '200':
          description: Count retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have insights permission
        '500':
          description: Server error
  /insights/panels/comments/{panel_id}:
    get:
      summary: Get panel comments
      description: Retrieves all comments for a specific panel
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel
      responses:
        '200':
          description: Comments retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    text:
                      type: string
        '401':
          description: Unauthorized
        '403':
          description: >-
            Forbidden - user cannot access this panel or does not have insights
            permission
        '500':
          description: Server error
  /insights/panels/{panel_id}:
    get:
      summary: Get panel aggregate data
      description: Retrieves aggregated data for a specific panel
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel
      responses:
        '200':
          description: Panel data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                description: Aggregated panel data
        '401':
          description: Unauthorized
        '403':
          description: >-
            Forbidden - user cannot access this panel or does not have insights
            permission
        '500':
          description: Server error
  /insights/products/count/{product_id}:
    get:
      summary: Get product response count
      description: Returns the count of responses for a specific product
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product
      responses:
        '200':
          description: Count retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have insights permission
        '500':
          description: Server error
  /insights/products/comments/{product_id}:
    get:
      summary: Get product comments
      description: Retrieves all comments for a specific product
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product
      responses:
        '200':
          description: Comments retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    text:
                      type: string
        '401':
          description: Unauthorized
        '403':
          description: >-
            Forbidden - user cannot access this product or does not have
            insights permission
        '500':
          description: Server error
  /insights/products/{product_id}:
    get:
      summary: Get product aggregate data
      description: Retrieves aggregated data for a specific product
      tags:
        - Insights
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product
      responses:
        '200':
          description: Product data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                description: Aggregated product data
        '401':
          description: Unauthorized
        '403':
          description: >-
            Forbidden - user cannot access this product or does not have
            insights permission
        '500':
          description: Server error
  /integrations/products/{product_id}:
    get:
      summary: Get product data
      description: Retrieves aggregated data for a product by its external ID
      tags:
        - Integrations
      security:
        - ApiKeyAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: External ID of the product
      responses:
        '200':
          description: Product data retrieved successfully or product not found message
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    description: Aggregated product data
                  - type: object
                    properties:
                      error:
                        type: boolean
                      message:
                        type: string
        '401':
          description: Unauthorized - invalid API key
        '500':
          description: Server error
  /integrations/constants/{type}:
    get:
      summary: Get constants by type
      description: Retrieves constants of a specific type for use in integrations
      tags:
        - Integrations
      security:
        - ApiKeyAuth: []
      parameters:
        - in: path
          name: type
          required: true
          schema:
            type: string
          description: Type of constants to retrieve (e.g., demographics, flavors, effects)
      responses:
        '200':
          description: Constants retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
        '401':
          description: Unauthorized - invalid API key
  /integrations:
    get:
      summary: Check integration API status
      description: Simple endpoint to verify the integration API is working
      tags:
        - Integrations
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: API is working
          content:
            text/plain:
              schema:
                type: string
                example: Hello World
        '401':
          description: Unauthorized - invalid API key
  /organizations:
    get:
      summary: Get all organizations
      description: Retrieves all organizations accessible to the admin user with pagination
      tags:
        - Organizations
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for pagination
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of items per page
      responses:
        '200':
          description: List of organizations retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  organizations:
                    type: array
                    items:
                      $ref: '#/components/schemas/Organization'
                  total:
                    type: integer
                    description: Total number of organizations
                  page:
                    type: integer
                    description: Current page number
                  limit:
                    type: integer
                    description: Number of items per page
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have admin permissions
        '500':
          description: Server error
    post:
      summary: Create a new organization
      description: Creates a new organization with the provided data
      tags:
        - Organizations
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Organization'
      responses:
        '201':
          description: Organization created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Organization'
                  - type: object
                    properties:
                      organization_id:
                        type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have admin permissions
        '500':
          description: Server error
  /organizations/users/{org_id}:
    get:
      summary: Get users in an organization
      description: Retrieves all users belonging to a specific organization
      tags:
        - Organizations
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: org_id
          required: true
          schema:
            type: string
          description: ID of the organization
      responses:
        '200':
          description: List of users retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this organization
        '500':
          description: Server error
  /organizations/{org_id}:
    get:
      summary: Get organization details
      description: Retrieves detailed information about a specific organization
      tags:
        - Organizations
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: org_id
          required: true
          schema:
            type: string
          description: ID of the organization to retrieve
      responses:
        '200':
          description: Organization details retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Organization'
                  - type: object
                    properties:
                      organization_id:
                        type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
        '500':
          description: Server error
    put:
      summary: Update organization details
      description: Updates an existing organization with new name or website data
      tags:
        - Organizations
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: org_id
          required: true
          schema:
            type: string
          description: ID of the organization to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: New organization name
                website:
                  type: string
                  description: New organization website URL
      responses:
        '200':
          description: Organization updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Organization'
                  - type: object
                    properties:
                      organization_id:
                        type: string
        '400':
          description: Bad request - must provide name or website to update
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have admin/insights permissions
        '500':
          description: Server error
  /panels:
    get:
      summary: Get available panels for the user
      description: >-
        Retrieves all panels available to the user that they have not started or
        completed
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter panels
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: Panels retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  panels:
                    type: array
                    items:
                      $ref: '#/components/schemas/Panel'
                  currentPage:
                    type: integer
                  totalPages:
                    type: integer
                  hasMore:
                    type: boolean
        '401':
          description: Unauthorized
        '500':
          description: Server error
    post:
      summary: Create a new panel
      description: Creates a new panel with the provided data
      tags:
        - Panels
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Panel'
      responses:
        '201':
          description: Panel created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Panel'
                  - type: object
                    properties:
                      panel_id:
                        type: string
                      created:
                        type: object
                      updated:
                        type: object
        '400':
          description: Invalid panel data
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /panels/{panel_id}:
    delete:
      summary: Delete a panel
      description: Marks a panel as deleted and removes it from Redis cache
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to delete
      responses:
        '200':
          description: Panel deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this panel
        '500':
          description: Server error
    put:
      summary: Update panel details
      description: Updates an existing panel with new data
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Panel'
      responses:
        '200':
          description: Panel updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Panel'
                  - type: object
                    properties:
                      panel_id:
                        type: string
        '401':
          description: Unauthorized
        '500':
          description: Server error
    get:
      summary: Get panel details
      description: Retrieves detailed information about a specific panel
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to retrieve
      responses:
        '200':
          description: Panel details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Panel'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this panel
        '500':
          description: Server error
  /panels/aggregates/{panel_id}:
    get:
      summary: Get aggregate data for a panel
      description: Retrieves aggregate response data for a specific panel
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to get aggregates for
      responses:
        '200':
          description: Aggregate data retrieved successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this panel
        '500':
          description: Server error
  /panels/completed:
    get:
      summary: Get completed panels for the user
      description: Retrieves all panels that the user has completed
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: Completed panels retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  panels:
                    type: array
                    items:
                      $ref: '#/components/schemas/Panel'
                  currentPage:
                    type: integer
                  totalPages:
                    type: integer
                  hasMore:
                    type: boolean
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /panels/in-progress:
    get:
      summary: Get in-progress panels for the user
      description: Retrieves all panels that the user has started but not completed
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: In-progress panels retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  panels:
                    type: array
                    items:
                      $ref: '#/components/schemas/Panel'
                  currentPage:
                    type: integer
                  totalPages:
                    type: integer
                  hasMore:
                    type: boolean
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /panels/start/{panel_id}:
    get:
      summary: Start a panel
      description: Creates a progress document for the user to start taking a panel
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to start
      responses:
        '200':
          description: Panel started successfully or existing progress returned
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this panel
        '500':
          description: Server error
  /panels/finish/{panel_id}:
    get:
      summary: Finish a panel
      description: Marks a panel as completed for the user and updates aggregate data
      tags:
        - Panels
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to mark as completed
      responses:
        '200':
          description: Panel completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '400':
          description: Panel already completed or not all questions answered
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this panel
        '500':
          description: Server error
  /products:
    get:
      summary: Get all products
      description: Retrieves all products accessible to the user with pagination
      tags:
        - Products
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Organization ID to filter products by
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for pagination
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of items per page
      responses:
        '200':
          description: List of products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  total:
                    type: integer
                    description: Total number of products
                  page:
                    type: integer
                    description: Current page number
                  limit:
                    type: integer
                    description: Number of items per page
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
        '500':
          description: Server error
    post:
      summary: Create a new product
      description: Creates a new product with the provided data
      tags:
        - Products
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Product'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Product'
                  - type: object
                    properties:
                      product_id:
                        type: string
                      created:
                        type: object
                      updated:
                        type: object
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have admin/insights permissions
        '500':
          description: Server error
  /products/{product_id}:
    delete:
      summary: Delete a product
      description: Deletes a specific product by ID
      tags:
        - Products
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product to delete
      responses:
        '200':
          description: Product deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have admin/insights permissions
        '500':
          description: Server error
    put:
      summary: Update product details
      description: Updates an existing product with new data
      tags:
        - Products
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Product'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Product'
                  - type: object
                    properties:
                      product_id:
                        type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have admin/insights permissions
        '500':
          description: Server error
    get:
      summary: Get product details
      description: Retrieves detailed information about a specific product
      tags:
        - Products
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: product_id
          required: true
          schema:
            type: string
          description: ID of the product to retrieve
      responses:
        '200':
          description: Product details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
        '500':
          description: Server error
  /profile/affiliate:
    post:
      summary: Validate and add affiliate code
      description: >-
        Validates an affiliate code and adds associated organizations to the
        user's organization list
      tags:
        - Profile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - affiliateCode
              properties:
                affiliateCode:
                  type: string
                  description: The affiliate code to validate and add
      responses:
        '200':
          description: Affiliate code validated and added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '400':
          description: Invalid affiliate code
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
    delete:
      summary: Remove affiliate code
      description: >-
        Removes the organizations associated with the affiliate code from the
        user's organization list
      tags:
        - Profile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - affiliateCode
              properties:
                affiliateCode:
                  type: string
                  description: The affiliate code to remove
      responses:
        '200':
          description: Affiliate code removed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '400':
          description: Invalid affiliate code
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /profile/first-login:
    put:
      summary: Update first login status
      description: Sets the user's firstLogin flag to false after their first login
      tags:
        - Profile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: First login status updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /profile/me:
    get:
      summary: Get user profile
      description: Retrieves the current user's profile information
      tags:
        - Profile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
    delete:
      summary: Delete user account
      description: Deletes the current user's account and all associated data
      tags:
        - Profile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: User account deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
    put:
      summary: Update user profile
      description: Updates the current user's profile information
      tags:
        - Profile
      security:
        - BearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  description: User's first name
                last_name:
                  type: string
                  description: User's last name
                demographics:
                  type: object
                  properties:
                    gender:
                      type: string
                    age-group:
                      type: string
                    ethnicity:
                      type: string
                    income:
                      type: string
                    hispanic:
                      type: string
      responses:
        '200':
          description: User profile updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '400':
          description: >-
            Bad request - must provide at least one field to update or invalid
            demographics
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /profile/email:
    put:
      summary: Update email address
      description: Updates the user's email address in both Firebase Auth and Firestore
      tags:
        - Profile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  description: New email address
      responses:
        '200':
          description: Email updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /profile/me/organizations:
    get:
      summary: Get user organizations
      description: Retrieves all organizations the user belongs to
      tags:
        - Profile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Organizations retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Organization'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /profile/me/organizations/{organization_id}:
    delete:
      summary: Remove organization from user
      description: Removes the specified organization from the user's list of organizations
      tags:
        - Profile
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: organization_id
          required: true
          schema:
            type: string
          description: ID of the organization to remove
      responses:
        '200':
          description: Organization removed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '400':
          description: Organization not found in user organizations
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
  /projects:
    get:
      summary: Get all projects
      description: >-
        Retrieves all projects accessible to the user, with optional reference
        hydration and pagination
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
        - in: query
          name: include_references
          schema:
            type: boolean
          description: Whether to include referenced entities
        - in: query
          name: full_details
          schema:
            type: boolean
          description: Whether to include full details of referenced entities
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: List of projects retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/Project'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
    post:
      summary: Create a project
      description: Creates a new project with the provided data
      tags:
        - Projects
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - organization_id
              properties:
                name:
                  type: string
                  description: Project name
                description:
                  type: string
                  description: Project description
                organization_id:
                  type: string
                  description: Organization ID the project belongs to
                start_date:
                  type: string
                  format: date-time
                  description: Project start date
                end_date:
                  type: string
                  format: date-time
                  description: Project end date
                users:
                  type: array
                  items:
                    type: string
                  description: Initial list of user IDs with access
                panels:
                  type: array
                  items:
                    type: string
                  description: Initial list of panel IDs
                samples:
                  type: array
                  items:
                    type: string
                  description: Initial list of sample/product IDs
      responses:
        '201':
          description: Project created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          description: Invalid request data
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/user:
    get:
      summary: Get user projects
      description: Retrieves all projects that the authenticated user has access to
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: include_references
          schema:
            type: boolean
          description: Whether to include referenced entities
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: User projects retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/Project'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/counts/open:
    get:
      summary: Get open projects count
      description: Retrieves the count of ongoing and scheduled projects
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
      responses:
        '200':
          description: Open projects count retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/counts/completed:
    get:
      summary: Get completed projects count
      description: Retrieves the count of completed projects
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
      responses:
        '200':
          description: Completed projects count retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/counts/ending-soon:
    get:
      summary: Get count of projects ending soon
      description: Retrieves the count of projects ending within a specified number of days
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
        - in: query
          name: days
          schema:
            type: integer
            default: 7
          description: Number of days threshold for considering a project as ending soon
      responses:
        '200':
          description: Projects ending soon count retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/distribution/product-types:
    get:
      summary: Get product type distribution
      description: Retrieves the distribution of product types across all projects
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
      responses:
        '200':
          description: Product type distribution retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
                    count:
                      type: integer
                    percentage:
                      type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/dashboard-stats:
    get:
      summary: Get dashboard statistics
      description: >-
        Retrieves combined statistics for the dashboard including counts and
        distributions
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
      responses:
        '200':
          description: Dashboard statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  openCount:
                    type: integer
                  completedCount:
                    type: integer
                  endingSoonCount:
                    type: integer
                  productDistribution:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                        count:
                          type: integer
                        percentage:
                          type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/ending-soon:
    get:
      summary: Get projects ending soon
      description: Retrieves projects that are ending within a specified number of days
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter projects
        - in: query
          name: days
          schema:
            type: integer
            default: 7
          description: Number of days threshold for considering a project as ending soon
        - in: query
          name: include_references
          schema:
            type: boolean
          description: Whether to include referenced entities
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        '200':
          description: Projects ending soon retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/Project'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /projects/{project_id}:
    get:
      summary: Get a project
      description: Retrieves a specific project by ID with optional reference hydration
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to retrieve
        - in: query
          name: include_references
          schema:
            type: boolean
          description: Whether to include referenced entities
      responses:
        '200':
          description: Project retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
    put:
      summary: Update a project
      description: Updates an existing project with the provided data
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Project name
                description:
                  type: string
                  description: Project description
                start_date:
                  type: string
                  format: date-time
                  description: Project start date
                end_date:
                  type: string
                  format: date-time
                  description: Project end date
                status:
                  type: string
                  enum:
                    - draft
                    - ongoing
                    - scheduled
                    - completed
                    - archived
                  description: Project status
      responses:
        '200':
          description: Project updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          description: Invalid request data
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
    delete:
      summary: Delete a project
      description: Deletes an existing project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to delete
      responses:
        '200':
          description: Project deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Project deleted successfully
                  project_id:
                    type: string
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
  /projects/{project_id}/panels/{panel_id}:
    post:
      summary: Add a panel to a project
      description: >-
        Associates a panel with a project. If the panel has an associated
        product/sample, that product is automatically added to the project as
        well.
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to add
      responses:
        '200':
          description: >-
            Panel added to project successfully. If the panel has an associated
            product, it was also added to the project.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or panel not found
        '500':
          description: Server error
    delete:
      summary: Remove a panel from a project
      description: >-
        Removes the association between a panel and a project. If the panel has
        an associated product/sample and no other panel in the project is using
        that sample, the sample will be automatically removed from the project
        as well.
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel to remove
      responses:
        '200':
          description: >-
            Panel removed from project successfully. If the panel's associated
            product/sample is not used by any other panel, it was also removed
            from the project.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or panel not found
        '500':
          description: Server error
  /projects/{project_id}/users/{user_id}:
    post:
      summary: Add a user to a project
      description: Associates a user with a project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: user_id
          required: true
          schema:
            type: string
          description: ID of the user to add
      responses:
        '200':
          description: User added to project successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or user not found
        '500':
          description: Server error
    delete:
      summary: Remove a user from a project
      description: Removes the association between a user and a project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: user_id
          required: true
          schema:
            type: string
          description: ID of the user to remove
      responses:
        '200':
          description: User removed from project successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or user not found
        '500':
          description: Server error
  /projects/{project_id}/samples/{sample_id}:
    post:
      summary: Add a sample to a project
      description: Associates a sample/product with a project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: sample_id
          required: true
          schema:
            type: string
          description: ID of the sample/product to add
      responses:
        '200':
          description: Sample added to project successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or sample not found
        '500':
          description: Server error
    delete:
      summary: Remove a sample from a project
      description: Removes the association between a sample/product and a project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: sample_id
          required: true
          schema:
            type: string
          description: ID of the sample/product to remove
      responses:
        '200':
          description: Sample removed from project successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or sample not found
        '500':
          description: Server error
  /projects/{project_id}/collaborators/{user_id}:
    post:
      summary: Add a collaborator to a project
      description: Adds a user as a collaborator on a project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: user_id
          required: true
          schema:
            type: string
          description: ID of the user to add as collaborator
      responses:
        '200':
          description: Collaborator added to project successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '401':
          description: Unauthorized
        '404':
          description: Project or user not found
        '500':
          description: Server error
    delete:
      summary: Remove a collaborator from a project
      description: Removes a user from project collaborators
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project
        - in: path
          name: user_id
          required: true
          schema:
            type: string
          description: ID of the user to remove as collaborator
      responses:
        '200':
          description: Collaborator removed from project successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          description: Cannot remove the project owner from collaborators
        '401':
          description: Unauthorized
        '404':
          description: Project or user not found
        '500':
          description: Server error
  /projects/{project_id}/collaborators:
    get:
      summary: Get project collaborators
      description: Retrieves all collaborators for a specific project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to get collaborators for
      responses:
        '200':
          description: Project collaborators retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
  /projects/{project_id}/panels:
    get:
      summary: Get project panels
      description: >-
        Retrieves detailed information about all panels associated with a
        project
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to get panels for
      responses:
        '200':
          description: Project panels retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Panel'
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
  /projects/{project_id}/refresh-status:
    post:
      summary: Refresh project status
      description: Updates a project's status based on current date and project dates
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to refresh status
      responses:
        '200':
          description: Project status refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - draft
                      - ongoing
                      - scheduled
                      - completed
                      - archived
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
  /projects/{project_id}/calculate-completion:
    post:
      summary: Calculate project completion
      description: >-
        Calculates the completion percentage of a project based on panel
        responses
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: project_id
          required: true
          schema:
            type: string
          description: ID of the project to calculate completion for
      responses:
        '200':
          description: Project completion calculated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  completion_percentage:
                    type: number
                    format: float
                    minimum: 0
                    maximum: 100
        '401':
          description: Unauthorized
        '404':
          description: Project not found
        '500':
          description: Server error
  /projects/sync/group/{group_id}:
    post:
      summary: Sync projects with group changes
      description: >-
        Updates projects associated with a group when group members or resources
        change
      tags:
        - Projects
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: group_id
          required: true
          schema:
            type: string
          description: ID of the group whose changes should be synced to projects
        - in: query
          name: refresh_status
          schema:
            type: boolean
            default: false
          description: Whether to refresh project statuses after syncing
      responses:
        '200':
          description: Projects synchronized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  group_id:
                    type: string
                  projects_updated:
                    type: integer
                  projects_refreshed:
                    type: integer
                  project_ids:
                    type: array
                    items:
                      type: string
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /questions:
    get:
      summary: Get all questions for user's organization
      description: Retrieves all questions belonging to the user's organization
      tags:
        - Questions
      security:
        - BearerAuth: []
      parameters:
        - in: query
          name: organization
          schema:
            type: string
          description: Optional organization ID to filter questions
        - in: query
          name: includeOptions
          schema:
            type: boolean
            default: false
          description: Whether to include detailed option data
      responses:
        '200':
          description: Questions retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Question'
        '401':
          description: Unauthorized
        '500':
          description: Server error
    post:
      summary: Create a new question with options
      description: Creates a new question with associated options
      tags:
        - Questions
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the question
                stepInstructions:
                  type: string
                  description: Instructions for the question
                stepType:
                  type: string
                  description: Type of question (e.g., multiple-choice, slider)
                required:
                  type: boolean
                  description: Whether the question is required
                options:
                  type: array
                  description: Array of option objects
                  items:
                    type: object
                    properties:
                      label:
                        type: string
                      instructions:
                        type: string
                      sliderLeft:
                        type: string
                      sliderCenter:
                        type: string
                      sliderRight:
                        type: string
              required:
                - name
                - stepInstructions
                - stepType
      responses:
        '201':
          description: Question created successfully
        '400':
          description: Invalid input data
        '401':
          description: Unauthorized
        '500':
          description: Server error
  /questions/{questionId}:
    get:
      summary: Get a specific question
      description: Retrieves a specific question by ID with optional reference hydration
      tags:
        - Questions
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: questionId
          required: true
          schema:
            type: string
          description: ID of the question to retrieve
        - in: query
          name: includeOptions
          schema:
            type: boolean
            default: true
          description: Whether to include detailed option data
      responses:
        '200':
          description: Question retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this question
        '404':
          description: Question not found
        '500':
          description: Server error
    put:
      summary: Update a question
      description: Updates basic properties of a question (name, instructions, required)
      tags:
        - Questions
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: questionId
          required: true
          schema:
            type: string
          description: ID of the question to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: New name for the question
                stepInstructions:
                  type: string
                  description: New instructions for the question
                required:
                  type: boolean
                  description: Whether the question is required
      responses:
        '200':
          description: Question updated successfully
        '400':
          description: Invalid input data
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this question
        '404':
          description: Question not found
        '500':
          description: Server error
    delete:
      summary: Delete a question
      description: Marks a question as deleted
      tags:
        - Questions
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: questionId
          required: true
          schema:
            type: string
          description: ID of the question to delete
      responses:
        '200':
          description: Question deleted successfully
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user cannot access this question
        '404':
          description: Question not found
        '500':
          description: Server error
  /responses:
    get:
      summary: Get user responses
      description: Retrieves all responses submitted by the current user
      tags:
        - Responses
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Responses retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Response'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have survey permission
        '500':
          description: Server error
    post:
      summary: Submit a response
      description: Submits a response to a question in a panel
      tags:
        - Responses
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - panel_id
                - question_id
                - data
              properties:
                panel_id:
                  type: string
                  description: ID of the panel
                question_id:
                  type: string
                  description: ID of the question being answered
                data:
                  type: object
                  description: Response data specific to the question type
      responses:
        '201':
          description: Response submitted successfully
          content:
            application/json:
              schema:
                type: object
                description: Updated panel progress
        '400':
          description: >-
            Bad request - panel already completed, panel not active, or invalid
            response data
        '401':
          description: Unauthorized
        '403':
          description: >-
            Forbidden - user cannot access this panel or does not have survey
            permission
        '500':
          description: Server error
  /responses/{panel_id}:
    get:
      summary: Get responses for a panel
      description: Retrieves all responses submitted by the user for a specific panel
      tags:
        - Responses
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel
      responses:
        '200':
          description: Responses retrieved successfully
          content:
            application/json:
              schema:
                type: object
                description: Response details
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have survey permission
        '500':
          description: Server error
  /responses/panel/{panel_id}/all:
    get:
      summary: Get all responses for a panel
      description: Retrieves all responses submitted for a specific panel, with user data
      tags:
        - Responses
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: panel_id
          required: true
          schema:
            type: string
          description: ID of the panel
      responses:
        '200':
          description: Responses retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                description: Array of responses with user data
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Server error
  /users:
    get:
      summary: Get users with insights role
      description: >-
        Retrieves all users with insights role from organizations accessible to
        the current user
      tags:
        - Users
      security:
        - BearerAuth: []
      responses:
        '200':
          description: List of users retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - user does not have required permissions
        '500':
          description: Server error
  /users/{id}:
    get:
      summary: Get user statistics
      description: Retrieves detailed statistics for a specific user
      tags:
        - Users
      security:
        - BearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: ID of the user to retrieve statistics for
      responses:
        '200':
          description: User statistics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
        '500':
          description: Server error
tags:
  - name: Projects
    description: Project management endpoints
