const { redisCache } = require("../config");

//Check if the user has permission to create a public panel middleware
const canCreatePublicPanel = async (req, res, next) => {
  const userId = req.user.uid;
  const user = await redisCache.getUser(userId);
  const panelData = req.body;
  console.log(user.roles, panelData.isPublic);
  if (panelData.isPublic) {
    if (user.roles.includes("admin")) {
      next();
    } else {
      res.status(403).send("User does not have access to create a public panel");
    }
  } else {
    next();
  }
  next();
};

module.exports = canCreatePublicPanel;
