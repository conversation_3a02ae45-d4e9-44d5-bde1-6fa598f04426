const express = require('express');
const router = express.Router();

const { authenticateUser, checkPermissions, paginationMiddleware } = require('../middleware');
const { ProjectService } = require('../services');
const { referenceHydrator } = require('../util');

// Apply authentication to all project routes
router.all('*', authenticateUser);

/**
 * @swagger
 * tags:
 *   name: Projects
 *   description: Project management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Project:
 *       type: object
 *       required:
 *         - name
 *         - owner
 *         - organization_id
 *       properties:
 *         project_id:
 *           type: string
 *           description: Unique identifier for the project
 *         name:
 *           type: string
 *           description: Name of the project
 *         description:
 *           type: string
 *           description: Description of the project
 *         owner:
 *           type: string
 *           description: User ID of the project owner
 *         organization_id:
 *           type: string
 *           description: Organization ID the project belongs to
 *         status:
 *           type: string
 *           enum: [draft, ongoing, scheduled, completed, archived]
 *           description: Current status of the project
 *         users:
 *           type: array
 *           items:
 *             type: string
 *           description: List of user IDs with access to the project
 *         collaborators:
 *           type: array
 *           items:
 *             type: string
 *           description: List of user IDs who can collaborate on the project
 *         start_date:
 *           type: string
 *           format: date-time
 *           description: Project start date
 *         end_date:
 *           type: string
 *           format: date-time
 *           description: Project end date
 *         panels:
 *           type: array
 *           items:
 *             type: string
 *           description: List of panel IDs associated with the project
 *         samples:
 *           type: array
 *           items:
 *             type: string
 *           description: List of sample/product IDs associated with the project
 */

/**
 * @swagger
 * /projects:
 *   get:
 *     summary: Get all projects
 *     description: Retrieves all projects accessible to the user, with optional reference hydration and pagination
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *       - in: query
 *         name: include_references
 *         schema:
 *           type: boolean
 *         description: Whether to include referenced entities
 *       - in: query
 *         name: full_details
 *         schema:
 *           type: boolean
 *         description: Whether to include full details of referenced entities
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of projects retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 projects:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Project'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', paginationMiddleware, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || req.userData.organizations[0];
    console.log("ProjectsOrganization: ", organization);
    // Handle reference hydration options
    const shouldHydrateReferences = req.query.include_references === 'true';
    const includeFullDetails = req.query.full_details === 'true';
    
    // Get projects with or without references
    let projects;
    if (shouldHydrateReferences) {
      projects = await ProjectService.getProjectsWithReferences(userId, organization, {
        includeFullDetails
      });
      console.log("Hydrated Projects: ", projects);
    } else {
      projects = await ProjectService.getProjects(userId, organization, req.userData.organizations);
      console.log("Unhydrated Projects: ", projects);
    }

    const start = req.pagination.startIndex;
    const end = start + req.pagination.limit;
    const paginatedProjects = projects.slice(start, end);
    console.log("Paginated Projects: ", paginatedProjects);
    res.status(200).send({
      projects: paginatedProjects,
      total: projects.length,
      currentPage: req.pagination.page,
      totalPages: Math.ceil(projects.length / req.pagination.limit),
      hasMore: (req.pagination.limit * req.pagination.page) < projects.length,
      limit: req.pagination.limit
    });
  } catch (error) {
    res.status(500).send('Error fetching projects: ' + error.message);
  }
});

/**
 * @swagger
 * /projects:
 *   post:
 *     summary: Create a project
 *     description: Creates a new project with the provided data
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - organization_id
 *             properties:
 *               name:
 *                 type: string
 *                 description: Project name
 *               description:
 *                 type: string
 *                 description: Project description
 *               organization_id:
 *                 type: string
 *                 description: Organization ID the project belongs to
 *               start_date:
 *                 type: string
 *                 format: date-time
 *                 description: Project start date
 *               end_date:
 *                 type: string
 *                 format: date-time
 *                 description: Project end date
 *               users:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Initial list of user IDs with access
 *               panels:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Initial list of panel IDs
 *               samples:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Initial list of sample/product IDs
 *     responses:
 *       201:
 *         description: Project created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const projectData = req.body;
    
    // Set organization_id from the user's primary organization if not provided
    if (!projectData.organization_id) {
      projectData.organization_id = req.userData.organizations[0];
    } else if (!req.userData.organizations.includes(projectData.organization_id)) {
      return res.status(400).send('User does not have access to this organization');
    }

    const newProject = await ProjectService.createProject(projectData, userId);

    res.status(201).send({
      project_id: newProject.project_id,
      ...newProject
    });
  } catch (error) {
    res.status(500).send('Error creating project: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/user:
 *   get:
 *     summary: Get user projects
 *     description: Retrieves all projects that the authenticated user has access to
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: include_references
 *         schema:
 *           type: boolean
 *         description: Whether to include referenced entities
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: User projects retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 projects:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Project'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/user', paginationMiddleware, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    try {
      const userId = req.user.uid;
      const projects = await ProjectService.getProjectsByUserId(userId);
      
      // Apply pagination
      const start = req.pagination.startIndex;
      const end = start + req.pagination.limit;
      const paginatedProjects = projects.slice(start, end);
      
      res.status(200).send({
        projects: paginatedProjects,
        currentPage: req.pagination.page,
        totalPages: Math.ceil(projects.length / req.pagination.limit),
        hasMore: req.pagination.hasMore
      });
    } catch (error) {
      res.status(500).send('Error fetching projects: ' + error.message);
    }
  });

/**
 * @swagger
 * /projects/counts/open:
 *   get:
 *     summary: Get open projects count
 *     description: Retrieves the count of ongoing and scheduled projects
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *     responses:
 *       200:
 *         description: Open projects count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/counts/open', checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || null;
    
    const count = await ProjectService.getOpenProjectsCount(userId, organization);
    
    res.status(200).send({
      count
    });
  } catch (error) {
    res.status(500).send('Error fetching open projects count: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/counts/completed:
 *   get:
 *     summary: Get completed projects count
 *     description: Retrieves the count of completed projects
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *     responses:
 *       200:
 *         description: Completed projects count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/counts/completed', checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || null;
    
    const count = await ProjectService.getCompletedProjectsCount(userId, organization);
    
    res.status(200).send({
      count
    });
  } catch (error) {
    res.status(500).send('Error fetching completed projects count: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/counts/ending-soon:
 *   get:
 *     summary: Get count of projects ending soon
 *     description: Retrieves the count of projects ending within a specified number of days
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: Number of days threshold for considering a project as ending soon
 *     responses:
 *       200:
 *         description: Projects ending soon count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/counts/ending-soon', checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || null;
    const daysThreshold = parseInt(req.query.days || '7', 10);
    
    const projects = await ProjectService.getProjectsEndingSoon(userId, organization, daysThreshold);
    
    res.status(200).send({
      count: projects.length
    });
  } catch (error) {
    res.status(500).send('Error fetching projects ending soon count: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/distribution/product-types:
 *   get:
 *     summary: Get product type distribution
 *     description: Retrieves the distribution of product types across all projects
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *     responses:
 *       200:
 *         description: Product type distribution retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   type:
 *                     type: string
 *                   count:
 *                     type: integer
 *                   percentage:
 *                     type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/distribution/product-types', checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || null;
    
    const distribution = await ProjectService.getProductTypeDistribution(userId, organization);
    
    res.status(200).send(distribution);
  } catch (error) {
    res.status(500).send('Error fetching product type distribution: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/dashboard-stats:
 *   get:
 *     summary: Get dashboard statistics
 *     description: Retrieves combined statistics for the dashboard including counts and distributions
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 openCount:
 *                   type: integer
 *                 completedCount:
 *                   type: integer
 *                 endingSoonCount:
 *                   type: integer
 *                 productDistribution:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                       count:
 *                         type: integer
 *                       percentage:
 *                         type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/dashboard-stats', checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.userData.uid;
    const organization = req.query.organization || req.userData.organizations[0];
    
    const stats = await ProjectService.getDashboardStats(userId, organization);
    
    res.status(200).send(stats);
  } catch (error) {
    res.status(500).send('Error fetching dashboard statistics: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/ending-soon:
 *   get:
 *     summary: Get projects ending soon
 *     description: Retrieves projects that are ending within a specified number of days
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter projects
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: Number of days threshold for considering a project as ending soon
 *       - in: query
 *         name: include_references
 *         schema:
 *           type: boolean
 *         description: Whether to include referenced entities
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Projects ending soon retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 projects:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Project'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/ending-soon', paginationMiddleware, checkPermissions(['insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || null;
    const daysThreshold = parseInt(req.query.days || '7', 10);
    
    // Handle reference hydration options
    const shouldHydrateReferences = req.query.include_references === 'true';
    
    // Get projects ending soon
    let projects = await ProjectService.getProjectsEndingSoon(userId, organization, daysThreshold);
    
    // Hydrate references if requested
    if (shouldHydrateReferences) {
      // Create a specialized reference map for projects ending soon
      const references = {
        "owner": {
          collection: "users",
          target: "owner_details",
          fields: ["first_name", "last_name", "uid"]
        },
        "organization_id": {
          collection: "organizations",
          fields: ["name", "organization_id"]
        }
      };
      
      projects = await Promise.all(
        projects.map(project => 
          referenceHydrator.hydrateDocument(project, references)
        )
      );
    }
    
    const start = req.pagination.startIndex;
    const end = start + req.pagination.limit;
    const paginatedProjects = projects.slice(start, end);
    
    res.status(200).send({
      projects: paginatedProjects,
      total: projects.length,
      page: req.pagination.page,
      limit: req.pagination.limit
    });
  } catch (error) {
    res.status(500).send('Error fetching projects ending soon: ' + error.message);
  }
});

/**
 * @swagger
 * /projects/{project_id}:
 *   get:
 *     summary: Get a project
 *     description: Retrieves a specific project by ID with optional reference hydration
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to retrieve
 *       - in: query
 *         name: include_references
 *         schema:
 *           type: boolean
 *         description: Whether to include referenced entities
 *     responses:
 *       200:
 *         description: Project retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.get('/:project_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    
    // Handle reference hydration options
    const shouldHydrateReferences = req.query.include_references === 'true';
    const includeFullDetails = req.query.full_details === 'true';
    
    // Get project with or without references
    let projectDetails;
    if (shouldHydrateReferences) {
      projectDetails = await ProjectService.getProjectWithReferences(projectId, {
        includeFullDetails
      });
    } else {
      projectDetails = await ProjectService.getProject(projectId);
    }

    res.status(200).send(projectDetails);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error fetching project details: ' + error.message);
    }
  }
});

/**
 * @swagger
 * /projects/{project_id}:
 *   put:
 *     summary: Update a project
 *     description: Updates an existing project with the provided data
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Project name
 *               description:
 *                 type: string
 *                 description: Project description
 *               start_date:
 *                 type: string
 *                 format: date-time
 *                 description: Project start date
 *               end_date:
 *                 type: string
 *                 format: date-time
 *                 description: Project end date
 *               status:
 *                 type: string
 *                 enum: [draft, ongoing, scheduled, completed, archived]
 *                 description: Project status
 *     responses:
 *       200:
 *         description: Project updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.put('/:project_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    const updateData = req.body;

    const updated = await ProjectService.updateProject(projectId, updateData);

    res.status(200).send({
      project_id: projectId,
      ...updated
    });
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error updating project: ' + error.message);
    }
  }
});

/**
 * @swagger
 * /projects/{project_id}:
 *   delete:
 *     summary: Delete a project
 *     description: Deletes an existing project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to delete
 *     responses:
 *       200:
 *         description: Project deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Project deleted successfully
 *                 project_id:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.delete('/:project_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    await ProjectService.deleteProject(projectId);
    
    res.status(200).send({ message: 'Project deleted successfully', project_id });
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error deleting project: ' + error.message);
    }
  }
});

/**
 * @swagger
 * /projects/{project_id}/panels/{panel_id}:
 *   post:
 *     summary: Add a panel to a project
 *     description: Associates a panel with a project. If the panel has an associated product/sample, that product is automatically added to the project as well.
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to add
 *     responses:
 *       200:
 *         description: Panel added to project successfully. If the panel has an associated product, it was also added to the project.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or panel not found
 *       500:
 *         description: Server error
 */
router.post('/:project_id/panels/:panel_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, panel_id } = req.params;
    const updated = await ProjectService.addPanelToProject(project_id, panel_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or panel not found');
    } else {
      res.status(500).send('Error adding panel to project: ' + error.message);
    }
  }
});

/**
 * @swagger
 * /projects/{project_id}/panels/{panel_id}:
 *   delete:
 *     summary: Remove a panel from a project
 *     description: Removes the association between a panel and a project. If the panel has an associated product/sample and no other panel in the project is using that sample, the sample will be automatically removed from the project as well.
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to remove
 *     responses:
 *       200:
 *         description: Panel removed from project successfully. If the panel's associated product/sample is not used by any other panel, it was also removed from the project.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or panel not found
 *       500:
 *         description: Server error
 */
router.delete('/:project_id/panels/:panel_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, panel_id } = req.params;
    const updated = await ProjectService.removePanelFromProject(project_id, panel_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or panel not found');
    } else {
      res.status(500).send('Error removing panel from project: ' + error.message);
    }
  }
});

/**
 * @swagger
 * /projects/{project_id}/users/{user_id}:
 *   post:
 *     summary: Add a user to a project
 *     description: Associates a user with a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to add
 *     responses:
 *       200:
 *         description: User added to project successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or user not found
 *       500:
 *         description: Server error
 */
router.post('/:project_id/users/:user_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, user_id } = req.params;
    const updated = await ProjectService.addUserToProject(project_id, user_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or user not found');
    } else {
      res.status(500).send('Error adding user to project: ' + error.message);
    }
  }
});

/**
 * @swagger
 * /projects/{project_id}/users/{user_id}:
 *   delete:
 *     summary: Remove a user from a project
 *     description: Removes the association between a user and a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to remove
 *     responses:
 *       200:
 *         description: User removed from project successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or user not found
 *       500:
 *         description: Server error
 */
router.delete('/:project_id/users/:user_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, user_id } = req.params;
    const updated = await ProjectService.removeUserFromProject(project_id, user_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or user not found');
    } else {
      res.status(500).send('Error removing user from project: ' + error.message);
    }
  }
});

// Add a sample/product to a project
/**
 * @swagger
 * /projects/{project_id}/samples/{sample_id}:
 *   post:
 *     summary: Add a sample to a project
 *     description: Associates a sample/product with a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: sample_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the sample/product to add
 *     responses:
 *       200:
 *         description: Sample added to project successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or sample not found
 *       500:
 *         description: Server error
 */
router.post('/:project_id/samples/:sample_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, sample_id } = req.params;
    const updated = await ProjectService.addSampleToProject(project_id, sample_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or sample not found');
    } else {
      res.status(500).send('Error adding sample to project: ' + error.message);
    }
  }
});

// Remove a sample/product from a project
/**
 * @swagger
 * /projects/{project_id}/samples/{sample_id}:
 *   delete:
 *     summary: Remove a sample from a project
 *     description: Removes the association between a sample/product and a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: sample_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the sample/product to remove
 *     responses:
 *       200:
 *         description: Sample removed from project successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or sample not found
 *       500:
 *         description: Server error
 */
router.delete('/:project_id/samples/:sample_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, sample_id } = req.params;
    const updated = await ProjectService.removeSampleFromProject(project_id, sample_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or sample not found');
    } else {
      res.status(500).send('Error removing sample from project: ' + error.message);
    }
  }
});

// Add a collaborator to a project
/**
 * @swagger
 * /projects/{project_id}/collaborators/{user_id}:
 *   post:
 *     summary: Add a collaborator to a project
 *     description: Adds a user as a collaborator on a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to add as collaborator
 *     responses:
 *       200:
 *         description: Collaborator added to project successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or user not found
 *       500:
 *         description: Server error
 */
router.post('/:project_id/collaborators/:user_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, user_id } = req.params;
    const updated = await ProjectService.addCollaboratorToProject(project_id, user_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or user not found');
    } else {
      res.status(500).send('Error adding collaborator to project: ' + error.message);
    }
  }
});

// Remove a collaborator from a project
/**
 * @swagger
 * /projects/{project_id}/collaborators/{user_id}:
 *   delete:
 *     summary: Remove a collaborator from a project
 *     description: Removes a user from project collaborators
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project
 *       - in: path
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to remove as collaborator
 *     responses:
 *       200:
 *         description: Collaborator removed from project successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       400:
 *         description: Cannot remove the project owner from collaborators
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project or user not found
 *       500:
 *         description: Server error
 */
router.delete('/:project_id/collaborators/:user_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { project_id, user_id } = req.params;
    const updated = await ProjectService.removeCollaboratorFromProject(project_id, user_id);
    
    res.status(200).send(updated);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project or user not found');
    } else if (error.message.includes('owner')) {
      res.status(400).send('Cannot remove the project owner from collaborators');
    } else {
      res.status(500).send('Error removing collaborator from project: ' + error.message);
    }
  }
});

// Get all collaborators for a project
/**
 * @swagger
 * /projects/{project_id}/collaborators:
 *   get:
 *     summary: Get project collaborators
 *     description: Retrieves all collaborators for a specific project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to get collaborators for
 *     responses:
 *       200:
 *         description: Project collaborators retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.get('/:project_id/collaborators', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    const collaborators = await ProjectService.getProjectCollaborators(projectId);
    
    res.status(200).send(collaborators);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error fetching project collaborators: ' + error.message);
    }
  }
});

// Get detailed panel information for a project
/**
 * @swagger
 * /projects/{project_id}/panels:
 *   get:
 *     summary: Get project panels
 *     description: Retrieves detailed information about all panels associated with a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to get panels for
 *     responses:
 *       200:
 *         description: Project panels retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Panel'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.get('/:project_id/panels', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    const panelDetails = await ProjectService.getProjectPanelDetails(projectId);
    
    res.status(200).send(panelDetails);
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error fetching project panel details: ' + error.message);
    }
  }
});

// Refresh project status based on dates
/**
 * @swagger
 * /projects/{project_id}/refresh-status:
 *   post:
 *     summary: Refresh project status
 *     description: Updates a project's status based on current date and project dates
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to refresh status
 *     responses:
 *       200:
 *         description: Project status refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [draft, ongoing, scheduled, completed, archived]
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.post('/:project_id/refresh-status', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    const newStatus = await ProjectService.refreshProjectStatus(projectId);
    
    res.status(200).send({ status: newStatus });
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error refreshing project status: ' + error.message);
    }
  }
});

// Calculate project completion percentage
/**
 * @swagger
 * /projects/{project_id}/calculate-completion:
 *   post:
 *     summary: Calculate project completion
 *     description: Calculates the completion percentage of a project based on panel responses
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: project_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the project to calculate completion for
 *     responses:
 *       200:
 *         description: Project completion calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 completion_percentage:
 *                   type: number
 *                   format: float
 *                   minimum: 0
 *                   maximum: 100
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.post('/:project_id/calculate-completion', checkPermissions(['insights']), async (req, res) => {
  try {
    const projectId = req.params.project_id;
    const percentage = await ProjectService.calculateProjectCompletionPercentage(projectId);
    
    res.status(200).send({ completion_percentage: percentage });
  } catch (error) {
    if (error.message.includes('not found')) {
      res.status(404).send('Project not found');
    } else {
      res.status(500).send('Error calculating project completion: ' + error.message);
    }
  }
});

// Sync projects with group changes
/**
 * @swagger
 * /projects/sync/group/{group_id}:
 *   post:
 *     summary: Sync projects with group changes
 *     description: Updates projects associated with a group when group members or resources change
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group whose changes should be synced to projects
 *       - in: query
 *         name: refresh_status
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to refresh project statuses after syncing
 *     responses:
 *       200:
 *         description: Projects synchronized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 group_id:
 *                   type: string
 *                 projects_updated:
 *                   type: integer
 *                 projects_refreshed:
 *                   type: integer
 *                 project_ids:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/sync/group/:group_id', checkPermissions(['insights']), async (req, res) => {
  try {
    const { group_id } = req.params;
    const refreshStatus = req.query.refresh_status === 'true';
    
    // First sync project access records
    const syncResult = await ProjectService.syncProjectsWithGroupChanges(group_id, {});
    
    // Then optionally refresh project statuses
    let refreshResult = { refreshed: 0 };
    if (refreshStatus && syncResult.updated > 0) {
      refreshResult = await ProjectService.refreshProjectsForGroup(group_id);
    }
    
    res.status(200).send({
      group_id,
      projects_updated: syncResult.updated,
      projects_refreshed: refreshResult.refreshed,
      project_ids: syncResult.projects
    });
  } catch (error) {
    res.status(500).send('Error syncing projects with group changes: ' + error.message);
  }
});

module.exports = router; 