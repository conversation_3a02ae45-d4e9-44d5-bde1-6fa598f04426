/**
 * Test script to validate atomic operations and caching behavior
 * 
 * This script tests the atomic operations for projects to ensure that 
 * caching works correctly with operations like arrayUnion and arrayRemove.
 */

// Import required modules
const { ProjectRepository } = require('../src/repositories');
const { firestoreModule, redisCache } = require('../src/config');

// Create a test project ID (use an existing project from your database)
const TEST_PROJECT_ID = process.env.TEST_PROJECT_ID || 'replace_with_test_project_id';
const TEST_USER_ID = process.env.TEST_USER_ID || 'replace_with_test_user_id';
const TEST_PANEL_ID = process.env.TEST_PANEL_ID || 'replace_with_test_panel_id';

// Initialize repository
const projectRepo = new ProjectRepository();

async function runTests() {
  try {
    console.log('Starting atomic operations tests...');
    
    // Test 1: Check initial state
    console.log('\n=== Test 1: Check initial project state ===');
    const initialProject = await projectRepo.read(TEST_PROJECT_ID);
    console.log('Initial project state:', JSON.stringify({
      assigned_panels: initialProject.assigned_panels,
      assigned_users: initialProject.assigned_users,
      collaborators: initialProject.collaborators
    }, null, 2));
    
    // Test 2: Atomic add panel
    console.log('\n=== Test 2: Atomic add panel ===');
    // Clear cache first
    await redisCache.deleteProjectData(TEST_PROJECT_ID);
    console.log('Cache cleared before operation');
    
    // Add panel atomically
    const withPanel = await projectRepo.addPanelAtomically(TEST_PROJECT_ID, TEST_PANEL_ID);
    console.log('Project after adding panel:', JSON.stringify({
      assigned_panels: withPanel.assigned_panels
    }, null, 2));
    
    // Check cache to make sure it reflects the update
    const cachedAfterAdd = await redisCache.getProjectData(TEST_PROJECT_ID);
    console.log('Cached project after adding panel:', JSON.stringify({
      assigned_panels: cachedAfterAdd?.assigned_panels || []
    }, null, 2));
    
    // Test 3: Atomic remove panel
    console.log('\n=== Test 3: Atomic remove panel ===');
    const withoutPanel = await projectRepo.removePanelAtomically(TEST_PROJECT_ID, TEST_PANEL_ID);
    console.log('Project after removing panel:', JSON.stringify({
      assigned_panels: withoutPanel.assigned_panels
    }, null, 2));
    
    // Check cache to make sure it reflects the update
    const cachedAfterRemove = await redisCache.getProjectData(TEST_PROJECT_ID);
    console.log('Cached project after removing panel:', JSON.stringify({
      assigned_panels: cachedAfterRemove?.assigned_panels || []
    }, null, 2));
    
    // Test 4: Concurrent operations (simulated)
    console.log('\n=== Test 4: Simulate concurrent operations ===');
    
    // Operation 1: Add a panel
    console.log('Starting concurrent operation 1: Add panel');
    const operation1Promise = projectRepo.addPanelAtomically(TEST_PROJECT_ID, TEST_PANEL_ID);
    
    // Operation 2: Add a user (slightly delayed to simulate concurrency)
    console.log('Starting concurrent operation 2: Add user');
    const operation2Promise = new Promise(resolve => {
      setTimeout(async () => {
        const result = await projectRepo.addUserAtomically(TEST_PROJECT_ID, TEST_USER_ID);
        resolve(result);
      }, 100);
    });
    
    // Wait for both operations to complete
    const [afterOp1, afterOp2] = await Promise.all([operation1Promise, operation2Promise]);
    
    console.log('Final project state after concurrent operations:', JSON.stringify({
      assigned_panels: afterOp2.assigned_panels,
      assigned_users: afterOp2.assigned_users
    }, null, 2));
    
    // Check cache one more time
    const finalCachedState = await redisCache.getProjectData(TEST_PROJECT_ID);
    console.log('Final cached state:', JSON.stringify({
      assigned_panels: finalCachedState?.assigned_panels || [],
      assigned_users: finalCachedState?.assigned_users || []
    }, null, 2));
    
    console.log('\nAll tests completed successfully!');
    
    // Cleanup: Reset test project to initial state
    console.log('\n=== Cleaning up: Resetting project to initial state ===');
    await projectRepo.update(TEST_PROJECT_ID, {
      assigned_panels: initialProject.assigned_panels,
      assigned_users: initialProject.assigned_users,
      collaborators: initialProject.collaborators,
      updated_at: new Date()
    });
    console.log('Project reset to initial state.');
    
  } catch (error) {
    console.error('Error running tests:', error.message);
    process.exit(1);
  }
}

// Run the tests
runTests().then(() => {
  console.log('Test script completed.');
  process.exit(0);
}); 