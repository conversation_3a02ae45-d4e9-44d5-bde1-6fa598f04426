const Joi = require('joi');

// Define packaging schema using <PERSON><PERSON> for robust validation
const packagingSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the packaging option'),
    
  type: Joi.string()
    .required()
    .description('Type of the packaging option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the packaging option'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the packaging option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake packaging document for testing
const generateFakePackaging = (overrides = {}) => {
  const fakePackaging = {
    name: faker.lorem.word(),
    type: faker.helpers.arrayElement(['bottle', 'can', 'box', 'pouch', 'jar', 'wrapper']),
    priority: faker.number.int({min: 1, max: 100}),
    option_id: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validatePackaging(fakePackaging);
  if (error) {
    throw new Error(`Generated invalid packaging: ${error.message}`);
  }

  return fakePackaging;
};

// Generate multiple fake packagings
const generateFakePackagings = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakePackaging(overrides));
};

// Validate packaging document against schema
const validatePackaging = (packaging) => {
  return packagingSchema.validate(packaging, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to packaging model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validatePackaging(data);
  
  if (error) {
    throw new Error(`Invalid packaging data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert packaging model to Firestore document
const toFirestore = (packaging) => {
  const { value, error } = validatePackaging(packaging);
  
  if (error) {
    throw new Error(`Invalid packaging data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  packagingSchema,
  generateFakePackaging,
  generateFakePackagings,
  validatePackaging,
  fromFirestore,
  toFirestore
};
