# API Documentation Guide

This guide explains how to maintain and update our Swagger/OpenAPI documentation for the Sensory Analysis Panel Management API.

## Quick Start

```bash
# Generate or update Swagger documentation files
npm run generate:swagger

# Validate that all endpoints are documented
npm run validate:swagger

# Do both steps in one command
npm run docs
```

## Documentation Workflow

1. Add JSDoc annotations to your code (routes and models)
2. Run `npm run docs` to generate and validate documentation
3. Fix any undocumented routes or models
4. View the documentation at `/api-docs` endpoint

## How to Document Endpoints

Add JSDoc comments above your route handler functions like this:

```javascript
/**
 * @swagger
 * /your-path:
 *   get:
 *     summary: Short description of what this endpoint does
 *     description: Longer description with more details
 *     tags: [CategoryName]
 *     security:
 *       - BearerAuth: []     # If endpoint requires authentication
 *     parameters:
 *       - in: path
 *         name: parameter_name
 *         required: true
 *         schema:
 *           type: string
 *         description: Description of the parameter
 *       - in: query
 *         name: query_param
 *         schema:
 *           type: string
 *         description: Description of the query parameter
 *     responses:
 *       200:
 *         description: Success response description
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/YourModel'    # Reference to a schema
 *       400:
 *         description: Bad request description
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/your-path', async (req, res) => {
  // Route implementation
});
```

## How to Document Models

Add JSDoc comments at the top of your model files:

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     YourModel:
 *       type: object
 *       required:
 *         - id
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: Display name
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 */
```

## Common Types

- **Strings**: `type: string`
- **Numbers**: `type: number` or `type: integer`
- **Booleans**: `type: boolean`
- **Arrays**: `type: array` with `items` property
- **Objects**: `type: object` with `properties`
- **Dates**: `type: string, format: date-time`

## Best Practices

1. **Consistency**: Maintain consistent documentation across all endpoints
2. **Completeness**: Document all parameters, request bodies, and response types
3. **Validation**: Run `npm run validate:swagger` to check for undocumented routes
4. **Organization**: Use tags to group related endpoints
5. **Updates**: Keep documentation in sync with code changes
6. **Examples**: Add example values where helpful

## Request Body Documentation

For endpoints that accept request bodies:

```javascript
/**
 * @swagger
 * /resource:
 *   post:
 *     summary: Create a new resource
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/YourModel'
 *     responses:
 *       201:
 *         description: Resource created successfully
 */
```

## Authentication

Most endpoints require authentication. Add this to secured endpoints:

```javascript
/**
 * @swagger
 * /secure-endpoint:
 *   get:
 *     security:
 *       - BearerAuth: []
 *     # Rest of documentation...
 */
```

## Common Response Status Codes

- **200**: Success (GET, PUT)
- **201**: Created (POST)
- **204**: No Content (DELETE)
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **500**: Server Error 