const Joi = require('joi');

// Define effect schema using <PERSON><PERSON> for robust validation
const effectSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the effect option'),
    
  type: Joi.string()
    .valid('positive', 'negative')
    .required()
    .description('Type of the effect option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the effect'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the effect option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake effect document for testing
const generateFakeEffect = (overrides = {}) => {
  const fakeEffect = {
    name: faker.lorem.word(),
    type: faker.helpers.arrayElement(['positive', 'negative']),
    priority: faker.number.int({min: 1, max: 100}),
    option_id: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateEffect(fakeEffect);
  if (error) {
    throw new Error(`Generated invalid effect: ${error.message}`);
  }

  return fakeEffect;
};

// Generate multiple fake effects
const generateFakeEffects = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeEffect(overrides));
};

// Validate effect document against schema
const validateEffect = (effect) => {
  return effectSchema.validate(effect, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to effect model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateEffect(data);
  
  if (error) {
    throw new Error(`Invalid effect data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert effect model to Firestore document
const toFirestore = (effect) => {
  const { value, error } = validateEffect(effect);
  
  if (error) {
    throw new Error(`Invalid effect data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  effectSchema,
  generateFakeEffect,
  generateFakeEffects,
  validateEffect,
  fromFirestore,
  toFirestore
};
