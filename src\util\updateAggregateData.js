const { redisCache, firestoreModule, firebaseListeners } = require('../config');

const updateAggregateData = async (panel_id, product_id, progress, user) => {
    let updates = {
        count: firestoreModule.FieldValue.increment(1)
    };
    for(let question_id of ["a9k1bpXX4H57Bdl7XhaX", "wjA1uhqbIMFuZwAaCoJ8"]){
        let question_progress = progress[question_id];
        Object.entries(question_progress).forEach(([key, value]) => {
            updates[`${key}_count`] = firestoreModule.FieldValue.increment(1);
            updates[`${key}_sum`] = firestoreModule.FieldValue.increment(value);
            updates[`${key}_sum_squares`] = firestoreModule.FieldValue.increment(value * value);//This will allow us to calculate the variance later, and thus the standard deviation
        });
    }

    if(user.demographics){
        Object.entries(user.demographics).forEach(([demographic, key]) => {
            updates[`${key}_count`] = firestoreModule.FieldValue.increment(1);
        });
    }

    console.log("Updates: ", updates);

    if(Object.keys(updates).length > 0){
        await firestoreModule.updateDocument(`aggregates`, panel_id, updates);
        await redisCache.setAnyData(`aggregate:${panel_id}`, updates);
        await firestoreModule.updateDocument(`aggregates`, product_id, updates);
        await redisCache.setAnyData(`aggregate:${product_id}`, updates);
    }
}

module.exports = updateAggregateData;