const { redisCache } = require('../config');


const hydrateProduct = async (productId, organizations) => {
  let product = await redisCache.getProductData(productId);
  if(!organizations[product.organization_id]){
    organizations[product.organization_id] = await redisCache.getAnyData('organizations', product.organization_id);
  }
  product.organization = organizations[product.organization_id];
  return product;
}

module.exports = hydrateProduct;