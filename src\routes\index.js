const express = require('express');
const router = express.Router();

const authRouter = require('./auth');
const profileRouter = require('./profile');
const organizationsRouter = require('./organizations');
const productsRouter = require('./products');
const constantsRouter = require('./constants');
const panelRouter = require('./panels');
const responsesRouter = require('./responses');
const searchRouter = require('./search');
const docsRouter = require('./docs');
const insightsRouter = require('./insights');
const integrationsRouter = require('./integrations');
const usersRouter = require('./users');
const projectsRouter = require('./projects');
const groupsRouter = require('./groups');
const templatesRouter = require('./panel-templates');
const questionsRouter = require('./questions');

router.use('/auth', authRouter);
router.use('/profile', profileRouter);
router.use('/organizations', organizationsRouter);
router.use('/products', productsRouter);
router.use('/panels', panelRouter);
router.use('/responses', responsesRouter);
router.use('/api-docs', docsRouter);
router.use('/insights', insightsRouter);
router.use('/integrations', integrationsRouter);
router.use('/constants', constantsRouter);
router.use('/users', usersRouter);
router.use('/projects', projectsRouter);
router.use('/groups', groupsRouter);
router.use('/search', searchRouter);
router.use('/users', usersRouter);
router.use('/templates', templatesRouter);
router.use('/questions', questionsRouter);

module.exports = router;