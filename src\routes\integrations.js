const express = require('express');
const router = express.Router();
const { redisCache, firestoreModule, firebaseListeners } = require('../config');
const { authenticateApiUser, canAccessProductDetails } = require('../middleware');
router.all('*', authenticateApiUser);

/**
 * @swagger
 * /integrations/products/{product_id}:
 *   get:
 *     summary: Get product data
 *     description: Retrieves aggregated data for a product by its external ID
 *     tags: [Integrations]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: External ID of the product
 *     responses:
 *       200:
 *         description: Product data retrieved successfully or product not found message
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   description: Aggregated product data
 *                 - type: object
 *                   properties:
 *                     error:
 *                       type: boolean
 *                     message:
 *                       type: string
 *       401:
 *         description: Unauthorized - invalid API key
 *       500:
 *         description: Server error
 */
router.get('/products/:product_id', async (req, res) => {
    try {
        const productId = req.params.product_id;
        //TODO: Check firestore for a product with an external_id that matches the productId, otherwise just ask redis for the product data
        const product = await firestoreModule.getProductByExternalId(productId);
        console.log(product, productId);
        if (product.length > 0) {
            const productData = await redisCache.getAnyData("aggregates", product[0].id);
            res.status(200).send(productData);
        } else {
            // const productData = await redisCache.getAnyData("aggregates", productId);
            res.status(200).send({"error": true, "message": "Product not found"});
        }
    } catch (error) {
        res.status(500).send('Error fetching product data: ' + error.message);
    }
});

/**
 * @swagger
 * /integrations/constants/{type}:
 *   get:
 *     summary: Get constants by type
 *     description: Retrieves constants of a specific type for use in integrations
 *     tags: [Integrations]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *         description: Type of constants to retrieve (e.g., demographics, flavors, effects)
 *     responses:
 *       200:
 *         description: Constants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *       401:
 *         description: Unauthorized - invalid API key
 */
router.get("/constants/:type", (req, res) => {
    return res.status(200).send(firebaseListeners.getConstants(req.params.type));
});

/**
 * @swagger
 * /integrations:
 *   get:
 *     summary: Check integration API status
 *     description: Simple endpoint to verify the integration API is working
 *     tags: [Integrations]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: API is working
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "Hello World"
 *       401:
 *         description: Unauthorized - invalid API key
 */
router.get('/', async (req, res) => {
    res.status(200).send('Hello World');
});

module.exports = router;