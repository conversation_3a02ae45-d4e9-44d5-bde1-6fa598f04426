const hydrateTemplate = require("./hydrateTemplate");

const paginateTemplates = async (templates, page, limit, startIndex)=>{
    const hydratedTemplates = await Promise.all(templates.map(async (template) => hydrateTemplate(template)));

    const paginatedTemplates = hydratedTemplates.slice(startIndex, startIndex + limit);
    const totalTemplates = templates.length;
    const totalPages = Math.ceil(totalTemplates / limit);
    const hasMore = page < totalPages;
    return { templates: paginatedTemplates, currentPage: page, totalPages, hasMore };
}
module.exports = paginateTemplates;