const { program } = require('commander');
const inquirer = require('inquirer').default;
const dotenv = require('dotenv');
dotenv.config();
const { firestoreModule, redisCache } = require('../config');
const { formatUsersTable, formatUserDetails } = require('../models/user');
const { formatPanelsTable, formatPanelDetails } = require('../models/panel');
const transferPanel = require('./transferPanel');
const transferProduct = require('./transferProduct');
const { UserService } = require('../services');

async function mainMenu() {
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an operation:',
      choices: [
        'Add User to Organization',
        'Delete Panel',
        'Delete Product',
        'Clear Organization Cache',
        'Flush Redis Cache',
        'Mark Panel Private',
        'Transfer Panel',
        'Transfer Product',
        'Bulk Transfer Panels',
        'View Panel Details',
        'View Product Details',
        'View Organization Users',
        'View Public Panels',
        'View Panel Response Users',
        'Exit'
      ]
    }
  ]);

  switch (action) {
    case 'Add User to Organization':
      await handleAddUserToOrganization();
      break;
    case 'Delete Panel':
      await handlePanelDeletion();
      break;
    case 'Delete Product':
      await handleProductDeletion();
      break;
    case 'Clear Organization Cache':
      await handleOrgCacheClear();
      break;
    case 'Flush Redis Cache':
      await handleRedisCacheFlush();
      break;
    case 'Mark Panel Private':
      await handleMarkPanelPrivate();
      break;
    case 'Transfer Panel':
      await handleTransferPanel();
      break;
    case 'Transfer Product':
      await handleTransferProduct();
      break;
    case 'Bulk Transfer Panels':
      await handleBulkTransferPanels();
      break;
    case 'View Panel Details':
      await handleViewPanelDetails();
      break;
    case 'View Product Details':
      await handleViewProductDetails();
      break;
    case 'View Organization Users':
      await handleViewOrganizationUsers();
      break;
    case 'View Public Panels':
      await handleViewPublicPanels();
      break;
    case 'View Panel Response Users':
      await handleViewPanelResponseUsers();
      break;
    case 'Exit':
      console.log('Goodbye!');
      process.exit(0);
  }

  // Return to main menu
  await mainMenu();
}

async function handlePanelDeletion() {
  const { panelId, confirm } = await inquirer.prompt([
    {
      type: 'input',
      name: 'panelId',
      message: 'Enter Panel ID to delete:'
    },
    {
      type: 'confirm',
      name: 'confirm',
      message: 'Are you sure you want to delete this panel?',
      default: false
    }
  ]);

  if (confirm) {
    try {
      // Get panel data first to show what's being deleted
      const panelData = await redisCache.getPanelData(panelId);
      console.log('Deleting panel:', {
        id: panelId,
        organization: panelData?.organization_id,
        title: panelData?.title
      });

      await firestoreModule.markDeleted(panelId, 'panels');
      await redisCache.deletePanel(panelId);
      console.log(`Successfully deleted panel ${panelId}`);
    } catch (error) {
      console.error('Error deleting panel:', error);
    }
  }
}

async function handleProductDeletion() {
  const { productId, confirm } = await inquirer.prompt([
    {
      type: 'input',
      name: 'productId',
      message: 'Enter Product ID to delete:'
    },
    {
      type: 'confirm',
      name: 'confirm',
      message: 'Are you sure you want to delete this product?',
      default: false
    }
  ]);

  if (confirm) {
    try {
      // Get product data first to show what's being deleted
      const productData = await redisCache.getProductData(productId);
      console.log('Deleting product:', {
        id: productId,
        organization: productData?.organization_id,
        name: productData?.name
      });

      await firestoreModule.markDeleted(productId, 'products');
      await redisCache.deleteProduct(productId);
      console.log(`Successfully deleted product ${productId}`);
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  }
}

async function handleOrgCacheClear() {
  const { orgId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'orgId',
      message: 'Enter Organization ID to clear cache:'
    }
  ]);

  try {
    await redisCache.deleteOrganizationCache(orgId);
    console.log(`Successfully cleared cache for organization ${orgId}`);
  } catch (error) {
    console.error('Error clearing organization cache:', error);
  }
}

async function handleRedisCacheFlush() {
  const { type } = await inquirer.prompt([
    {
      type: 'list',
      name: 'type',
      message: 'Select cache type to flush:',
      choices: ['all', 'userPanels', 'panels']
    }
  ]);

  try {
    await redisCache.flushCachedData(type);
    console.log(`Successfully flushed ${type} cache`);
  } catch (error) {
    console.error('Error flushing cache:', error);
  }
}

async function handleMarkPanelPrivate() {
  const { panelId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'panelId',
      message: 'Enter Panel ID to mark private:'
    }
  ]);

  try {
    const panel = await redisCache.getPanelData(panelId);
    if (!panel) {
      console.log(`Panel ${panelId} not found`);
      return;
    }

    console.log('\nPanel Details:');
    console.log(formatPanelDetails(panel));

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Are you sure you want to make this panel private?',
        default: false
      }
    ]);

    if (confirm) {
      await firestoreModule.updateDocument('panels', panelId, { isPublic: false });
      await redisCache.deletePanel(panelId);
      console.log(`Successfully marked panel ${panelId} as private`);
    }
  } catch (error) {
    console.error('Error marking panel as private:', error);
  }
}

async function handleTransferPanel() {
  const { panelId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'panelId',
      message: 'Enter Panel ID to transfer:'
    }
  ]);

  try {
    const panel = await redisCache.getPanelData(panelId);
    if (!panel) {
      console.log(`Panel ${panelId} not found`);
      return;
    }

    console.log('\nPanel Details:');
    console.log(formatPanelDetails(panel));
    console.log(`Current Organization ID: ${panel.organization_id}`);

    const { newOrganizationId } = await inquirer.prompt([
      {
        type: 'input',
        name: 'newOrganizationId',
        message: 'Enter new Organization ID to transfer panel to:'
      }
    ]);

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to transfer this panel from ${panel.organization_id} to ${newOrganizationId}?`,
        default: false
      }
    ]);

    if (confirm) {
      await transferPanel(panelId, newOrganizationId);
      await redisCache.deletePanel(panelId); // Clear the cache for this panel
      await redisCache.deleteOrganizationCache(panel.organization_id); // Clear old org cache
      await redisCache.deleteOrganizationCache(newOrganizationId); // Clear new org cache
      console.log(`Successfully transferred panel ${panelId} to organization ${newOrganizationId}`);
    }
  } catch (error) {
    console.error('Error transferring panel:', error);
  }
}

async function handleTransferProduct() {
  const { productId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'productId',
      message: 'Enter Product ID to transfer:'
    }
  ]);

  try {
    const product = await redisCache.getProductData(productId);
    if (!product) {
      console.log(`Product ${productId} not found`);
      return;
    }

    console.log('\nProduct Details:');
    console.log(JSON.stringify(product, null, 2));
    console.log(`Current Organization ID: ${product.organization_id}`);

    const { newOrganizationId } = await inquirer.prompt([
      {
        type: 'input',
        name: 'newOrganizationId',
        message: 'Enter new Organization ID to transfer product to:'
      }
    ]);

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to transfer this product from ${product.organization_id} to ${newOrganizationId}?`,
        default: false
      }
    ]);

    if (confirm) {
      await transferProduct(productId, newOrganizationId);
      await redisCache.deleteProduct(productId); // Clear the cache for this product
      await redisCache.deleteOrganizationCache(product.organization_id); // Clear old org cache
      await redisCache.deleteOrganizationCache(newOrganizationId); // Clear new org cache
      console.log(`Successfully transferred product ${productId} to organization ${newOrganizationId}`);
    }
  } catch (error) {
    console.error('Error transferring product:', error);
  }
}

async function handleViewPanelDetails() {
  const { panelId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'panelId',
      message: 'Enter Panel ID to view:'
    }
  ]);

  try {
    const panel = await redisCache.getPanelData(panelId);
    console.log(JSON.stringify(panel, null, 2));
  } catch (error) {
    console.error('Error viewing panel details:', error);
  }
}

async function handleViewProductDetails() {
  const { productId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'productId',
      message: 'Enter Product ID to view:'
    }
  ]);

  try {
    const product = await redisCache.getProductData(productId);
    console.log(JSON.stringify(product, null, 2));
  } catch (error) {
    console.error('Error viewing product details:', error);
  }
}

async function handleViewOrganizationUsers() {
  const { organizationId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'organizationId',
      message: 'Enter Organization ID to view users:'
    }
  ]);

  try {
    const users = await UserService.getUsersByOrganization(organizationId);
    if (users.length === 0) {
      console.log(`No users found for organization ${organizationId}`);
      return;
    }

    console.log(`Found ${users.length} users in organization ${organizationId}:`);
    console.log(formatUsersTable(users));

    // Option to view detailed information for a specific user
    const { viewDetails } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'viewDetails',
        message: 'Would you like to view detailed information for a specific user?',
        default: false
      }
    ]);

    if (viewDetails) {
      const { userEmail } = await inquirer.prompt([
        {
          type: 'list',
          name: 'userEmail',
          message: 'Select a user to view details:',
          choices: users.map(user => user.email)
        }
      ]);

      const selectedUser = users.find(user => user.email === userEmail);
      console.log('\nDetailed User Information:');
      console.log(formatUserDetails(selectedUser));
    }
  } catch (error) {
    console.error('Error viewing organization users:', error);
  }
}

async function handleViewPublicPanels() {
  try {
    const publicPanels = await firestoreModule.getDocumentsByQuery('panels', [
      ['isPublic', '==', true]
    ]);

    if (publicPanels.length === 0) {
      console.log('No public panels found');
      return;
    }

    console.log(`Found ${publicPanels.length} public panels:`);
    console.log(formatPanelsTable(publicPanels));

    const { makePrivate } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'makePrivate',
        message: 'Would you like to make any of these panels private?',
        default: false
      }
    ]);

    if (makePrivate) {
      const { selectedPanels } = await inquirer.prompt([
        {
          type: 'checkbox',
          name: 'selectedPanels',
          message: 'Select panels to make private:',
          choices: publicPanels.map(panel => ({
            name: `${panel.name} (${panel.organization_id})`,
            value: panel.id
          }))
        }
      ]);

      if (selectedPanels.length > 0) {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: `Are you sure you want to make ${selectedPanels.length} panel(s) private?`,
            default: false
          }
        ]);

        if (confirm) {
          for (const panelId of selectedPanels) {
            try {
              await firestoreModule.updateDocument('panels', panelId, { isPublic: false });
              await redisCache.deletePanel(panelId);
              console.log(`Successfully made panel ${panelId} private`);
            } catch (error) {
              console.error(`Error making panel ${panelId} private:`, error);
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Error viewing public panels:', error);
  }
}

async function handleViewPanelResponseUsers() {
  const { panelId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'panelId',
      message: 'Enter Panel ID to view respondents:'
    }
  ]);

  try {
    // First get panel details to show what we're looking at
    const panel = await redisCache.getPanelData(panelId);
    if (!panel) {
      console.log(`Panel ${panelId} not found`);
      return;
    }

    console.log('\nPanel Details:');
    console.log(formatPanelDetails(panel));

    const users = await firestoreModule.getPanelResponseUsers(panelId);
    if (users.length === 0) {
      console.log('\nNo users have responded to this panel yet');
      return;
    }

    console.log(`\nFound ${users.length} users who responded to this panel:`);
    console.log(formatUsersTable(users));

    // Option to view detailed information for a specific user
    const { viewDetails } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'viewDetails',
        message: 'Would you like to view detailed information for a specific user?',
        default: false
      }
    ]);

    if (viewDetails) {
      const { userEmail } = await inquirer.prompt([
        {
          type: 'list',
          name: 'userEmail',
          message: 'Select a user to view details:',
          choices: users.map(user => user.email)
        }
      ]);

      const selectedUser = users.find(user => user.email === userEmail);
      console.log('\nDetailed User Information:');
      console.log(formatUserDetails(selectedUser));
    }
  } catch (error) {
    console.error('Error viewing panel response users:', error);
  }
}

async function handleAddUserToOrganization() {
  const { userId, organizationId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'userId',
      message: 'Enter User ID to add to organization:'
    },
    {
      type: 'input',
      name: 'organizationId',
      message: 'Enter Organization ID to add user to:'
    }
  ]);

  try {
    // Get user data first to show what's being modified
    const userData = await UserService.getUserById(userId);
    if (!userData) {
      console.log(`User ${userId} not found`);
      return;
    }

    console.log('\nUser Details:');
    console.log(formatUserDetails(userData));

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to add this user to organization ${organizationId}?`,
        default: false
      }
    ]);

    if (confirm) {
      const result = await UserService.addUserToOrganization(userId, organizationId);
      if(result.success){
        console.log(`Successfully added user ${userId} to organization ${organizationId}`);
        console.log(result);
        // Clear relevant caches
        // await redisCache.deleteOrganizationCache(organizationId);
        // console.log(`Cleared cache for organization ${organizationId}`);
      } else {
        console.error(`Error adding user ${userId} to organization ${organizationId}:`, result);
      }
    }
  } catch (error) {
    console.error('Error adding user to organization:', error);
  }
}

async function handleBulkTransferPanels() {
  // Ask for source organization ID
  const { sourceOrgId } = await inquirer.prompt([
    {
      type: 'input',
      name: 'sourceOrgId',
      message: 'Enter source Organization ID to list panels from:'
    }
  ]);

  try {
    // Get all organizations for later selection
    const allOrganizations = await firestoreModule.getCollection('organizations');
    
    // Get all panels from the source organization
    const orgPanels = await firestoreModule.getDocumentsByQuery('panels', [
      ['organization_id', '==', sourceOrgId],
      ['deleted', '==', false]
    ]);

    if (orgPanels.length === 0) {
      console.log(`No panels found for organization ${sourceOrgId}`);
      return;
    }

    console.log(`\nFound ${orgPanels.length} panels in organization ${sourceOrgId}`);
    
    // Display panels and allow multi-selection
    const { selectedPanels } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'selectedPanels',
        message: 'Select panels to transfer:',
        choices: orgPanels.map(panel => ({
          name: `${panel.name || 'Untitled'} (ID: ${panel.id})`,
          value: panel.id
        }))
      }
    ]);

    if (selectedPanels.length === 0) {
      console.log('No panels selected for transfer');
      return;
    }

    // Ask for destination organization
    const { destinationOrgId } = await inquirer.prompt([
      {
        type: 'list',
        name: 'destinationOrgId',
        message: 'Select destination organization:',
        choices: allOrganizations.map(org => ({
          name: `${org.name} (ID: ${org.id})`,
          value: org.id
        }))
      }
    ]);

    // Confirm the transfer
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to transfer ${selectedPanels.length} panel(s) from ${sourceOrgId} to ${destinationOrgId}?`,
        default: false
      }
    ]);

    if (confirm) {
      console.log(`Starting transfer of ${selectedPanels.length} panel(s)...`);
      
      // Track results
      const results = {
        success: [],
        failed: []
      };

      // Transfer each panel
      for (const panelId of selectedPanels) {
        try {
          await transferPanel(panelId, destinationOrgId);
          results.success.push(panelId);
          console.log(`✓ Successfully transferred panel ${panelId}`);
        } catch (error) {
          results.failed.push({ id: panelId, error: error.message });
          console.error(`✗ Failed to transfer panel ${panelId}: ${error.message}`);
        }
      }

      // Summary
      console.log('\nTransfer Summary:');
      console.log(`Successfully transferred: ${results.success.length} panel(s)`);
      console.log(`Failed transfers: ${results.failed.length} panel(s)`);
      
      if (results.failed.length > 0) {
        console.log('\nFailed panels:');
        results.failed.forEach(failure => {
          console.log(`- Panel ${failure.id}: ${failure.error}`);
        });
      }

      // Clear affected organization caches
      await redisCache.deleteOrganizationCache(sourceOrgId);
      await redisCache.deleteOrganizationCache(destinationOrgId);
      console.log('\nOrganization caches cleared');
    }
  } catch (error) {
    console.error('Error performing bulk panel transfer:', error);
  }
}

program
  .name('incident-response')
  .description('CLI tool for database incident response')
  .version('1.0.0');

program
  .command('interactive')
  .description('Start interactive menu')
  .action(async () => {
    console.log('Starting incident response tool...');
    await mainMenu();
  });

program.parse();
