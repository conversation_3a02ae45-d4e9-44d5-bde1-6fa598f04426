const { firebaseListeners } = require('../config');

const validateResponseKeys = (data, questionID) => {
    console.log("Validating keys for question: ", questionID);
    const keys = Object.keys(data);
    let validKeys = [];
    switch(questionID){
      case 'a9k1bpXX4H57Bdl7XhaX'://AROMAS QUESTION
        validKeys = firebaseListeners.getKeys('flavor');
        break;
      case 'awja0ameTALoE0D4GlWD'://COMMENTS QUESTION
        validKeys = ['comment'];
        break;
      case 'h1Cb1yTczSvyyaSqiWfK'://EFFECTS QUESTION
        validKeys = firebaseListeners.getKeys('effect');
        break;
      case 'wjA1uhqbIMFuZwAaCoJ8'://METRICS QUESTION
        validKeys = firebaseListeners.getKeys('metric');
        break;
      default:
        return res.status(400).send(`Invalid question ID: ${questionID}`);
    }
    console.log("Valid keys: ", validKeys);
    const invalidKeys = keys.filter(key => !validKeys.includes(key));
    console.log("Invalid keys: ", invalidKeys);
    if(invalidKeys.length > 0){
      throw new Error(`Invalid response keys: ${invalidKeys.join(', ')}`);
    }
  }

  module.exports = validateResponseKeys;