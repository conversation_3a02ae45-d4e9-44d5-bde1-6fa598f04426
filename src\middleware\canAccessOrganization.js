const { UserService } = require('../services');

/**
 * Middleware to check if the user can access the organization
 * Verifies that the organization_id from route parameters is in the user's organizations array
 */
const canAccessOrganization = async (req, res, next) => {
  const userId = req.user.uid;
  const organizationId = req.params.org_id || req.body.organization_id;
  
  try {
    const user = req.userData || await UserService.getUserById(userId);
    const userOrgs = user.organizations || [];
    
    if (userOrgs.includes(organizationId)) {
      req.organization_id = organizationId;
      req.userData = user;
      next();
    } else {
      res.status(403).send('User does not have access to this organization');
    }
  } catch (error) {
    console.error('Error checking organization access:', error);
    res.status(500).send('Error checking organization access');
  }
};

module.exports = canAccessOrganization;
