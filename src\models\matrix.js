const Joi = require('joi');

// Define matrix schema using <PERSON><PERSON> for robust validation
const matrixSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the matrix option'),
    
  type: Joi.string()
    .required()
    .description('Type of the matrix option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the matrix'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the matrix option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake matrix document for testing
const generateFakeMatrix = (overrides = {}) => {
  const fakeMatrix = {
    name: faker.lorem.word(),
    type: faker.helpers.arrayElement(['flower', 'concentrate', 'edible', 'topical', 'tincture']),
    priority: faker.number.int({min: 1, max: 100}),
    option_id: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateMatrix(fakeMatrix);
  if (error) {
    throw new Error(`Generated invalid matrix: ${error.message}`);
  }

  return fakeMatrix;
};

// Generate multiple fake matrices
const generateFakeMatrices = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeMatrix(overrides));
};

// Validate matrix document against schema
const validateMatrix = (matrix) => {
  return matrixSchema.validate(matrix, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to matrix model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateMatrix(data);
  
  if (error) {
    throw new Error(`Invalid matrix data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert matrix model to Firestore document
const toFirestore = (matrix) => {
  const { value, error } = validateMatrix(matrix);
  
  if (error) {
    throw new Error(`Invalid matrix data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  matrixSchema,
  generateFakeMatrix,
  generateFakeMatrices,
  validateMatrix,
  fromFirestore,
  toFirestore
};
