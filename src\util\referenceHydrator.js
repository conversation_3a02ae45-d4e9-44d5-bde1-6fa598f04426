const { firestoreModule } = require('../config');

/**
 * ReferenceHydrator - A utility for populating references across models
 * 
 * This class provides methods to hydrate references to other entities in a document
 * or array of documents. It supports both direct references (single ID strings) and 
 * array references (arrays of ID strings).
 */
class ReferenceHydrator {
  constructor() {
    this.firestoreModule = firestoreModule;
    // Cache to avoid fetching the same entity multiple times
    this.entityCache = new Map();
  }

  /**
   * Clear the internal entity cache
   */
  clearCache() {
    this.entityCache.clear();
  }

  /**
   * Fetch entity by ID and collection with caching
   * 
   * @param {string} collection - The collection name
   * @param {string} id - The entity ID
   * @returns {Promise<Object>} - The entity data
   */
  async fetchEntity(collection, id) {
    const cacheKey = `${collection}:${id}`;
    
    if (this.entityCache.has(cacheKey)) {
      return this.entityCache.get(cacheKey);
    }
    
    try {
      const entity = await this.firestoreModule.getDocument(collection, id);
      this.entityCache.set(cacheKey, entity);
      return entity;
    } catch (error) {
      console.error(`Error fetching ${collection} with ID ${id}: ${error.message}`);
      return null;
    }
  }

  /**
   * Batch fetch entities by IDs and collection with caching
   * 
   * @param {string} collection - The collection name
   * @param {string[]} ids - Array of entity IDs
   * @returns {Promise<Object[]>} - Array of entity data
   */
  async batchFetchEntities(collection, ids, id_field="id") {
    console.log("Batch fetching entities:", collection, ids);
    if (!ids || ids.length === 0) {
      return [];
    }
    
    // Filter out IDs we already have in cache
    const uncachedIds = ids.filter(id => {
      const cacheKey = `${collection}:${id}`;
      return !this.entityCache.has(cacheKey);
    });
    console.log("Uncached IDs:", uncachedIds);
    // Fetch uncached entities in batches (Firestore has a limit of 10 for 'in' queries)
    if (uncachedIds.length > 0) {
      const batchSize = 10;
      for (let i = 0; i < uncachedIds.length; i += batchSize) {
        const batchIds = uncachedIds.slice(i, i + batchSize);
        console.log("Batch IDs:", batchIds);
        try {
          // Using a batch query to fetch multiple entities at once
          //TODO: Properly fetch the id field for the collection
          const conditions = [[id_field, "in", batchIds]];
          const entities = await this.firestoreModule.queryDocuments(collection, conditions);
          
          // Add to cache
          entities.forEach(entity => {
            const cacheKey = `${collection}:${entity.id}`;
            this.entityCache.set(cacheKey, entity);
          });
        } catch (error) {
          console.error(`Error batch fetching ${collection}: ${error.message}`);
        }
      }
    }
    console.log("Returning entities from cache:", this.entityCache);
    // Return all requested entities from cache
    let ret =  ids
      .map(id => {
        const cacheKey = `${collection}:${id}`;
        console.log("Cache key:", cacheKey);
        return this.entityCache.get(cacheKey) || null;
      })
      .filter(Boolean); // Filter out nulls
    console.log("Returning entities:", ret);
    return ret;
  }

  /**
   * Hydrate references in a document based on a reference map
   * 
   * @param {Object} document - The document to hydrate
   * @param {Object} referenceMap - Map of field paths to reference definitions
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Document with hydrated references
   */
  async hydrateDocument(document, referenceMap, options = {}) {
    if (!document) {
      return null;
    }
    
    // Create a new object to avoid modifying the original
    const result = { ...document };
    console.log("Hydrating document:", result);
    // Process each reference definition
    const hydrationPromises = Object.entries(referenceMap).map(async ([fieldPath, definition]) => {
      // Extract the current field value using the field path
      const fieldValue = this.getFieldValueByPath(result, fieldPath);
      console.log("Field value:", fieldValue);
      if (!fieldValue) {
        return; // Nothing to hydrate
      }
      
      const { collection, target, isArray = false, fields = null, hydrate: nestedHydrate = null, id_field = "id" } = definition;
      console.log("Definition:", definition);
      if (isArray) {
        // Handle array of references
        console.log("Field value is an array:", Array.isArray(fieldValue));
        if (!Array.isArray(fieldValue) || fieldValue.length === 0) {
          return;
        }
        
        // Batch fetch all entities
        console.log("Batch fetching entities:", fieldValue);
        const entities = await this.batchFetchEntities(collection, fieldValue, id_field);
        console.log("Entities:", entities);
        // Process each entity
        const processedEntities = await Promise.all(
          entities.map(async entity => {
            // If we have nested hydration, apply it
            if (nestedHydrate) {
              entity = await this.hydrateDocument(entity, nestedHydrate, options);
            }
            console.log("Entity:", entity);
            // If fields is specified, only include those fields
            if (fields) {
              return this.pickFields(entity, fields);
            }
            
            return entity;
          })
        );
        
        // Set the hydrated value on the result
        this.setFieldValueByPath(result, target || fieldPath, processedEntities);
      } else {
        // Handle single reference
        const entity = await this.fetchEntity(collection, fieldValue);
        
        if (entity) {
          let processedEntity = entity;
          
          // If we have nested hydration, apply it
          if (nestedHydrate) {
            processedEntity = await this.hydrateDocument(entity, nestedHydrate, options);
          }
          
          // If fields is specified, only include those fields
          if (fields) {
            processedEntity = this.pickFields(processedEntity, fields);
          }
          
          // Set the hydrated value on the result
          this.setFieldValueByPath(result, target || fieldPath, processedEntity);
        }
      }
    });
    
    // Wait for all hydrations to complete
    await Promise.all(hydrationPromises);
    
    console.log("Hydrated project panels:", result.panels);
    
    return result;
  }
  
  /**
   * Hydrate references in an array of documents
   * 
   * @param {Object[]} documents - Array of documents to hydrate
   * @param {Object} referenceMap - Map of field paths to reference definitions
   * @param {Object} options - Additional options
   * @returns {Promise<Object[]>} - Documents with hydrated references
   */
  async hydrateDocuments(documents, referenceMap, options = {}) {
    if (!documents || documents.length === 0) {
      return [];
    }
    
    // Process all documents in parallel
    return Promise.all(
      documents.map(document => this.hydrateDocument(document, referenceMap, options))
    );
  }
  
  /**
   * Get a nested field value by path string (e.g., "user.profile.name")
   * 
   * @param {Object} obj - The object to traverse
   * @param {string} path - The dot-notation path to the field
   * @returns {any} - The field value or undefined
   */
  getFieldValueByPath(obj, path) {
    return path.split('.').reduce((current, part) => {
      return current && current[part] !== undefined ? current[part] : undefined;
    }, obj);
  }
  
  /**
   * Set a nested field value by path string
   * 
   * @param {Object} obj - The object to modify
   * @param {string} path - The dot-notation path to the field
   * @param {any} value - The value to set
   */
  setFieldValueByPath(obj, path, value) {
    const parts = path.split('.');
    const last = parts.pop();
    
    const target = parts.reduce((current, part) => {
      if (!current[part]) {
        current[part] = {};
      }
      return current[part];
    }, obj);
    
    target[last] = value;
  }
  
  /**
   * Pick specific fields from an object
   * 
   * @param {Object} obj - The source object
   * @param {string[]} fields - Array of field names to pick
   * @returns {Object} - New object with only the specified fields
   */
  pickFields(obj, fields) {
    if (!obj) return null;
    
    return fields.reduce((result, field) => {
      if (obj[field] !== undefined) {
        result[field] = obj[field];
      }
      return result;
    }, {});
  }
}

module.exports = new ReferenceHydrator(); 