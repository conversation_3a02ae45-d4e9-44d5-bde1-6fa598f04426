#!/bin/bash

# This script contains curl commands to test the project entity management functionality
# Replace the placeholders with actual values before running

TOKEN="YOUR_AUTH_TOKEN_HERE"
API_URL="http://localhost:7890"
PROJECT_ID="YOUR_PROJECT_ID"
USER_ID="YOUR_USER_ID"
PANEL_ID="YOUR_PANEL_ID"
SAMPLE_ID="YOUR_SAMPLE_ID"

echo "==== Project Entity Management Test Commands ===="
echo ""

echo "=== Panel Management ==="
echo ""

echo "1. Add a panel to a project"
echo "curl -X POST ${API_URL}/projects/${PROJECT_ID}/panels/${PANEL_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "2. Remove a panel from a project"
echo "curl -X DELETE ${API_URL}/projects/${PROJECT_ID}/panels/${PANEL_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "=== User Management ==="
echo ""

echo "3. Add a user to a project"
echo "curl -X POST ${API_URL}/projects/${PROJECT_ID}/users/${USER_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "4. Remove a user from a project"
echo "curl -X DELETE ${API_URL}/projects/${PROJECT_ID}/users/${USER_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "=== Sample Management ==="
echo ""

echo "5. Add a sample to a project"
echo "curl -X POST ${API_URL}/projects/${PROJECT_ID}/samples/${SAMPLE_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "6. Remove a sample from a project"
echo "curl -X DELETE ${API_URL}/projects/${PROJECT_ID}/samples/${SAMPLE_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "=== Collaborator Management ==="
echo ""

echo "7. Add a collaborator to a project"
echo "curl -X POST ${API_URL}/projects/${PROJECT_ID}/collaborators \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"user_id\": \"${USER_ID}\"}'"
echo ""

echo "8. Remove a collaborator from a project"
echo "curl -X DELETE ${API_URL}/projects/${PROJECT_ID}/collaborators/${USER_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "9. Get project details after changes"
echo "curl -X GET ${API_URL}/projects/${PROJECT_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "To run these commands, replace the placeholders at the top of the script with your actual values."
echo "You can run individual commands by copying and pasting them into your terminal." 