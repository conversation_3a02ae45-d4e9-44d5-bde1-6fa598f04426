const BaseRepository = require('./base');
const { validateUser, validateUserUpdate } = require('../models/user');

class UserRepository extends BaseRepository {
    constructor() {
        super();
    }

    // CORE CRUD OPERATIONS
    
    async read(id) {
        const userData = await this.getUserById(id);
        return userData;
    }

    async getUserById(id) {
        try {
            // Try to get from cache first
            const key = `user:${id}`;
            let userData = await this.redisCache.client.get(key);
    
            if (!userData) {
                // Cache miss: Fetch from Firestore
                console.log("Cache miss for user: ", id);
                userData = await this.firestoreModule.getDocument('users', id);
                
                // Cache the result with a TTL of 1 hour
                await this.redisCache.client.setex(key, 3600, JSON.stringify(userData));
            } else {
                userData = JSON.parse(userData);
            }
            return userData;
        } catch (error) {
            throw new Error(`Error fetching user: ${error.message}`);
        }
    }

    async create(userData) {
        try {
            const { error } = validateUser(userData);
            if (error) {
                throw new Error(`Invalid user data: ${error.message}`);
            }
            
            // Create in Firestore
            const userId = userData.id || await this.firestoreModule.getDocumentKey('users');
            await this.firestoreModule.createDocument('users', userData, userId);
            
            // Update cache
            const key = `user:${userId}`;
            userData.id = userId;
            await this.redisCache.client.setex(key, 3600, JSON.stringify(userData));
            
            return userData;
        } catch (error) {
            throw new Error(`Error creating user: ${error.message}`);
        }
    }

    async update(userId, updateData) {
        try {
            const { error } = validateUserUpdate(updateData);
            if (error) {
                throw new Error(`Invalid user data: ${error.message}`);
            }
            
            // Update in Firestore
            await this.firestoreModule.updateDocument('users', userId, updateData);
            
            // Update cache
            const key = `user:${userId}`;
            // Get current data first to merge with updates
            let currentData = await this.redisCache.client.get(key);
            if (currentData) {
                currentData = JSON.parse(currentData);
                const updatedData = { ...currentData, ...updateData };
                await this.redisCache.client.setex(key, 3600, JSON.stringify(updatedData));
                return updatedData;
            } else {
                // If not in cache, fetch complete updated document from Firestore
                const userData = await this.firestoreModule.getDocument('users', userId);
                await this.redisCache.client.setex(key, 3600, JSON.stringify(userData));
                return userData;
            }
        } catch (error) {
            throw new Error(`Error updating user: ${error.message}`);
        }
    }

    async delete(userId) {
        try {
            // Soft delete in Firestore
            await this.firestoreModule.softDeleteDocument('users', userId);
            
            // Remove from cache
            const keysToDelete = [
                `user:${userId}`,
                `userPanels:${userId}`,
                `completedPanels:${userId}`,
                `completedPanels:${userId}:*`,
                `progressPanels:${userId}:*`
            ];
            
            // Delete each pattern
            for (const pattern of keysToDelete) {
                await this.redisCache.deleteKeysByPattern(pattern);
            }
            
            return { success: true, message: `User ${userId} deleted successfully` };
        } catch (error) {
            throw new Error(`Error deleting user: ${error.message}`);
        }
    }

    // USER-ORGANIZATION RELATIONSHIP
    
    async getUsersByOrganization(organizationId) {
        try {
            const key = `orgUsers:${organizationId}`;
            let users = await this.redisCache.client.get(key);
            
            if (!users) {
                // Cache miss: Fetch from Firestore
                console.log("Cache miss for organization users: ", organizationId);
                
                // Query Firestore for users in this organization
                const query = ['organizations', 'array-contains', organizationId];
                users = await this.firestoreModule.getDocumentsByQuery('users', [query]);
                
                // Cache the result with a TTL of 1 hour
                await this.redisCache.client.setex(key, 3600, JSON.stringify(users));
                
                // Also cache individual users
                const pipeline = this.redisCache.client.pipeline();
                users.forEach(user => {
                    pipeline.setex(`user:${user.id}`, 3600, JSON.stringify(user));
                });
                await pipeline.exec();
            } else {
                users = JSON.parse(users);
            }
            
            return users;
        } catch (error) {
            throw new Error(`Error fetching users by organization: ${error.message}`);
        }
    }

    async addUserToOrganization(userId, organizationId, userData = null) {
        try {
            // Get current user data
            const userData = await this.getUserById(userId);
            
            // Check if user already belongs to this organization
            const userOrganizations = userData.organizations || [];
            if (userOrganizations.includes(organizationId)) {
                return { success: true, message: 'User already belongs to this organization' };
            }
            
            
            const updatedOrganizations = [...userOrganizations, organizationId];

            // Update in Firestore
            await this.firestoreModule.updateDocument('users', userId, {
                organizations: this.firestoreModule.FieldValue.arrayUnion(organizationId)
            });
            
            // Update cache
            const userKey = `user:${userId}`;
            
            if (userData) {
                userData.organizations = updatedOrganizations;
                await this.redisCache.client.setex(userKey, 3600, JSON.stringify(userData));
            }
            
            // Invalidate organization users cache
            await this.redisCache.deleteKeysByPattern(`orgUsers:${organizationId}`);
            
            // Invalidate available panels cache since user now has access to more panels
            await this.redisCache.deleteKeysByPattern(`availablePanels:${userId}`);
            
            return { 
                success: true, 
                message: 'User added to organization successfully',
                organizations: updatedOrganizations
            };
        } catch (error) {
            throw new Error(`Error adding user to organization: ${error.message}`);
        }
    }
}

module.exports = new UserRepository();