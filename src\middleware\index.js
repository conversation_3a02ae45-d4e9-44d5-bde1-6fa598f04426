const paginationMiddleware = require('./pagination');
const checkPermissions = require('./checkPermissions');
const canAccessOrganization = require('./canAccessOrganization');
const canAccessPanel = require('./canAccessPanel');
const canAccessPanelDetails = require('./canAccessPanelDetails');
const canAccessProductDetails = require('./canAccessProductDetails');
const authenticateApiUser = require('./authenticateApiUser');
const authenticateUser = require('./authenticateUser');
const authenticateFirebase = require('./authenticateFirebase');
const swaggerAuth = require('./swaggerAuth');
const getProgress = require('./getProgress');

module.exports = {
  paginationMiddleware,
  checkPermissions,
  canAccessOrganization,
  canAccessPanel,
  canAccessPanelDetails,
  canAccessProductDetails,
  authenticateApiUser,
  authenticateUser,
  authenticateFirebase,
  swaggerAuth,
  getProgress
}