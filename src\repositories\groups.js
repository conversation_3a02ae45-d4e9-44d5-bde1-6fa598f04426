const { validateGroup, validateGroupUpdate } = require("../models/group");
const BaseRepository = require("./base");

class GroupRepository extends BaseRepository {
  constructor() {
    super();
  }

  async create(groupData) {
    // Validate group data against schema
    const { error } = validateGroup(groupData);
    if (error) {
      throw new Error(`Invalid group data: ${error.message}`);
    }
    // Create group in Firestore
    const groupId = await this.firestoreModule
      .createDocument("groups", groupData)
      .then((docRef) => docRef.id);

    // cache in redis
    await this.redisCache.setAnyData(`groups:${groupId}`, groupData);
    return groupId;
  }

  async read(type, id) {
    let groupData = [];

    if (type === "user") {
      groupData = await this.redisCache.getGroupData("groups", id);
      const query = ["createdBy", "==", id];
      if (!groupData) {
        groupData = await this.firestoreModule.getDocumentsByQuery("groups", [query]);
        await this.redisCache.setAnyData(`groups:${id}`, groupData);
      }
    } else if (type === "group") {
      groupData = await this.redisCache.getAnyData("groups", id);
      if (!groupData) {
        groupData = await this.firestoreModule.getDocument("groups", id);
        await this.redisCache.setAnyData(`groups:${id}`, groupData);
      }
    } else if (type === "organization") {
      groupData = await this.redisCache.getGroupData("orgGroups", id);
      const query = ["organization_id", "==", id];
      if (!groupData) {
        groupData = await this.firestoreModule.getDocumentsByQuery("groups", [query]);
        await this.redisCache.setAnyData(`orgGroups:${id}`, groupData);
      }
    }

    if (!groupData) {
      throw new Error("Group not found");
    }

    if(!Array.isArray(groupData)) {
        if(groupData.deleted){
            throw new Error("Group not found");
        }
        return groupData
    };
    // Filter out soft-deleted groups
    return groupData.filter(group => !group.deleted);
  }

  async update(groupId, updateData) {
    // Validate update data against schema
    const { error } = validateGroupUpdate(updateData);
    if (error) {
      throw new Error(`Invalid group data: ${error.message}`);
    }

    let updated = await this.firestoreModule.updateDocument("groups", groupId, updateData);
    if (!updated) {
      throw new Error("Group not found");
    }
    
    // Get the updated group data to cache
    const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
    await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
    
    return updatedGroup;
  }

  async delete(groupId) {
    const group = await this.firestoreModule.getDocument("groups", groupId);

    let deleted = await this.firestoreModule.updateDocument("groups", groupId, {
      ...group,
      deleted: true,
    });

    if (!deleted) {
      throw new Error("Group not found");
    }
    
    console.log(deleted);
    
    // Update cache with deleted flag
    const updatedGroup = { ...group, deleted: true };
    await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
    
    return updatedGroup;
  }

  // === Atomic array operations for users ===
  
  /**
   * Add a user to a group using atomic Firestore updates
   * @param {string} groupId - The ID of the group
   * @param {string} userId - The ID of the user to add
   * @returns {Promise<Object>} - The updated group
   */
  async addUserToGroup(groupId, userId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("groups", groupId, {
        users: this.firestoreModule.FieldValue.arrayUnion(userId),
        updated_at: new Date()
      });
      
      // Get the updated group
      const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
      
      // Update cache
      await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
      
      return updatedGroup;
    } catch (error) {
      console.error(`Error adding user to group: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a user from a group using atomic Firestore updates
   * @param {string} groupId - The ID of the group
   * @param {string} userId - The ID of the user to remove
   * @returns {Promise<Object>} - The updated group
   */
  async removeUserFromGroup(groupId, userId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("groups", groupId, {
        users: this.firestoreModule.FieldValue.arrayRemove(userId),
        updated_at: new Date()
      });
      
      // Get the updated group
      const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
      
      // Update cache
      await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
      
      return updatedGroup;
    } catch (error) {
      console.error(`Error removing user from group: ${error.message}`);
      throw error;
    }
  }
  
  // === Atomic array operations for panels ===
  
  /**
   * Add a panel to a group using atomic Firestore updates
   * @param {string} groupId - The ID of the group
   * @param {string} panelId - The ID of the panel to add
   * @returns {Promise<Object>} - The updated group
   */
  async addPanelToGroup(groupId, panelId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("groups", groupId, {
        panels: this.firestoreModule.FieldValue.arrayUnion(panelId),
        updated_at: new Date()
      });
      
      // Get the updated group
      const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
      
      // Update cache
      await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
      
      return updatedGroup;
    } catch (error) {
      console.error(`Error adding panel to group: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a panel from a group using atomic Firestore updates
   * @param {string} groupId - The ID of the group
   * @param {string} panelId - The ID of the panel to remove
   * @returns {Promise<Object>} - The updated group
   */
  async removePanelFromGroup(groupId, panelId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("groups", groupId, {
        panels: this.firestoreModule.FieldValue.arrayRemove(panelId),
        updated_at: new Date()
      });
      
      // Get the updated group
      const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
      
      // Update cache
      await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
      
      return updatedGroup;
    } catch (error) {
      console.error(`Error removing panel from group: ${error.message}`);
      throw error;
    }
  }
  
  // === Atomic array operations for samples ===
  
  /**
   * Add a sample to a group using atomic Firestore updates
   * @param {string} groupId - The ID of the group
   * @param {string} sampleId - The ID of the sample to add
   * @returns {Promise<Object>} - The updated group
   */
  async addSampleToGroup(groupId, sampleId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("groups", groupId, {
        samples: this.firestoreModule.FieldValue.arrayUnion(sampleId),
        updated_at: new Date()
      });
      
      // Get the updated group
      const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
      
      // Update cache
      await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
      
      return updatedGroup;
    } catch (error) {
      console.error(`Error adding sample to group: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a sample from a group using atomic Firestore updates
   * @param {string} groupId - The ID of the group
   * @param {string} sampleId - The ID of the sample to remove
   * @returns {Promise<Object>} - The updated group
   */
  async removeSampleFromGroup(groupId, sampleId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("groups", groupId, {
        samples: this.firestoreModule.FieldValue.arrayRemove(sampleId),
        updated_at: new Date()
      });
      
      // Get the updated group
      const updatedGroup = await this.firestoreModule.getDocument("groups", groupId);
      
      // Update cache
      await this.redisCache.setAnyData(`groups:${groupId}`, updatedGroup);
      
      return updatedGroup;
    } catch (error) {
      console.error(`Error removing sample from group: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new GroupRepository();
