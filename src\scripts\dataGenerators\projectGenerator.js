const { faker } = require('@faker-js/faker');
const { firestoreModule } = require('../../config');

/**
 * Project Generator - Creates realistic project data with proper relationships
 * to organizations, users, panels and samples.
 */
class ProjectGenerator {
  constructor() {
    this.statuses = ['draft', 'ongoing', 'completed', 'archived'];
  }

  /**
   * Fetch real organizations from Firestore
   * @returns {Promise<Array>} List of organization documents
   */
  async fetchOrganizations() {
    try {
      console.log('Fetching organizations...');
      const organizations = await firestoreModule.getCollection('organizations');
      console.log(`Found ${organizations.length} organizations`);
      return organizations;
    } catch (error) {
      console.error('Error fetching organizations:', error.message);
      throw error;
    }
  }

  /**
   * Fetch real users that belong to the given organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} List of user documents
   */
  async fetchOrganizationUsers(organizationId) {
    try {
      console.log(`Fetching users for organization ${organizationId}...`);
      const users = await firestoreModule.queryDocuments('users', [
        ['organizations', 'array-contains', organizationId]
      ]);
      console.log(`Found ${users.length} users for organization ${organizationId}`);
      return users;
    } catch (error) {
      console.error(`Error fetching users for organization ${organizationId}:`, error.message);
      return [];
    }
  }

  /**
   * Fetch real panels that belong to the given organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} List of panel documents
   */
  async fetchOrganizationPanels(organizationId) {
    try {
      console.log(`Fetching panels for organization ${organizationId}...`);
      const panels = await firestoreModule.queryDocuments('panels', [
        ['organization_id', '==', organizationId]
      ]);
      console.log(`Found ${panels.length} panels for organization ${organizationId}`);
      return panels;
    } catch (error) {
      console.error(`Error fetching panels for organization ${organizationId}:`, error.message);
      return [];
    }
  }

  /**
   * Generate a random project status
   * @returns {string} Status
   */
  generateStatus() {
    return faker.helpers.arrayElement(this.statuses);
  }

  /**
   * Generate a random number of collaborators from users
   * @param {Array} users - List of users
   * @param {string} ownerId - ID of the owner (to exclude)
   * @param {number} max - Maximum number of collaborators
   * @returns {Array} List of user IDs
   */
  selectCollaborators(users, ownerId, max = 3) {
    const filteredUsers = users.filter(user => user.uid !== ownerId);
    const count = Math.min(faker.number.int({ min: 0, max }), filteredUsers.length);
    return faker.helpers.arrayElements(
      filteredUsers.map(user => user.uid),
      count
    );
  }

  /**
   * Select a random subset of panels
   * @param {Array} panels - List of panels
   * @param {number} max - Maximum number of panels
   * @returns {Array} List of panel IDs
   */
  selectPanels(panels, max = 5) {
    const count = Math.min(faker.number.int({ min: 1, max }), panels.length);
    return faker.helpers.arrayElements(
      panels.map(panel => panel.panel_id),
      count
    );
  }

  /**
   * Calculate mock completion percentage
   * @param {string} status - Project status
   * @returns {number} Percentage
   */
  calculateCompletionPercentage(status) {
    switch (status) {
      case 'draft':
        return 0;
      case 'ongoing':
        return faker.number.int({ min: 1, max: 99 });
      case 'completed':
        return 100;
      case 'archived':
        return faker.number.int({ min: 0, max: 100 });
      default:
        return 0;
    }
  }

  /**
   * Generate a project document with real relationships
   * @param {Object} data - Base project data
   * @returns {Object} Complete project document
   */
  generateProject(data) {
    const {
      organization,
      owner,
      panels,
      collaborators,
      status = this.generateStatus()
    } = data;

    const assigned_panels = this.selectPanels(panels);
    const total_responses = faker.number.int({ min: 0, max: 1000 });
    const completion_percentage = this.calculateCompletionPercentage(status);

    const now = new Date();

    return {
      project_id: faker.string.uuid(),
      name: faker.company.catchPhrase(),
      description: faker.lorem.paragraph(),
      organization_id: organization.organization_id || organization.id,
      owner: owner.uid,
      collaborators: collaborators || [],
      assigned_panels,
      assigned_users: [], // This will be populated by business logic
      status,
      total_responses,
      completion_percentage
    };
  }

  /**
   * Create a project in Firestore
   * @param {Object} projectData - Project data to save
   * @returns {Promise<Object>} Created project
   */
  async createProject(projectData) {
    try {
      console.log(`Creating project ${projectData.name}...`);
      const projectRef = firestoreModule.db.collection('projects').doc(projectData.project_id);
      await projectRef.set(projectData);
      console.log(`Project ${projectData.name} created with ID: ${projectData.project_id}`);
      return projectData;
    } catch (error) {
      console.error(`Error creating project ${projectData.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Generate and create a batch of projects
   * @param {number} count - Number of projects to generate
   * @returns {Promise<Array>} List of created projects
   */
  async generateProjects(count = 5) {
    try {
      const organizations = await this.fetchOrganizations();
      if (!organizations.length) {
        throw new Error('No organizations found to create projects');
      }

      const createdProjects = [];

      for (let i = 0; i < count; i++) {
        // Select a random organization
        const organization = faker.helpers.arrayElement(organizations);
        
        // Fetch users and panels for this organization
        const users = await this.fetchOrganizationUsers(organization.organization_id || organization.id);
        if (!users.length) {
          console.warn(`Skipping project creation for org ${organization.organization_id || organization.id} - no users found`);
          continue;
        }

        const panels = await this.fetchOrganizationPanels(organization.organization_id || organization.id);
        if (!panels.length) {
          console.warn(`Skipping project creation for org ${organization.organization_id || organization.id} - no panels found`);
          continue;
        }

        // Select a random user as owner
        const owner = faker.helpers.arrayElement(users);
        
        // Generate collaborators
        const collaborators = this.selectCollaborators(users, owner.uid);
        
        // Generate the project
        const status = this.generateStatus();
        const projectData = this.generateProject({
          organization,
          owner,
          panels,
          collaborators,
          status
        });

        // Create the project in Firestore
        const createdProject = await this.createProject(projectData);
        createdProjects.push(createdProject);
      }

      console.log(`Successfully created ${createdProjects.length} projects`);
      return createdProjects;
    } catch (error) {
      console.error('Error generating projects:', error.message);
      throw error;
    }
  }
}

module.exports = ProjectGenerator; 