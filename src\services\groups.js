const {GroupRepository} = require('../repositories');
const BaseService = require('./base');
const ProjectService = require('./projects');
const referenceHydrator = require('../util/referenceHydrator');
const { GROUP_REFERENCES, GROUP_FULL_REFERENCES } = require('../util/referenceMap');

class GroupService extends BaseService{
    constructor(){
        super();
        this.GroupRepository = GroupRepository;
        this.projectService = new ProjectService();
    }

    async read(groupId){
        return await this.getGroupbyGroupId(groupId);
    }

    async createGroup(groupData){
        return await this.GroupRepository.create(groupData);
    }

    async getGroupsByUser(userId){
        return await this.GroupRepository.read('user',userId);
    }

    async getGroupbyGroupId(groupId){
        return await this.GroupRepository.read('group',groupId);
    }

    async updateGroup(groupId, updateData){
        // First update the group
        const updatedGroup = await this.GroupRepository.update(groupId, updateData);
        
        // Then sync all projects that use this group
        await this.projectService.syncProjectsWithGroupChanges(groupId, updateData);
        
        // If the group data affects project completion (like adding/removing panels), 
        // also refresh project statuses
        if (updateData.panels || updateData.users || updateData.samples) {
            await this.projectService.refreshProjectsForGroup(groupId);
        }
        
        return updatedGroup;
    }

    async deleteGroup(groupId){
        // Get group data before deletion
        const groupData = await this.getGroupbyGroupId(groupId);
        
        // Delete the group
        const deletedGroup = await this.GroupRepository.delete(groupId);
        
        // Sync all projects that used this group
        await this.projectService.syncProjectsWithGroupChanges(groupId, {
            ...groupData,
            users: [],
            panels: [],
            samples: []
        });
        
        return { success: true, message: `Group ${groupId} deleted successfully` };
    }

    async getByOrganization(organizationId){
        return await this.GroupRepository.read('organization', organizationId);
    }
    
    // === User Management Methods ===
    
    /**
     * Add a user to a group
     * @param {string} groupId - The ID of the group
     * @param {string} userId - The ID of the user to add
     * @returns {Promise<Object>} - The updated group
     */
    async addUserToGroup(groupId, userId) {
        try {
            // Add user to group
            const updatedGroup = await this.GroupRepository.addUserToGroup(groupId, userId);
            
            // Sync projects that use this group
            await this.projectService.syncProjectsWithGroupChanges(groupId, updatedGroup);
            
            return updatedGroup;
        } catch (error) {
            throw new Error(`Error adding user to group: ${error.message}`);
        }
    }
    
    /**
     * Remove a user from a group
     * @param {string} groupId - The ID of the group
     * @param {string} userId - The ID of the user to remove
     * @returns {Promise<Object>} - The updated group
     */
    async removeUserFromGroup(groupId, userId) {
        try {
            // Remove user from group
            const updatedGroup = await this.GroupRepository.removeUserFromGroup(groupId, userId);
            
            // Sync projects that use this group
            await this.projectService.syncProjectsWithGroupChanges(groupId, updatedGroup);
            
            return updatedGroup;
        } catch (error) {
            throw new Error(`Error removing user from group: ${error.message}`);
        }
    }
    
    // === Panel Management Methods ===
    
    /**
     * Add a panel to a group
     * @param {string} groupId - The ID of the group
     * @param {string} panelId - The ID of the panel to add
     * @returns {Promise<Object>} - The updated group
     */
    async addPanelToGroup(groupId, panelId) {
        try {
            // Add panel to group
            const updatedGroup = await this.GroupRepository.addPanelToGroup(groupId, panelId);
            
            // Sync projects that use this group
            await this.projectService.syncProjectsWithGroupChanges(groupId, updatedGroup);
            
            // Refresh project status since panel assignments affect completion
            await this.projectService.refreshProjectsForGroup(groupId);
            
            return updatedGroup;
        } catch (error) {
            throw new Error(`Error adding panel to group: ${error.message}`);
        }
    }
    
    /**
     * Remove a panel from a group
     * @param {string} groupId - The ID of the group
     * @param {string} panelId - The ID of the panel to remove
     * @returns {Promise<Object>} - The updated group
     */
    async removePanelFromGroup(groupId, panelId) {
        try {
            // Remove panel from group
            const updatedGroup = await this.GroupRepository.removePanelFromGroup(groupId, panelId);
            
            // Sync projects that use this group
            await this.projectService.syncProjectsWithGroupChanges(groupId, updatedGroup);
            
            // Refresh project status since panel assignments affect completion
            await this.projectService.refreshProjectsForGroup(groupId);
            
            return updatedGroup;
        } catch (error) {
            throw new Error(`Error removing panel from group: ${error.message}`);
        }
    }
    
    // === Sample Management Methods ===
    
    /**
     * Add a sample to a group
     * @param {string} groupId - The ID of the group
     * @param {string} sampleId - The ID of the sample to add
     * @returns {Promise<Object>} - The updated group
     */
    async addSampleToGroup(groupId, sampleId) {
        try {
            // Add sample to group
            const updatedGroup = await this.GroupRepository.addSampleToGroup(groupId, sampleId);
            
            // Sync projects that use this group
            await this.projectService.syncProjectsWithGroupChanges(groupId, updatedGroup);
            
            return updatedGroup;
        } catch (error) {
            throw new Error(`Error adding sample to group: ${error.message}`);
        }
    }
    
    /**
     * Remove a sample from a group
     * @param {string} groupId - The ID of the group
     * @param {string} sampleId - The ID of the sample to remove
     * @returns {Promise<Object>} - The updated group
     */
    async removeSampleFromGroup(groupId, sampleId) {
        try {
            // Remove sample from group
            const updatedGroup = await this.GroupRepository.removeSampleFromGroup(groupId, sampleId);
            
            // Sync projects that use this group
            await this.projectService.syncProjectsWithGroupChanges(groupId, updatedGroup);
            
            return updatedGroup;
        } catch (error) {
            throw new Error(`Error removing sample from group: ${error.message}`);
        }
    }

    /**
     * Get group with hydrated references
     * @param {string} groupId - The group ID
     * @param {Object} referenceOptions - Options for reference hydration
     * @returns {Promise<Object>} - Hydrated group
     */
    async getGroupWithReferences(groupId, referenceOptions = {}) {
        try {
            // Get base group
            const group = await this.GroupRepository.read('group', groupId);
            if (!group) {
                throw new Error("Group not found");
            }
            
            // Determine which references to hydrate
            const { 
                references = GROUP_REFERENCES,
                includeFullDetails = false
            } = referenceOptions;
            
            // Use the full reference map if requested
            const referenceMap = includeFullDetails ? GROUP_FULL_REFERENCES : references;
            
            // Hydrate references
            const hydratedGroup = await referenceHydrator.hydrateDocument(group, referenceMap);
            
            return hydratedGroup;
        } catch (error) {
            console.error(`Error fetching group with references: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get groups with hydrated references
     * @param {Array} groups - Array of group objects
     * @param {Object} referenceOptions - Options for reference hydration
     * @returns {Promise<Object[]>} - Hydrated groups
     */
    async getGroupsWithReferences(groups, referenceOptions = {}) {
        try {
            if (!groups || groups.length === 0) {
                return [];
            }
            
            // Determine which references to hydrate
            const { 
                references = GROUP_REFERENCES,
                includeFullDetails = false,
                includeFields = null  // Optional fields to include in result
            } = referenceOptions;
            
            // Use the full reference map if requested
            const referenceMap = includeFullDetails ? GROUP_FULL_REFERENCES : references;
            
            // Hydrate all groups in parallel
            const hydratedGroups = await referenceHydrator.hydrateDocuments(groups, referenceMap);
            
            // Filter fields if requested
            if (includeFields) {
                return hydratedGroups.map(group => {
                    return includeFields.reduce((result, field) => {
                        if (group[field] !== undefined) {
                            result[field] = group[field];
                        }
                        return result;
                    }, {});
                });
            }
            
            return hydratedGroups;
        } catch (error) {
            console.error(`Error fetching groups with references: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get groups by user with hydrated references
     * @param {string} userId - User ID
     * @param {Object} referenceOptions - Options for reference hydration
     * @returns {Promise<Object[]>} - Hydrated groups
     */
    async getGroupsByUserWithReferences(userId, referenceOptions = {}) {
        try {
            const groups = await this.GroupRepository.read('user', userId);
            return this.getGroupsWithReferences(groups, referenceOptions);
        } catch (error) {
            throw new Error(`Error getting groups by user with references: ${error.message}`);
        }
    }

    /**
     * Get groups by organization with hydrated references
     * @param {string} organizationId - Organization ID
     * @param {Object} referenceOptions - Options for reference hydration
     * @returns {Promise<Object[]>} - Hydrated groups
     */
    async getGroupsByOrganizationWithReferences(organizationId, referenceOptions = {}) {
        try {
            const groups = await this.GroupRepository.read('organization', organizationId);
            return this.getGroupsWithReferences(groups, referenceOptions);
        } catch (error) {
            throw new Error(`Error getting groups by organization with references: ${error.message}`);
        }
    }
}

module.exports = new GroupService();