const { validateResponse } = require("../models/response");
const BaseRepository = require("./base");

class ResponseRepository extends BaseRepository {
  constructor() {
    super();
  }

  async create(responseData) {
    const { error } = validateResponse(responseData);
    if (error) {
      throw new Error(`Invalid response data: ${error.message}`);
    }

    // Create response in Firestore
    const responseId = await this.firestoreModule.createDocument(
      "responses",
      responseData
    );

    // Cache the new response data
    await this.redisCache.setAnyData(`responses:${responseId}`, responseData);

    return responseId;
  }

  async read(responseId) {
    let responseDetails = await this.redisCache.getAnyData(`responses:${responseId}`);

    if (!responseDetails) {
      // If not in cache, fetch from Firestore
      responseDetails = await this.firestoreModule.getDocument("responses", responseId);
      if (!responseDetails) {
        throw new Error("Response not found");
      }
      // Cache the result
      await this.redisCache.setAnyData(`responses:${responseId}`, responseDetails);
    }

    return responseDetails;
  }

  async update(id, updateData) {
    // Get current response data to merge with updates
    const currentResponse = await this.read(id);
    const updatedResponse = { ...currentResponse, ...updateData };

    // Validate the merged data
    const { error } = validateResponse(updatedResponse);
    if (error) {
      throw new Error(`Invalid response data: ${error.message}`);
    }

    const updated = await this.firestoreModule.updateDocument(
      "responses",
      id,
      updateData
    );
    
    if (!updated) {
      throw new Error("Response not found");
    }

    // Update Redis cache with new data
    await this.redisCache.setAnyData(`responses:${id}`, updatedResponse);

    return updatedResponse;
  }

  async delete(id) {
    // Mark response as deleted in Firestore
    await this.firestoreModule.markDeleted(id, "responses");
    
    // Remove from Redis cache
    await this.redisCache.deleteAnyData(`responses:${id}`);
  }

  async getResponsesByUser(userId) {
    // Query Firestore for responses by user ID
    const responses = await this.firestoreModule.queryDocuments(
      "responses",
      [["user_id", "==", userId]]
    );
    
    return responses;
  }

  async getResponsesByPanel(panelId) {
    // Query Firestore for responses by panel ID
    const responses = await this.firestoreModule.queryDocuments(
      "responses",
      [["panel_id", "==", panelId]]
    );
    
    return responses;
  }

  async getResponsesByProduct(productId) {
    // Query Firestore for responses by product ID
    const responses = await this.firestoreModule.queryDocuments(
      "responses",
      [["product_id", "==", productId]]
    );
    
    return responses;
  }

  async getResponseByPanelAndUser(panelId, userId) {
    // Query Firestore for a specific response by panel and user
    const responses = await this.firestoreModule.queryDocuments(
      "responses",
      [
        ["panel_id", "==", panelId],
        ["user_id", "==", userId]
      ]
    );
    
    return responses.length > 0 ? responses[0] : null;
  }

  async getResponsesByQuestion(questionId) {
    // Query Firestore for responses by question ID
    const responses = await this.firestoreModule.queryDocuments(
      "responses",
      [["question_id", "==", questionId]]
    );
    
    return responses;
  }
}

module.exports = ResponseRepository; 