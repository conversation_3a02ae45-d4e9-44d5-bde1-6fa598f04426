const { firestoreModule } = require('../config');

const authenticateFirebase = async (req, res, next) => {
    const token = req.headers.authorization;
    if (!token) return res.status(401).send('No token provided');
  
    try {
      const decodedToken = await firestoreModule.auth.verifyIdToken(token);
      req.user = decodedToken;
      next();
    } catch (error) {
      res.status(401).send('Invalid token');
    }
  };

  module.exports = authenticateFirebase;