const { firestoreModule } = require('../config');

async function populateMJBizAggregates(){
    let panel_ids = ["5RbZxBBNEfxEtaIX27S2", "8m5tiz5h1DeqPT3Uf6D0", "GZ8I3oRDCAv5XVVzBOz4", "YdASb3BgpursCkauBhGv", "aEgAPKMxoXSyr1UGsJfa", "abRJuERkwwWzzmM9bYqC"];
    let product_ids = ["WbkNBItCkakC0y3hrZf9","xDmQrgO9hU2JBnexo7Pc","Y8EkgslO9gfbz6nMSKDs","xZiISxDxy4B3ceQgIdzL","Ofark2pXffjxrfLnHMDo","YnzO4srWPNY5htcKoegw"];
    // const panels = await firestoreModule.db.collection('panels').get();
    for(let panel_id of panel_ids){
        await firestoreModule.createDocument("aggregates", { count: 0 }, panel_id);
    }
    // const products = await firestoreModule.db.collection('products').get();
    for(let product_id of product_ids){
        await firestoreModule.createDocument("aggregates", { count: 0 }, product_id);
    }
}

module.exports = populateMJBizAggregates;