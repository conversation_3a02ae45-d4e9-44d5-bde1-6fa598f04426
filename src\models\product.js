const Joi = require('joi');

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - description
 *         - image
 *         - name
 *         - lot_number
 *         - manufacturing_date
 *         - product_type
 *         - packaging_option
 *         - packaging_date
 *         - producer
 *       properties:
 *         description:
 *           type: string
 *           description: Description of the product
 *         external_id:
 *           type: string
 *           description: External identifier for the product
 *         image:
 *           type: string
 *           format: uri
 *           description: Product image URL
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: Display name of the product
 *         organization_id:
 *           type: string
 *           description: ID of the organization that owns this product
 *         product_id:
 *           type: string
 *           description: ID of the product
 *         lot_number:
 *           type: string
 *           description: Lot number of the product
 *         manufacturing_date:
 *           type: string
 *           format: date
 *           description: Manufacturing date of the product
 *         notes:
 *           type: string
 *           description: Notes about the product
 *         product_type:
 *           type: string
 *           description: Type of the product
 *         product_subtype:
 *           type: string
 *           description: Subtype of the product
 *         packaging_option:
 *           type: string
 *           description: Packaging option of the product
 *         packaging_date:
 *           type: string
 *           format: date
 *           description: Packaging date of the product
 *         producer:
 *           type: string
 *           description: Producer of the product
 *       example:
 *         description: "Premium vaporizer cartridge with full-spectrum distillate"
 *         external_id: "VPC001"
 *         image: "https://example.com/images/product1.jpg"
 *         name: "Premium Vape Cartridge"
 *         organization_id: "org123456"
 *         product_id: "prod789012"
 *         lot_number: "L2023101"
 *         manufacturing_date: "2023-10-01"
 *         notes: "Bestselling product in Q4 2023"
 *         product_type: "Vaporizer"
 *         product_subtype: "Cartridge"
 *         packaging_option: "Glass cartridge with box"
 *         packaging_date: "2023-10-05"
 *         producer: "Abstrax Labs"
 */

// Define product schema using Joi for robust validation
const productSchema = Joi.object({
  description: Joi.string()
    .required()
    .description('Description of the product'),
    
  external_id: Joi.string()
    .optional()
    .allow('')
    .description('External identifier for the product'),
    
  image: Joi.string()
    .required()
    .uri()
    .description('Product image URL'),
    
  name: Joi.string()
    .required()
    .min(2)
    .max(100)
    .description('Display name of the product'),
    
  organization_id: Joi.string()
    .optional()
    .allow('')
    .description('ID of the organization that owns this product'),

  product_id: Joi.string()
    .optional()
    .allow('')
    .description('ID of the product'),

  lot_number: Joi.string()
    .required()
    .description('Lot number of the product'),

  manufacturing_date: Joi.date()
    .required()
    .description('Manufacturing date of the product'),

  notes: Joi.string()
    .optional()
    .allow('')
    .description('Notes about the product'),

  product_type: Joi.string()
    .required()
    .description('Type of the product'),

  product_subtype: Joi.string()
    .optional()
    .allow('')
    .description('Subtype of the product'),

  packaging_option: Joi.string()
    .required()
    .description('Packaging option of the product'),

  packaging_date: Joi.date()
    .required()
    .description('Packaging date of the product'),

  producer: Joi.string()
    .required()
    .description('Producer of the product')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake product document for testing
const generateFakeProduct = (overrides = {}) => {
  const fakeProduct = {
    description: faker.commerce.productDescription(),
    external_id: faker.string.alphanumeric(20),
    image: faker.image.url(),
    name: faker.commerce.productName(),
    organization_id: faker.string.alphanumeric(20),
    product_id: faker.string.alphanumeric(20),
    lot_number: faker.string.alphanumeric(20),
    manufacturing_date: faker.date.recent(),
    notes: faker.lorem.sentence(),
    product_type: faker.commerce.product(),
    product_subtype: faker.commerce.product(),
    packaging_option: faker.commerce.product(),
    packaging_date: faker.date.recent(),
    producer: faker.company.name(),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateProduct(fakeProduct);
  if (error) {
    throw new Error(`Generated invalid product: ${error.message}`);
  }

  return fakeProduct;
};

// Generate multiple fake products
const generateFakeProducts = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeProduct(overrides));
};

// Validate product document against schema
const validateProduct = (product) => {
  return productSchema.validate(product, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Add this new validation function for updates
const validateProductUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(productSchema.describe().keys).reduce((acc, [key, value]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to product model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateProduct(data);
  
  if (error) {
    throw new Error(`Invalid product data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert product model to Firestore document
const toFirestore = (product) => {
  const { value, error } = validateProduct(product);
  
  if (error) {
    throw new Error(`Invalid product data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  productSchema,
  generateFakeProduct,
  generateFakeProducts,
  validateProduct,
  validateProductUpdate,
  fromFirestore,
  toFirestore
};
