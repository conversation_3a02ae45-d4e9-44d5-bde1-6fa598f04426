const express = require('express');
const router = express.Router();

const { redisCache, firestoreModule } = require('../config');
const { paginatePanels, updateAggregateData, splitTimestamp } = require('../util');
const { paginationMiddleware, checkPermissions, canAccessPanel, authenticateUser, getProgress } = require('../middleware');
const { validatePanel } = require('../models/panel');
const { PanelService, UserService } = require('../services');
const canCreatePublicPanel = require('../middleware/canCreatePublicPanel');

// Get all panels

router.all('*', authenticateUser);

/**
 * @swagger
 * /panels:
 *   get:
 *     summary: Get available panels for the user
 *     description: Retrieves all panels available to the user that they have not started or completed
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter panels
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Panels retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 panels:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Panel'
 *                 currentPage:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasMore:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', paginationMiddleware, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    try {
        const userId = req.user.uid;
        const organization = req.query.organization || null;
        
        // Get user data
        const user = await UserService.getUserById(userId);
        const userOrgs = organization && user['organizations'].includes(organization) ? [organization] : user['organizations'];
        console.log("User orgs: ", userOrgs);

        // Get available panels for the user
        const availablePanelIds = await PanelService.getAvailablePanels(userId, userOrgs);
        console.log("Available panel ids: ", availablePanelIds);
        
        // Get user's in-progress and completed panels
        const userPanelIds = await PanelService.getUserPanels(userId, 'all');
        console.log("User panel ids: ", userPanelIds);
        
        // Filter out panels that are already in progress or completed
        const availablePanelsSet = new Set(availablePanelIds);
        userPanelIds.forEach(panelId => {
            availablePanelsSet.delete(panelId);
        });
        console.log("Available panels set: ", availablePanelsSet);
        
        // Convert back to array
        const filteredPanelIds = Array.from(availablePanelsSet);
        console.log("Filtered panel ids: ", filteredPanelIds);
        // Paginate the results
        let paginatedPanels = await paginatePanels(
            filteredPanelIds, 
            req.pagination.page, 
            req.pagination.limit, 
            req.pagination.startIndex
        );

        res.status(200).send(paginatedPanels);
    } catch (error) {
        res.status(500).send('Error fetching panels: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/{panel_id}:
 *   delete:
 *     summary: Delete a panel
 *     description: Marks a panel as deleted and removes it from Redis cache
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to delete
 *     responses:
 *       200:
 *         description: Panel deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel
 *       500:
 *         description: Server error
 */
router.delete('/:panel_id', checkPermissions(['admin', 'insights']), canAccessPanel, async (req, res) => {
    try {
        await firestoreModule.markDeleted('panels', req.params.panel_id);
        await redisCache.deletePanel(req.params.panel_id);
        res.status(200).send({success: true});
    } catch (error) {
        res.status(500).send('Error deleting panel: ' + error.message);
    }
});

// Create a new panel
/**
 * @swagger
 * /panels:
 *   post:
 *     summary: Create a new panel
 *     description: Creates a new panel with the provided data
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Panel'
 *     responses:
 *       201:
 *         description: Panel created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Panel'
 *                 - type: object
 *                   properties:
 *                     panel_id:
 *                       type: string
 *                     created:
 *                       type: object
 *                     updated:
 *                       type: object
 *       400:
 *         description: Invalid panel data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', checkPermissions(['admin', 'insights']), canCreatePublicPanel, async (req, res) => {
    try {
        const userId = req.user.uid;
        const panelData = req.body;

        const new_panel_id = await firestoreModule.getDocumentKey('panels');
        let new_panel = {
            "active": true,
            "allowedOrganizations": [req.userData.organizations[0]],//TODO: Replace with primaryOrganization of user
            "organization_id": req.userData.organizations[0],
            "invite_code": "123456789",
            "minutes": 5,
            "panel_id": new_panel_id,
            "deleted": false,
            ...panelData
        }

        // Validate panel data against schema
        const { error } = validatePanel(new_panel);
        if (error) {
            return res.status(400).send(`Invalid panel data: ${error.message}`);
        }

        let start_date = new_panel.start_date;
        let end_date = new_panel.end_date;

        // Convert ISO string timestamps to Firebase timestamps
        if (new_panel.start_date) {
            new_panel.start_date = firestoreModule.Timestamp.fromDate(new Date(new_panel.start_date));
        }
        if (new_panel.end_date) {
            new_panel.end_date = firestoreModule.Timestamp.fromDate(new Date(new_panel.end_date));
        }

        // Create panel and its aggregate record in Firestore
        const panelId = await firestoreModule.createDocument('panels', new_panel, new_panel_id);
        console.log("Creating Panel - Panel ID: ", panelId, new_panel_id);
        const aggregateId = await firestoreModule.createDocument('aggregates', { count: 0 }, new_panel_id);

        //TODO: Add to the panel_count in the linked product

        // Add panel ID to organization's panel list in Redis
        await redisCache.addPanelToOrganization(new_panel.organization_id, new_panel_id);

        // Format the panel data for Redis
        new_panel['id'] = new_panel_id;
        new_panel['end_date'] = splitTimestamp(end_date);
        new_panel['start_date'] = splitTimestamp(start_date);

        // Cache the new panel data
        await redisCache.setPanelData(new_panel_id, new_panel);

        res.status(201).send({
            panel_id: new_panel_id,
            ...new_panel,
            created: {
                _seconds: new Date().getTime() / 1000,
                _nanoseconds: 0
            },
            updated: {
            _seconds: new Date().getTime() / 1000,
            _nanoseconds: 0
            }
        });

    } catch (error) {
        res.status(500).send('Error creating panel: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/aggregates/{panel_id}:
 *   get:
 *     summary: Get aggregate data for a panel
 *     description: Retrieves aggregate response data for a specific panel
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to get aggregates for
 *     responses:
 *       200:
 *         description: Aggregate data retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel
 *       500:
 *         description: Server error
 */
router.get('/aggregates/:panel_id', canAccessPanel, checkPermissions(['survey', 'insights']), async (req, res) => {
    try {
        const aggregateData = await redisCache.getAggregates(req.params.panel_id);
        res.status(200).send(aggregateData);
    } catch (error) {
        res.status(500).send('Error fetching aggregate data: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/completed:
 *   get:
 *     summary: Get completed panels for the user
 *     description: Retrieves all panels that the user has completed
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Completed panels retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 panels:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Panel'
 *                 currentPage:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasMore:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/completed', checkPermissions(['survey']), paginationMiddleware, async (req, res) => {
    try {
        const userId = req.user.uid;
        const completedPanels = await redisCache.getCompletedPanels(userId);
        let paginatedPanels = await paginatePanels(completedPanels, req.pagination.page, req.pagination.limit, req.pagination.startIndex, userId);
        res.status(200).send(paginatedPanels);
    } catch (error) {
        console.log("Error fetching completed panels: ", error);
        res.status(500).send('Error fetching completed panels: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/in-progress:
 *   get:
 *     summary: Get in-progress panels for the user
 *     description: Retrieves all panels that the user has started but not completed
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: In-progress panels retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 panels:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Panel'
 *                 currentPage:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasMore:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/in-progress', checkPermissions(['survey']), paginationMiddleware, async (req, res) => {
    try {
        const userId = req.user.uid;
        const userPanelIds = await redisCache.getInProgressPanels(userId);
        let paginatedPanels = await paginatePanels(userPanelIds, req.pagination.page, req.pagination.limit, req.pagination.startIndex, userId);
        res.status(200).send(paginatedPanels);
    } catch (error) {
        res.status(500).send('Error fetching dashboard data: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/start/{panel_id}:
 *   get:
 *     summary: Start a panel
 *     description: Creates a progress document for the user to start taking a panel
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to start
 *     responses:
 *       200:
 *         description: Panel started successfully or existing progress returned
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel
 *       500:
 *         description: Server error
 */
router.get('/start/:panel_id', canAccessPanel, checkPermissions(['survey']), async (req, res) => {
    try {
        const userId = req.user.uid;
        const panel_id = req.params.panel_id;

        // Check if user has already started/completed this panel
        const existingProgress = await redisCache.getInProgressPanel(userId, panel_id);
        console.log("Existing progress: ", existingProgress);
        if (existingProgress) {
            //TODO: Can potentially just return a 200 since the frontend doesn't use the progress data
            return res.status(200).send(existingProgress);
            //return res.status(400).send('User has already started this panel');
        }

        // Create progress document
        const progressData = {
            panel_id: panel_id,
            product_id: req.panel.product_id,
            started: firestoreModule.FieldValue.serverTimestamp(),
            completed: false,
            currentQuestion: 0,
            question_ids: [],
            response_ids: {},
            responses: []
        };

        await firestoreModule.createDocument(`users/${userId}/panels`, progressData, panel_id);
        await redisCache.setAnyData(`progressPanels:${userId}:${panel_id}`, progressData);
        await redisCache.startPanel(userId, panel_id);

        res.status(201).send(progressData);

    } catch (error) {
        res.status(500).send('Error starting panel: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/finish/{panel_id}:
 *   get:
 *     summary: Finish a panel
 *     description: Marks a panel as completed for the user and updates aggregate data
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to mark as completed
 *     responses:
 *       200:
 *         description: Panel completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       400:
 *         description: Panel already completed or not all questions answered
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel
 *       500:
 *         description: Server error
 */
router.get('/finish/:panel_id', canAccessPanel, getProgress, checkPermissions(['survey']), async (req, res) => {
    try {
        console.log("Req progress: ", req.progress);

        if(req.progress.completed){
            console.log("Panel already completed");
            return res.status(400).send('Panel already completed');
        }

        console.log("Checking responses: ", req.progress.responses, req.panel.steps);
        for(let question_id of req.panel.steps){
            if(!req.progress.responses.includes(question_id)){
                console.log("Missing response: ", question_id);
                return res.status(400).send('User has not answered all questions');
            }
        }
        req.progress.completed = true;
        await firestoreModule.updateDocument(`users/${req.user.uid}/panels`, req.params.panel_id, {completed: true});
        await redisCache.setAnyData(`progressPanels:${req.user.uid}:${req.params.panel_id}`, req.progress);
        await redisCache.completePanel(req.user.uid, req.params.panel_id);
        //TODO: Add logic to update the panel's aggregate data
        await updateAggregateData(req.params.panel_id, req.panel.product_id, req.progress, req.userData);
        res.status(200).send({success: true});
    } catch (error) {
        res.status(500).send('Error finishing panel: ' + error);
    }
});

/**
 * @swagger
 * /panels/{panel_id}:
 *   put:
 *     summary: Update panel details
 *     description: Updates an existing panel with new data
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Panel'
 *     responses:
 *       200:
 *         description: Panel updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Panel'
 *                 - type: object
 *                   properties:
 *                     panel_id:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put('/:panel_id', checkPermissions(['admin', 'insights']), async (req, res) => {
    try {
        const panelId = req.params.panel_id;
        const updateData = req.body;

        let updated = await PanelService.updatePanel(panelId, updateData);

        res.status(200).send({
            panel_id: panelId,
            ...updated
        });

    } catch (error) {
        res.status(500).send('Error updating panel: ' + error.message);
    }
});

/**
 * @swagger
 * /panels/{panel_id}:
 *   get:
 *     summary: Get panel details
 *     description: Retrieves detailed information about a specific panel
 *     tags: [Panels]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to retrieve
 *     responses:
 *       200:
 *         description: Panel details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Panel'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel
 *       500:
 *         description: Server error
 */
router.get('/:panel_id', canAccessPanel, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    try {
        res.status(200).send(req.panel);
    } catch (error) {
        res.status(500).send('Error fetching panel details: ' + error.message);
    }
});

module.exports = router;