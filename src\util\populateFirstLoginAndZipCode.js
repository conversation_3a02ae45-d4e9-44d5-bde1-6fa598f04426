const { firestoreModule } = require('../config');
const dotenv = require('dotenv');

dotenv.config();

async function populateFirstLoginAndZipCode(){
    const users = await firestoreModule.db.collection('users').get();
    for(let user of users.docs){
        await firestoreModule.updateDocument('users', user.id, { firstLogin: true, zipCode: "87106" });
    }
}

module.exports = populateFirstLoginAndZipCode;