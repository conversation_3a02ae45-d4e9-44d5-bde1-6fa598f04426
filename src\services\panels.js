const BaseService = require('./base');
const { PanelRepository } = require('../repositories');

class PanelService extends BaseService {
    constructor() {
        super();
        this.panelRepository = PanelRepository;
    }

    async getPanel(panelId) {
        const panel = await this.firestoreModule.getDocument('panels', panelId);
        return panel;
    }

    async deletePanel(panelId){
        await this.firestoreModule.deleteDocument('panels', panelId);
        await this.redisCache.deletePanel(panelId);
    }
    //TODO: Add logic to update the panel's aggregate data
    async updatePanel(panelId, updateData){
        try {
            // Validate update data against schema
            let updated = await this.panelRepository.update(panelId, updateData);
            return {
                panel_id: panelId,
                ...updated
            };
    
        } catch (error) {
            throw new Error('Error updating panel: ' + error.message);
        }
    }

    async getUserPanels(userId, status = 'all', fetch=false) {
        // Get panel IDs
        const panelIds = await this.panelRepository.getUserPanelIds(userId, status);
        
        // If we need full panel data, fetch each panel
        if(fetch){
            const panels = await Promise.all(
                panelIds.map(async (panelId) => {
                    if (status === 'completed' || status === 'all') {
                        try {
                            return await this.panelRepository.getUserPanelData(userId, panelId);
                        } catch (error) {
                            console.error(`Error fetching panel ${panelId}:`, error);
                            return null;
                        }
                    } else {
                        // For in-progress, we might just need the IDs
                        return { id: panelId };
                    }
                })
            );
        
            return panels.filter(panel => panel !== null);
        }
        return panelIds;
    }
    
    async startPanel(userId, panelId) {
        // Check if user has access to this panel
        const availablePanelIds = await this.panelRepository.getAvailablePanelIds(userId);
        if (!availablePanelIds.includes(panelId)) {
            throw new Error('User does not have access to this panel');
        }
        
        // Get panel data
        const panelData = await this.panelRepository.getPanelByID(panelId);
        
        // Record panel start
        const startData = {
            panel_id: panelId,
            panel_title: panelData.title,
            organization_id: panelData.organization_id
            // Add other relevant panel data
        };
        
        return await this.panelRepository.recordPanelStart(userId, panelId, startData);
    }
    
    async completePanel(userId, panelId, completionData) {
        // Validate completion data
        // Check if panel was actually started
        // Add any business logic for completion
        
        return await this.panelRepository.recordPanelCompletion(userId, panelId, completionData);
    }
    
    async getAvailablePanels(userId, organizations=null, fetch=false) {
        console.log("Getting available panels for user: ", userId, "with organizations: ", organizations);
        // Get available panel IDs
        const panelIds = await this.panelRepository.getAvailablePanelIds(userId, organizations);
        
        // Fetch full panel data for each ID
        if(fetch){
            const panels = await Promise.all(
                panelIds.map(async (panelId) => {
                    try {
                    return await this.panelRepository.getPanelByID(panelId);
                } catch (error) {
                    console.error(`Error fetching panel ${panelId}:`, error);
                    return null;
                }
            })
        );
        
            return panels.filter(panel => panel !== null);
        }
        return panelIds;
    }


}

module.exports = PanelService;
