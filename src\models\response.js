/**
 * @swagger
 * components:
 *   schemas:
 *     Response:
 *       type: object
 *       required:
 *         - question_id
 *         - panel_id
 *         - product_id
 *         - organization_id
 *         - user_id
 *         - data
 *       properties:
 *         question_id:
 *           type: string
 *           description: Unique identifier for the question being answered
 *         panel_id:
 *           type: string
 *           description: Unique identifier for the panel being answered
 *         product_id:
 *           type: string
 *           description: Unique identifier for the product being answered
 *         organization_id:
 *           type: string
 *           description: Unique identifier for the organization being answered
 *         user_id:
 *           type: string
 *           description: Unique identifier for the user who submitted the response
 *         data:
 *           type: object
 *           description: Map of option_id to response value, contains the actual response data
 *           additionalProperties: true
 */
const Joi = require('joi');

// Define response schema using Joi for robust validation
const responseSchema = Joi.object({
  question_id: Joi.string()
    .required()
    .description('Unique identifier for the question being answered'),

  panel_id: Joi.string()
    .required()
    .description('Unique identifier for the panel being answered'),

  product_id: Joi.string()
    .required()
    .description('Unique identifier for the product being answered'),

  organization_id: Joi.string()
    .required()
    .description('Unique identifier for the organization being answered'),
  
  user_id: Joi.string()
    .required()
    .description('Unique identifier for the user being answered'),

  data: Joi.object()
    .pattern(
      Joi.string(), // option_id key pattern
      Joi.alternatives().try(
        Joi.string().min(0).max(255),//TODO: maybe change this to a better max
        Joi.number(),
        Joi.boolean()
      )
    )
    .required()
    .description('Map of option_id to response value')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake response document for testing
const generateFakeResponse = (overrides = {}) => {
  const fakeResponse = {
    question_id: faker.string.alphanumeric(20),
    panel_id: faker.string.alphanumeric(20),
    product_id: faker.string.alphanumeric(20),
    data: {
      [faker.string.alphanumeric(20)]: faker.helpers.arrayElement([0, 25, 99, 100]),
      [faker.string.alphanumeric(20)]: faker.helpers.arrayElement([0, 25, 99, 100]),
      [faker.string.alphanumeric(20)]: faker.helpers.arrayElement([0, 25, 99, 100])
    },
    ...overrides
  };

  // Validate the generated data
  const { error } = validateResponse(fakeResponse);
  if (error) {
    throw new Error(`Generated invalid response: ${error.message}`);
  }

  return fakeResponse;
};

// Generate multiple fake responses
const generateFakeResponses = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeResponse(overrides));
};

// Validate response document against schema
const validateResponse = (response) => {
  return responseSchema.validate(response, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to response model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateResponse(data);
  
  if (error) {
    throw new Error(`Invalid response data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert response model to Firestore document
const toFirestore = (response) => {
  const { value, error } = validateResponse(response);
  
  if (error) {
    throw new Error(`Invalid response data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  responseSchema,
  generateFakeResponse,
  generateFakeResponses,
  validateResponse,
  fromFirestore,
  toFirestore
};
