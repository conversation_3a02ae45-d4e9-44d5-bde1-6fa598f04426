const Redis = require('ioredis');

class RedisCache {
  constructor(firestore) {
    const redisConfig = {
        port: process.env.REDIS_PORT,
        host: process.env.REDIS_HOST,
        password: process.env.REDIS_PASSWORD,
        tls: {}
    }
    this.client = new Redis(redisConfig);
    this.firestore = firestore;

    // Prove the Redis connection is open
    this.client.on('connect', () => {
      console.log('Successfully connected to Redis');
      this.flushCachedData('all'); // Flush all cached data on startup, for debugging
    });

    this.client.on('error', (err) => {
      console.error('Redis connection error:', err);
    });
  }

  // Function to manually flush cached data for specific key groups
  async flushCachedData(keyGroup) {
    try {
      let pattern;
      switch (keyGroup) {
        case 'userPanels':
          pattern = 'userPanels:*';
          break;
        case 'panels':
          pattern = 'panel:*';
          break;
        case 'all':
          await this.client.flushall();
          console.log('All cached data flushed successfully');
          return;
        default:
          throw new Error('Invalid key group specified');
      }

      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(...keys);
        console.log(`Flushed ${keys.length} keys for ${keyGroup}`);
      } else {
        console.log(`No keys found for ${keyGroup}`);
      }
    } catch (error) {
      console.error('Error flushing cached data:', error);
      throw error;
    }
  }

  async getAggregates(panelId){
    return await this.getAnyData('aggregates', panelId);
  }


  async getUser(userId) {
    const key = `user:${userId}`;
    console.log("Getting user: ", key);
    let user = await this.client.get(key);
    //console.log("User: ", user);
    if(!user){
      console.log("Cache miss for user: ", userId);
      user = await this.firestore.fetchUserFromFirestore(userId);
      //console.log("User from firestore: ", user);
      await this.client.setex(key, 3600, JSON.stringify(user));
      //console.log("User set in cache: ", user);
    }
  
    return typeof user === 'string' ? JSON.parse(user) : user;
  }

  async getProductData(productId){
    return await this.getAnyData('products', productId);
  }

  async keyExists(key) {
    let exists = await this.client.exists(key);
    if(exists){
      return true;
    }
    exists = await this.firestore.documentExists('products', productId);
    if(exists){
      return true;
    } else {
      return false;
    }
  }

  // You could also add convenience methods like:
  async panelExists(panelId) {
    return this.keyExists(`panel:${panelId}`);
  }

  async userExists(userId) {
    return this.keyExists(`user:${userId}`);
  }

  async completePanel(userId, panelId){
    const in_progress_key = `userPanels:${userId}`;
    await this.client.srem(in_progress_key, [panelId]);
    const completed_key = `completedPanels:${userId}`;
    await this.client.sadd(completed_key, [panelId]);
  }

  async startPanel(userId, panelId){
    const in_progress_key = `userPanels:${userId}`;
    await this.client.sadd(in_progress_key, [panelId]);
  }

  async getInProgressPanels(userId){
    const key = `userPanels:${userId}`;
    let panelIds = await this.client.smembers(key);
    if(panelIds.length === 0){
      const panels = await this.firestore.fetchInProgressPanels(userId);
      panelIds = panels.map(panel => panel.id);
      if(panelIds.length > 0){
        await this.client.sadd(key, ...panelIds);
        await this.client.expire(key, 3600);
      }
    }
    return panelIds;
  }

  async getInProgressPanel(userId, panelId){
    const key = `progressPanels:${userId}:${panelId}`;
    let panel = await this.client.get(key);
    if(!panel){
      console.log("Cache miss for in progress panel: ", userId, panelId);
      panel = await this.firestore.fetchInProgressPanel(userId, panelId);
      //console.log("In ProgressPanel: ", panel);
      await this.setAnyData(key, panel);
    }
    return typeof panel === 'string' ? JSON.parse(panel) : panel;
  }

  async getPublicPanels(){
    const key = `publicPanels`;
    let panelIds = await this.client.smembers(key);
    if(panelIds.length === 0){
      const panels = await this.firestore.fetchPublicPanelsFromFirestore();
      panelIds = panels.map(panel => panel.id);
      if(panelIds.length > 0){
        await this.client.sadd(key, ...panelIds);
        await this.client.expire(key, 3600);
      }
    }
    return panelIds;
  }

  async setProductData(productId, productData){
    const key = `product:${productId}`;
    await this.client.setex(key, 3600, JSON.stringify(productData));
  }

  async getOrganizationProductIds(organizationId) {
    const pattern = `orgProducts:${organizationId}:*`;
    let productKeys = [];
    let cursor = '0';
    
    do {
      const [nextCursor, keys] = await this.client.scan(cursor, 'MATCH', pattern, 'COUNT', 100);
      cursor = nextCursor;
      productKeys.push(...keys);
    } while (cursor !== '0');
    
    if (productKeys.length === 0) {
      const products = await this.firestore.fetchOrganizationProductsFromFirestore(organizationId);
      if (products.length > 0) {
        const pipeline = this.client.pipeline();
        products.forEach(product => {
          pipeline.setex(`orgProducts:${organizationId}:${product.id}`, 3600, '1');
          this.setProductData(product.id, product);
        });
        await pipeline.exec();
        productKeys = products.map(product => `orgProducts:${organizationId}:${product.id}`);
      }
    }
    
    return productKeys.map(key => key.split(':')[2]);
  }

  async countPanelsUsingProduct(productId){
    const key = `panelCount:${productId}`;
    let count = await this.client.get(key);
    if(!count){
      count = await this.firestore.countPanelsUsingProduct(productId);
      await this.client.setex(key, 3600, count);
    }
    return count;
  }

  // Organization-specific panel ID caching
  async getOrganizationPanelIds(organizationId) {
    const pattern = `orgPanels:${organizationId}:*`;
    let panelKeys = [];
    let cursor = '0';
    
    do {
      const [nextCursor, keys] = await this.client.scan(cursor, 'MATCH', pattern, 'COUNT', 100);
      cursor = nextCursor;
      panelKeys.push(...keys);
    } while (cursor !== '0');
    
    if (panelKeys.length === 0) {
      const panels = await this.firestore.fetchOrganizationPanelsFromFirestore(organizationId);
      if (panels.length > 0) {
        const pipeline = this.client.pipeline();
        panels.forEach(panel => {
          pipeline.setex(`orgPanels:${organizationId}:${panel.id}`, 3600, '1');
          this.setPanelData(panel.id, panel);
        });
        await pipeline.exec();
        panelKeys = panels.map(panel => `orgPanels:${organizationId}:${panel.id}`);
      }
    }
    
    return panelKeys.map(key => key.split(':')[2]);
  }

  // Batch fetch panel IDs for multiple organizations
  async getBatchOrganizationsPanelIds(organizationIds) {
    let allPanelIds = [];
    
    // Process each organization
    for (const orgId of organizationIds) {
      const panelIds = await this.getOrganizationPanelIds(orgId);
      allPanelIds = [...allPanelIds, ...panelIds];
    }
    
    return allPanelIds;
  }

  // Batch fetch product IDs for multiple organizations
  async getBatchOrganizationsProductIds(organizationIds) {
    let allProductIds = [];
    
    // Process each organization
    for (const orgId of organizationIds) {
      const productIds = await this.getOrganizationProductIds(orgId);
      allProductIds = [...allProductIds, ...productIds];
    }
    
    return allProductIds;
  }

  async setUserPanelIds(userId, panelIds) {
    const key = `userPanels:${userId}`;
    await this.client.setex(key, 3600, JSON.stringify(panelIds));
  }

  // Individual panel data caching
  async getPanelData(panelId) {
    const key = `panel:${panelId}`;
    let panelData = await this.client.get(key);

    if (!panelData) {
      // Cache miss: Fetch from Firestore (implement this method separately)
      console.log("Cache miss for panel: ", panelId);
      panelData = await this.firestore.fetchPanelDataFromFirestore(panelId);
      // Cache the result with a TTL of 1 hour
      await this.client.setex(key, 3600, JSON.stringify(panelData));
    } else {
      panelData = JSON.parse(panelData);
    }

    return panelData;
  }

  //generic get for any data, with a switch for different types, and handling for cache misses  
  async getAnyData(type, id){
    const key = `${type}:${id}`;
    let data = await this.client.get(key);
    if(!data){
      let _doc = await this.firestore.fetchDataFromFirestore(type, id);
      data = await _doc.data();
      data.id = _doc.id;
      // console.log(type, id, "Data: ", data);
      await this.client.setex(key, 3600, JSON.stringify(data));
    }
    return typeof data === 'string' ? JSON.parse(data) : data;
  }

  async getGroupData(type, id){
    const key = `${type}:${id}`;
    let data = await this.client.get(key);
    console.log("Getting data: ", key, data);
    if(!data){
      const query = ["createdBy", "==", id];
      let data = await this.firestore.getDocumentsByQuery(type, [query]);
      await this.client.setex(key, 3600, JSON.stringify(data));
    }
    return typeof data === 'string' ? JSON.parse(data) : data;
  }

  /**
   * Retrieves comments for a specific panel from cache or Firestore
   * @param {string} docId - The ID of the panel or product
   * @param {boolean} panel - Whether the doc is a panel
   * @returns {Promise<Array>} Array of comment objects
   * @throws {Error} If Redis operations fail or data is malformed
   */
  async getComments(docId, panel=true) {
    try {
      const key = `comments:${docId}`;
      let cachedData = await this.client.get(key);

      if (!cachedData) {
        // Cache miss - fetch from Firestore and populate cache
        let comments;
        if(panel){
          comments = await this.firestore.fetchCommentsByPanelId(docId);
        } else {
          comments = await this.firestore.fetchCommentsByProductId(docId);
        }
        if (!Array.isArray(comments)) {
          throw new Error('Invalid comments data from Firestore');
        }

        const commentIds = comments.map(comment => comment.id);
        
        // Use pipeline for all cache writes
        const pipeline = this.client.pipeline();
        
        // Store comment IDs
        pipeline.setex(key, 3600, JSON.stringify({ ids: commentIds }));
        
        // Store individual comments
        comments.forEach(comment => {
          pipeline.setex(
            `comments:${docId}:${comment.id}`, 
            3600, 
            JSON.stringify(comment)
          );
        });

        await pipeline.exec();
        return comments;
      }

      // Cache hit - fetch all individual comments
      let { ids: commentIds } = JSON.parse(cachedData);
      if (!Array.isArray(commentIds)) {
        throw new Error('Invalid comment IDs format in cache');
      }

      if (commentIds.length === 0) {
        return [];
      }

      const pipeline = this.client.pipeline();
      commentIds.forEach(id => {
        pipeline.get(`comments:${docId}:${id}`);
      });

      const results = await pipeline.exec();
      const comments = results
        .map(([err, data]) => {
          if (err) {
            console.error('Error fetching comment:', err);
            return null;
          }
          try {
            return JSON.parse(data);
          } catch (e) {
            console.error('Error parsing comment data:', e);
            return null;
          }
        })
        .filter(comment => comment !== null); // Remove any failed comments

      return comments;

    } catch (error) {
      console.error(`Error in getCommentsByDocId for doc ${docId}:`, error);
      throw new Error(`Failed to fetch comments: ${error.message}`);
    }
  }

  async getCommentsByPanelId(panelId){
    return await this.getComments(panelId, true);
  }

  async getCommentsByProductId(productId){
    return await this.getComments(productId, false);
  }

  //generic set for any data, with a switch for different types
  async setAnyData(key, data){
    console.log(key,data);
    await this.client.setex(key, 3600, JSON.stringify(data));
  }

  // Add a panel to an organization's list of panels in Redis
  async addPanelToOrganization(organizationId, panelId) {
    const key = `orgPanels:${organizationId}:${panelId}`;
    await this.client.setex(key, 3600, '1');
  }

  async addProductToOrganization(organizationId, productId) {
    const key = `orgProducts:${organizationId}:${productId}`;
    await this.client.setex(key, 3600, '1');
  }

  async setPanelData(panelId, panelData) {
    const key = `panel:${panelId}`;
    await this.client.setex(key, 3600, JSON.stringify(panelData));
  }

  async setOrganizationData(orgId, orgData){
    const key = `org:${orgId}`;
    await this.client.setex(key, 3600, JSON.stringify(orgData));
  }

  async getOrganizationData(orgId){
    const key = `org:${orgId}`;
    let orgData = await this.client.get(key);
    return JSON.parse(orgData)
  }

  async setUserOrganizationData(orgId, orgData){
    const key = `userOrg:${orgId}`;
    await this.client.setex(key, 3600, JSON.stringify(orgData));
  }

  async getUserOrganizationData(orgId){
    const key = `userOrg:${orgId}`;
    let orgData = await this.client.get(key);
    return JSON.parse(orgData)
  }

  async getCompletedPanels(userId) {
    const key = `completedPanels:${userId}`;
    let panelIds = await this.client.smembers(key);
    if(panelIds.length === 0){
      const panels = await this.firestore.fetchCompletedPanels(userId);
      panelIds =  [];
      panels.forEach(panel => {
        panelIds.push(panel.id);
        this.setAnyData(`completedPanels:${userId}:${panel.id}`, panel);
      });
      if(panelIds.length > 0){
        await this.client.sadd(key, ...panelIds);
        await this.client.expire(key, 3600);
      }
    }
    return panelIds;
  }

  async getCompletedPanel(userId, panelId) {
    const key = `completedPanels:${userId}:${panelId}`;
    let panel = await this.client.get(key);
    if(!panel){
      panel = await this.firestore.fetchCompletedPanel(userId, panelId);
      await this.client.setex(key, 3600, JSON.stringify(panel));
    }
    return typeof panel === 'string' ? JSON.parse(panel) : panel;
  }

  async setUser(userId, userData) {
    const key = `user:${userId}`;
    await this.client.setex(key, 3600, JSON.stringify(userData));
  }

  // Update user's panel access
  async updateUserPanelAccess(userId, newPanelIds) {
    await this.setUserPanelIds(userId, newPanelIds);
  }

  // Delete panel from cache
  async deletePanel(panelId) {
    try {
      // Get panel data first to find the organization ID
      const panelData = await this.getPanelData(panelId);
      const organizationId = panelData?.organization_id;

      const keysToDelete = [
        `panel:${panelId}`,                // Main panel data
        `comments:${panelId}`,             // Panel comments list
        `comments:${panelId}:*`,           // Individual comments
        `*Panels:*:${panelId}`,            // User relationships (in-progress and completed)
      ];

      if (organizationId) {
        keysToDelete.push(`orgPanels:${organizationId}:*`);  // Organization relationships
      }

      await this.deleteKeysByPattern(keysToDelete);
      console.log(`Successfully deleted panel ${panelId} from cache`);
    } catch (error) {
      console.error(`Error deleting panel ${panelId} from cache:`, error);
      throw error;
    }
  }

  async deleteProduct(productId) {
    try {
      // Get product data first to find the organization ID
      const productData = await this.getProductData(productId);
      const organizationId = productData?.organization_id;

      const keysToDelete = [
        `product:${productId}`,            // Main product data
        `comments:${productId}`,           // Product comments list
        `comments:${productId}:*`,         // Individual comments
        `panelCount:${productId}`,         // Panel count
      ];

      if (organizationId) {
        keysToDelete.push(`orgProducts:${organizationId}:*`);  // Organization relationships
      }

      await this.deleteKeysByPattern(keysToDelete);
      console.log(`Successfully deleted product ${productId} from cache`);
    } catch (error) {
      console.error(`Error deleting product ${productId} from cache:`, error);
      throw error;
    }
  }

  async deleteOrganizationCache(organizationId) {
    try {
      await this.deleteKeysByPattern([
        `orgPanels:${organizationId}:*`,   // Organization panels
        `orgProducts:${organizationId}:*`,  // Organization products
      ]);
      
      console.log(`Successfully deleted cache for organization ${organizationId}`);
    } catch (error) {
      console.error(`Error deleting organization ${organizationId} cache:`, error);
      throw error;
    }
  }

  async deleteUserCache(userId) {
    try {
      await this.deleteKeysByPattern([
        `user:${userId}`,
        `userPanels:${userId}`,
        `completedPanels:${userId}`,
        `completedPanels:${userId}:*`,
        `progressPanels:${userId}:*`
      ]);
      
      console.log(`Successfully deleted cache for user ${userId}`);
    } catch (error) {
      console.error(`Error deleting user ${userId} cache:`, error);
      throw error;
    }
  }

  async deleteCollectionCache(collection) {
    try {
      await this.deleteKeysByPattern(`${collection}:*`);
      console.log(`Successfully deleted cache for collection ${collection}`);
    } catch (error) {
      console.error(`Error deleting collection ${collection} cache:`, error);
      throw error;
    }
  }

  async getCacheKeys(pattern) {
    let keys = [];
    let cursor = '0';
    
    do {
      const [nextCursor, scanKeys] = await this.client.scan(
        cursor,
        'MATCH',
        pattern,
        'COUNT',
        100
      );
      cursor = nextCursor;
      keys.push(...scanKeys);
    } while (cursor !== '0');
    
    return keys;
  }

  async getCacheStats() {
    const info = await this.client.info();
    const dbSize = await this.client.dbsize();
    const keyPatterns = [
      'user:*',
      'panel:*',
      'product:*',
      'comments:*',
      'userPanels:*',
      'completedPanels:*',
      'orgPanels:*',
      'orgProducts:*'
    ];

    const patternCounts = await Promise.all(
      keyPatterns.map(async pattern => {
        const keys = await this.getCacheKeys(pattern);
        return { pattern, count: keys.length };
      })
    );

    return {
      dbSize,
      patternCounts,
      info
    };
  }

  async bulkDelete(keys) {
    if (keys.length === 0) return;
    return this.client.del(...keys);
  }

  async clearAllUserData() {
    const patterns = [
      'user:*',
      'userPanels:*',
      'completedPanels:*',
      'progressPanels:*'
    ];
    await Promise.all(patterns.map(pattern => this.deleteKeysByPattern(pattern)));
  }

  async deleteKeysByPattern(patterns) {
    try {
      const pipeline = this.client.pipeline();
      
      // Handle single pattern or array of patterns
      const patternArray = Array.isArray(patterns) ? patterns : [patterns];
      
      for (const pattern of patternArray) {
        let cursor = '0';
        do {
          const [nextCursor, keys] = await this.client.scan(
            cursor,
            'MATCH',
            pattern,
            'COUNT',
            100
          );
          cursor = nextCursor;
          if (keys.length > 0) {
            pipeline.del(...keys);
          }
        } while (cursor !== '0');
      }
      
      await pipeline.exec();
    } catch (error) {
      console.error('Error deleting keys by pattern:', error);
      throw error;
    }
  }

  async getProjectData(projectId) {
    return await this.getAnyData('projects', projectId);
  }

  async setProjectData(projectId, projectData) {
    const key = `project:${projectId}`;
    await this.client.setex(key, 3600, JSON.stringify(projectData));
  }

  async deleteProjectData(projectId) {
    // Delete only the specific project data key
    const key = `project:${projectId}`;
    await this.client.del(key);
  }

  async addProjectToOrganization(organizationId, projectId) {
    await this.client.setex(`orgProjects:${organizationId}:${projectId}`, 3600, '1');
  }

  async getOrganizationProjectIds(organizationId) {
    const pattern = `orgProjects:${organizationId}:*`;
    let projectKeys = [];
    let cursor = '0';
    
    do {
      const [nextCursor, keys] = await this.client.scan(cursor, 'MATCH', pattern, 'COUNT', 100);
      cursor = nextCursor;
      projectKeys.push(...keys);
    } while (cursor !== '0');
    
    if (projectKeys.length === 0) {
      const projects = await this.firestore.fetchOrganizationProjectsFromFirestore(organizationId);
      if (projects.length > 0) {
        const pipeline = this.client.pipeline();
        projects.forEach(project => {
          pipeline.setex(`orgProjects:${organizationId}:${project.id}`, 3600, '1');
          this.setProjectData(project.id, project);
        });
        await pipeline.exec();
        projectKeys = projects.map(project => `orgProjects:${organizationId}:${project.id}`);
      }
    }
    
    return projectKeys.map(key => key.split(':')[2]);
  }

  async deleteProject(projectId) {
    // Delete project data
    const projectKey = `project:${projectId}`;
    await this.client.del(projectKey);

    // Find and delete organization-project associations
    const orgProjectPattern = `orgProjects:*:${projectId}`;
    const orgProjectKeys = await this.getCacheKeys(orgProjectPattern);
    if (orgProjectKeys.length > 0) {
      await this.bulkDelete(orgProjectKeys);
    }

    // Delete any other project-related cache entries
    const projectRelatedPattern = `*Project*:${projectId}*`;
    const relatedKeys = await this.getCacheKeys(projectRelatedPattern);
    if (relatedKeys.length > 0) {
      await this.bulkDelete(relatedKeys);
    }
  }
}

module.exports = RedisCache;
