const PanelService = require('./panels');
const UserService = require('./user');
const ProductService = require('./products');
const OrganizationService = require('./organizations');
const ProjectService = require('./projects');
const GroupService = require('./groups');
const ResponseService = require('./responses');
const QuestionService = require('./questions');
const PanelTemplateService = require('./panel-templates');


module.exports = {
    PanelService: new PanelService(),
    UserService: new UserService(),
    ProductService,
    OrganizationService,
    ProjectService: new ProjectService(),
    GroupService,
    ResponseService: new ResponseService(),
    QuestionService: new QuestionService(),
    PanelTemplateService: new PanelTemplateService()
}