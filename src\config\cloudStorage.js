const { Storage } = require('@google-cloud/storage');
const crypto = require('crypto');
const admin = require('firebase-admin');

class CloudStorageModule {
  constructor() {
    // Use the existing Firebase Admin app credentials
    const serviceAccount = require(process.env.FIREBASE_SERVICE_ACCOUNT_PATH);
    this.storage = new Storage({
      credential: admin.credential.cert(serviceAccount)
    });
    this.bucketName = process.env.FIREBASE_STORAGE_BUCKET;
    this.bucket = this.storage.bucket(this.bucketName);
  }

  /**
   * Uploads a base64 image to Google Cloud Storage
   * @param {string} base64Image - The base64 image string (including data URL prefix)
   * @param {string} prefix - Optional prefix for the file path (e.g., 'products/')
   * @returns {Promise<string>} The public URL of the uploaded image
   */
  async uploadImage(base64Image, prefix = 'product-images/') {
    try {
      // Remove the data URL prefix (e.g., "data:image/jpeg;base64,")
      const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');

      // Generate a unique filename
      const fileExtension = this.getImageExtension(base64Image);
      const fileName = `${prefix}${crypto.randomUUID()}${fileExtension}`;

      // Create a new blob in the bucket
      const file = this.bucket.file(fileName);

      // Upload the image buffer
      await file.save(imageBuffer, {
        contentType: this.getContentType(base64Image),
        public: true,
        validation: 'md5'
      });

      // Get the public URL
      return `https://storage.googleapis.com/${this.bucketName}/${fileName}`;

    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image to Cloud Storage');
    }
  }

  /**
   * Extracts the file extension from the base64 image
   * @private
   */
  getImageExtension(base64Image) {
    const match = base64Image.match(/^data:image\/(\w+);base64,/);
    if (!match) return '.jpg';

    const mimeType = match[1].toLowerCase();
    switch (mimeType) {
      case 'jpeg':
      case 'jpg':
        return '.jpg';
      case 'png':
        return '.png';
      case 'gif':
        return '.gif';
      case 'webp':
        return '.webp';
      default:
        return '.jpg';
    }
  }

  /**
   * Gets the content type from the base64 image
   * @private
   */
  getContentType(base64Image) {
    const match = base64Image.match(/^data:image\/(\w+);base64,/);
    if (!match) return 'image/jpeg';
    return `image/${match[1].toLowerCase()}`;
  }
}

module.exports = CloudStorageModule;
