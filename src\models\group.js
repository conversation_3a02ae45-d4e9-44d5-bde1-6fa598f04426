const Joi = require("joi");
const Table = require("cli-table3");

/**
 * @swagger
 * components:
 *   schemas:
 *     Group:
 *       type: object
 *       required:
 *         - name
 *         - description
 *         - createdBy
 *         - users
 *         - panels
 *         - samples
 *         - organization_id
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: Display name of the group
 *         description:
 *           type: string
 *           minLength: 2
 *           maxLength: 500
 *           description: Description of the group
 *         createdBy:
 *           type: string
 *           description: ID of the user that created the group
 *         users:
 *           type: array
 *           items:
 *             type: string
 *           description: List of user IDs that are in this group
 *         panels:
 *           type: array
 *           items:
 *             type: string
 *           description: List of panel IDs that are in this group
 *         samples:
 *           type: array
 *           items:
 *             type: string
 *           description: List of sample IDs that are in this group
 *         organization_id:
 *           type: string
 *           description: ID of the organization that owns this group
 */

// Define group schema using Joi for robust validation
const groupSchema = Joi.object({
  name: Joi.string()
    .required()
    .min(2)
    .max(100)
    .description("Display name of group"),

  description: Joi.string()
    .required()
    .min(2)
    .max(500)
    .description("Description of the group"),

  createdBy: Joi.string()
    .required()
    .description("ID of the user that created the group"),

  users: Joi.array()
    .items(Joi.string())
    .required()
    .description("List of user IDs that are in this group"),

  panels: Joi.array()
    .items(Joi.string())
    .required()
    .description("List of panel IDs that are in this group"),

  samples: Joi.array()
    .items(Joi.string())
    .required()
    .description("List of sample IDs that are in this group"),

  organization_id: Joi.string()
    .required()
    .description("ID of the organization that owns this group"),
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake panel document for testing
const generateFakeGroup = (overrides = {}) => {

    let id = overrides.organization_id || faker.string.alphanumeric(20);
    const fakeGroup={
        name: faker.company.name(),
        description: faker.lorem.sentence(),
        createdBy: faker.string.alphanumeric(20),
        users: [faker.string.alphanumeric(20)],
        panels:[faker.string.alphanumeric(20)],
        samples: [faker.string.alphanumeric(20)],
        organization_id: id,
        ...overrides
    }
    
      // Validate the generated data
      const { error } = validateGroup(fakeGroup);
      if (error) {
        throw new Error(`Generated invalid group: ${error.message}`);
      }
    
      return fakeGroup;
}

// Generate multiple fake groups
const generateFakeGroups = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeGroup(overrides));
};

// Validate group document against schema
const validateGroup = (group) => {
  return groupSchema.validate(group, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};


// Add this new validation function for updates
const validateGroupUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(groupSchema.describe().keys).reduce((acc, [key, value]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};


// Convert Firestore document to group model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateGroup(data);
  
  if (error) {
    throw new Error(`Invalid group data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert group model to Firestore document
const toFirestore = (group) => {
  const { value, error } = validateGroup(group);
  
  if (error) {
    throw new Error(`Invalid group data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
    groupSchema,
    generateFakeGroup,
    generateFakeGroups,
    validateGroup,
    validateGroupUpdate,
    fromFirestore,
    toFirestore
}
