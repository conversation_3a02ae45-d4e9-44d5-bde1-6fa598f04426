// src/services/userService.js
const UserRepository = require('../repositories/user');
const PanelRepository = require('../repositories/panels');

class UserService {

    constructor() {
        this.userRepository = UserRepository;
        this.panelRepository = PanelRepository;
    }

    async getUserById(userId) {
        return await UserRepository.getUserById(userId);
    }
    
    async createUser(userData) {
        // Additional business logic before creating user
        // e.g., check if email already exists, format data, etc.
        return await this.userRepository.create(userData);
    }
    
    async updateUser(userId, updateData) {
        // Additional validation or business logic
        return await this.userRepository.update(userId, updateData);
    }
    
    async deleteUser(userId) {
        // Additional cleanup logic
        return await this.userRepository.delete(userId);
    }
    
    async getUsersByOrganization(organizationId) {
        return await this.userRepository.getUsersByOrganization(organizationId);
    }
    
    async assignPanelsToUser(userId, panelIds) {
        // Validate that all panels exist
        // Check if user has permission to access these panels
        // Any other business logic
        
        return await this.userRepository.updateUserPanelAccess(userId, panelIds);
    }
    
    async addUserToOrganization(userId, organizationId, options = {}) {
        // Validate inputs
        if (!userId) throw new Error('User ID is required');
        if (!organizationId) throw new Error('Organization ID is required');
        
        // Check if organization exists
        // This would require an organizationRepository, but we'll assume it exists
        // const organizationExists = await organizationRepository.exists(organizationId);
        // if (!organizationExists) throw new Error('Organization not found');
        
        // Check if user exists
        const user = await this.userRepository.getUserById(userId);
        if (!user) throw new Error('User not found');
        
        // Check if user already belongs to this organization
        const userOrganizations = user.organizations || [];
        if (userOrganizations.includes(organizationId)) {
            return { success: true, message: 'User already belongs to this organization' };
        }

        // Check if the requester has permission to add users to this organization
        // This would typically be handled by middleware or a permission check
        // if (!hasPermission) throw new Error('Permission denied');
        
        // Add user to organization
        const result = await this.userRepository.addUserToOrganization(userId, organizationId, user);
        
        // Additional business logic after adding user
        // if (result.success && options.sendWelcomeEmail) {
            // Send welcome email to user
            // await emailService.sendOrganizationWelcome(user.email, organizationId);
        // }
        
        // If user needs default panel access, assign them
        // if (result.success && options.assignDefaultPanels) {
            // Get default panels for organization
            // const defaultPanelIds = await panelRepository.getDefaultPanelsForOrganization(organizationId);
            // await this.assignPanelsToUser(userId, defaultPanelIds);
        // }
        
        return result;
    }
}

module.exports = UserService;