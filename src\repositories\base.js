const { redisCache, firestoreModule } = require('../config');

class BaseRepository {
    constructor() {
        this.redisCache = redisCache;
        this.firestoreModule = firestoreModule;
    }

    /**
     * Utility method to perform atomic updates while ensuring cache consistency
     * @param {string} collection - The Firestore collection name
     * @param {string} documentId - The document ID
     * @param {Object} atomicUpdate - The atomic update object including Firestore FieldValue operations
     * @param {string} cacheKey - The Redis cache key to invalidate
     * @param {Function} cacheUpdateFn - Function to update the cache (receives updated document)
     * @returns {Promise<Object>} - The updated document
     */
    async performAtomicUpdate(collection, documentId, atomicUpdate, cacheKey, cacheUpdateFn) {
        try {
            // 1. Perform atomic update in Firestore
            await this.firestoreModule.updateDocument(collection, documentId, atomicUpdate);
            
            // 2. Invalidate cache
            if (cacheKey) {
                await this.redisCache.client.del(cacheKey);
            }
            
            // 3. Fetch fresh data from Firestore
            const updatedDoc = await this.firestoreModule.getDocument(collection, documentId);
            
            // 4. Update cache with fresh data if needed
            if (cacheKey && cacheUpdateFn) {
                await cacheUpdateFn(updatedDoc);
            }
            
            return updatedDoc;
        } catch (error) {
            console.error(`Error performing atomic update: ${error.message}`);
            throw error;
        }
    }
}

module.exports = BaseRepository;