const { validateOption } = require('../models/option');
const BaseRepository = require('./base');

class OptionRepository extends BaseRepository {
  constructor() {
    super();
  }

  /**
   * Create a new option in the database
   * @param {Object} optionData - The option data to create
   * @returns {Promise<string>} - The ID of the created option
   */
  async create(optionData) {
    const { error } = validateOption(optionData);
    if (error) {
      throw new Error(`Invalid option data: ${error.message}`);
    }

    // Generate an ID if not provided
    if (!optionData.option_id) {
      optionData.option_id = await this.firestoreModule.getDocumentKey('options');
    }

    // Create option in Firestore
    await this.firestoreModule.createDocument(
      "options",
      optionData,
      optionData.option_id
    );

    // Cache the new option data
    const cacheKey = `option:${optionData.option_id}`;
    await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(optionData));

    // Invalidate question options cache if this option is tied to a question
    if (optionData.question_id) {
      const questionOptionsKey = `question:${optionData.question_id}:options`;
      await this.redisCache.client.del(questionOptionsKey);
    }

    return optionData.option_id;
  }

  /**
   * Create multiple options in a batch operation
   * @param {Array<Object>} optionsArray - Array of option data to create
   * @returns {Promise<Array<string>>} - Array of created option IDs
   */
  async batchCreate(optionsArray) {
    if (!Array.isArray(optionsArray) || optionsArray.length === 0) {
      throw new Error('Options array is empty or invalid');
    }

    // Validate all options first
    for (const option of optionsArray) {
      const { error } = validateOption(option);
      if (error) {
        throw new Error(`Invalid option data: ${error.message}`);
      }
      // Generate IDs for options that don't have one
      if (!option.option_id) {
        option.option_id = await this.firestoreModule.getDocumentKey('options');
      }
    }

    // Create all options in a batch
    await this.firestoreModule.batchCreateDocuments('options', optionsArray, 'option_id');

    // Cache all options
    const pipeline = this.redisCache.client.pipeline();
    for (const option of optionsArray) {
      const cacheKey = `option:${option.option_id}`;
      pipeline.setex(cacheKey, 3600, JSON.stringify(option));
    }
    await pipeline.exec();

    // Invalidate question options cache for all affected questions
    const questionIds = [...new Set(optionsArray.map(option => option.question_id).filter(id => id))];
    if (questionIds.length > 0) {
      const pipeline = this.redisCache.client.pipeline();
      for (const questionId of questionIds) {
        pipeline.del(`question:${questionId}:options`);
      }
      await pipeline.exec();
    }

    return optionsArray.map(option => option.option_id);
  }

  /**
   * Get an option by ID
   * @param {string} optionId - The ID of the option to retrieve
   * @returns {Promise<Object>} - The option data
   */
  async read(optionId) {
    const cacheKey = `option:${optionId}`;
    let optionData = await this.redisCache.client.get(cacheKey);

    if (!optionData) {
      // Cache miss: Fetch from Firestore
      optionData = await this.firestoreModule.getDocument('options', optionId);
      
      if (!optionData) {
        throw new Error('Option not found');
      }
      
      // Cache the result with a TTL of 1 hour
      await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(optionData));
    } else {
      optionData = JSON.parse(optionData);
    }
    
    return optionData;
  }

  /**
   * Update an option
   * @param {string} optionId - The ID of the option to update
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object>} - The updated option
   */
  async update(optionId, updateData) {
    // We don't have an update validator for options, so we'll skip validation for now
    
    // Update in Firestore
    await this.firestoreModule.updateDocument("options", optionId, updateData);

    // Invalidate cache
    const cacheKey = `option:${optionId}`;
    await this.redisCache.client.del(cacheKey);

    // Get the option to check if it has a question ID
    const option = await this.firestoreModule.getDocument("options", optionId);
    
    // Invalidate question options cache if this option is tied to a question
    if (option && option.question_id) {
      const questionOptionsKey = `question:${option.question_id}:options`;
      await this.redisCache.client.del(questionOptionsKey);
    }

    // Update cache with new data
    await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(option));

    return option;
  }

  /**
   * Delete an option (soft delete)
   * @param {string} optionId - The ID of the option to delete
   */
  async delete(optionId) {
    // Get the option first to get the question_id
    const option = await this.read(optionId);
    
    await this.firestoreModule.markDeleted("options", optionId);
    
    // Invalidate cache
    const cacheKey = `option:${optionId}`;
    await this.redisCache.client.del(cacheKey);
    
    // Invalidate question options cache if this option is tied to a question
    if (option && option.question_id) {
      const questionOptionsKey = `question:${option.question_id}:options`;
      await this.redisCache.client.del(questionOptionsKey);
    }
  }

  /**
   * Get all options for a question
   * @param {string} questionId - The question ID
   * @returns {Promise<Array>} - Array of options
   */
  async getOptionsByQuestion(questionId) {
    const cacheKey = `question:${questionId}:options`;
    let options = await this.redisCache.client.get(cacheKey);

    if (!options) {
      // Cache miss: Fetch from Firestore
      const conditions = [
        ["question_id", "==", questionId],
        ["deleted", "!=", true]
      ];
      
      options = await this.firestoreModule.queryDocuments("options", conditions);
      
      // Cache the results
      if (options.length > 0) {
        await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(options));
      } else {
        // Cache empty results for a shorter time
        await this.redisCache.client.setex(cacheKey, 300, JSON.stringify([]));
        options = [];
      }
    } else {
      options = JSON.parse(options);
    }
    
    return options;
  }

  /**
   * Get options by their IDs in a batch
   * @param {Array<string>} optionIds - Array of option IDs to retrieve
   * @returns {Promise<Array<Object>>} - Array of option objects
   */
  async batchGetOptions(optionIds) {
    if (!optionIds || optionIds.length === 0) {
      return [];
    }

    // Check cache first
    const pipeline = this.redisCache.client.pipeline();
    optionIds.forEach(id => {
      const key = `option:${id}`;
      pipeline.get(key);
    });

    const results = await pipeline.exec();
    const cachedOptions = [];
    const missingIds = [];

    // Process cache results
    results.forEach((result, index) => {
      const [error, value] = result;
      if (!error && value) {
        cachedOptions.push(JSON.parse(value));
      } else {
        missingIds.push(optionIds[index]);
      }
    });

    // If all options were in cache, return them
    if (missingIds.length === 0) {
      return cachedOptions;
    }

    // Fetch missing options from Firestore in batches (Firestore has a limit for 'in' queries)
    const MAX_BATCH_SIZE = 10;
    let firestoreOptions = [];

    for (let i = 0; i < missingIds.length; i += MAX_BATCH_SIZE) {
      const batch = missingIds.slice(i, i + MAX_BATCH_SIZE);

      const conditions = [["option_id", "in", batch]];
      const fetchedOptions = await this.firestoreModule.queryDocuments("options", conditions);

      // Cache the fetched options
      const cachePipeline = this.redisCache.client.pipeline();
      fetchedOptions.forEach(option => {
        const key = `option:${option.option_id}`;
        cachePipeline.setex(key, 3600, JSON.stringify(option));
      });
      await cachePipeline.exec();

      firestoreOptions = [...firestoreOptions, ...fetchedOptions];
    }

    // Combine cached and freshly fetched options
    return [...cachedOptions, ...firestoreOptions];
  }
}

module.exports = OptionRepository; 