const FirestoreModule = require('./db');
const RedisCache = require('./redis');
const FirebaseListeners = require('./listeners');
const CloudStorage = require ('../config/cloudStorage');

// Initialize Firestore first since Redis depends on it
const firestoreInstance = FirestoreModule;

// Initialize Redis with Firestore instance
const redisInstance = new RedisCache(firestoreInstance);
const listenersInstance = new FirebaseListeners(firestoreInstance);
listenersInstance.initializeListeners();
const cloudStorageInstance = new CloudStorage();

module.exports = {
  firestoreModule: firestoreInstance,
  redisCache: redisInstance,
  firebaseListeners: listenersInstance,
  cloudStorage: cloudStorageInstance
};
