const { ProductRepository } = require('../repositories');

async function transferProduct(productId, newOrganizationId){
    try {
        // Create an instance of ProductRepository
        const productRepo = new ProductRepository();
        
        // Get the product data
        const product = await productRepo.read(productId);
        
        // Update the product owner
        const updatedProduct = await productRepo.update(productId, { organization_id: newOrganizationId });
    
        return true;
    } catch (error) {
        console.error('Error transferring product:', error);
        throw new Error('Error transferring product');
    }
}

module.exports = transferProduct; 