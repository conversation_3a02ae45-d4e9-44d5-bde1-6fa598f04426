const Joi = require('joi');

// Define demographic schema using <PERSON><PERSON> for robust validation
const demographicSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the demographic option'),
    
  type: Joi.string()
    .valid('ethnicity', 'age-group', 'income', 'gender', 'missing', 'hispanic')
    .required()
    .description('Type of the demographic option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the demographic'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the demographic option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake demographic document for testing
const generateFakeDemographic = (overrides = {}) => {
  const fakeDemographic = {
    name: faker.lorem.words(3),
    type: faker.helpers.arrayElement(['ethnicity', 'age-group', 'income', 'gender', 'missing', 'hispanic']),
    priority: faker.number.int({ min: 1, max: 100 }),
    option_id: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateDemographic(fakeDemographic);
  if (error) {
    throw new Error(`Generated invalid demographic: ${error.message}`);
  }

  return fakeDemographic;
};

// Generate multiple fake demographics
const generateFakeDemographics = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeDemographic(overrides));
};

// Validate demographic document against schema
const validateDemographic = (demographic) => {
  return demographicSchema.validate(demographic, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to demographic model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateDemographic(data);
  
  if (error) {
    throw new Error(`Invalid demographic data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert demographic model to Firestore document
const toFirestore = (demographic) => {
  const { value, error } = validateDemographic(demographic);
  
  if (error) {
    throw new Error(`Invalid demographic data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  demographicSchema,
  generateFakeDemographic,
  generateFakeDemographics,
  validateDemographic,
  fromFirestore,
  toFirestore
};
