const Joi = require('joi');

// Define metric schema using <PERSON><PERSON> for robust validation
const metricSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the metric option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the metric'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the metric option'),

  labels: Joi.array()
    .items(Joi.string().valid('poor', 'excellent', 'not enough', 'just right', 'too much', 'not satisfied', 'satisfied', '0', '100'))
    .required()
    .description('Labels for the metric option'),

  configuration: Joi.array()
    .items(Joi.string().valid('2', '3', 'ticker'))
    .required()
    .description('Configuration for the metric option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake metric document for testing
const generateFakeMetric = (overrides = {}) => {
  const fakeMetric = {
    name: faker.lorem.words(3),
    priority: faker.number.int({ min: 1, max: 100 }),
    option_id: faker.string.alphanumeric(20),
    labels: faker.helpers.arrayElements(['poor', 'excellent', 'not enough', 'just right', 'too much', 'not satisfied', 'satisfied', '0', '100'], 2),
    configuration: faker.helpers.arrayElements(['2', '3', 'ticker'], 1),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateMetric(fakeMetric);
  if (error) {
    throw new Error(`Generated invalid metric: ${error.message}`);
  }

  return fakeMetric;
};

// Generate multiple fake metrics
const generateFakeMetrics = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeMetric(overrides));
};

// Validate metric document against schema
const validateMetric = (metric) => {
  return metricSchema.validate(metric, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to metric model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateMetric(data);
  
  if (error) {
    throw new Error(`Invalid metric data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert metric model to Firestore document
const toFirestore = (metric) => {
  const { value, error } = validateMetric(metric);
  
  if (error) {
    throw new Error(`Invalid metric data for Firestore: ${error.message}`);
  }
  
  return value;
};


module.exports = {
  metricSchema,
  generateFakeMetric,
  generateFakeMetrics,
  validateMetric,
  fromFirestore,
  toFirestore
};