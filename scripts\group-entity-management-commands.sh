#!/bin/bash

# This script contains curl commands to test the group entity management functionality
# Replace the placeholders with actual values before running

TOKEN="YOUR_AUTH_TOKEN_HERE"
API_URL="http://localhost:7890"
GROUP_ID="YOUR_GROUP_ID"
USER_ID="YOUR_USER_ID"
PANEL_ID="YOUR_PANEL_ID"
SAMPLE_ID="YOUR_SAMPLE_ID"

echo "==== Group Entity Management Test Commands ===="
echo ""

echo "=== User Management ==="
echo ""

echo "1. Add a user to a group"
echo "curl -X POST ${API_URL}/groups/${GROUP_ID}/users/${USER_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "2. Remove a user from a group"
echo "curl -X DELETE ${API_URL}/groups/${GROUP_ID}/users/${USER_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "=== Panel Management ==="
echo ""

echo "3. Add a panel to a group"
echo "curl -X POST ${API_URL}/groups/${GROUP_ID}/panels/${PANEL_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "4. Remove a panel from a group"
echo "curl -X DELETE ${API_URL}/groups/${GROUP_ID}/panels/${PANEL_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "=== Sample Management ==="
echo ""

echo "5. Add a sample to a group"
echo "curl -X POST ${API_URL}/groups/${GROUP_ID}/samples/${SAMPLE_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "6. Remove a sample from a group"
echo "curl -X DELETE ${API_URL}/groups/${GROUP_ID}/samples/${SAMPLE_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "7. Get group details after changes"
echo "curl -X GET ${API_URL}/groups/${GROUP_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "8. Check projects affected by group changes"
echo "curl -X POST ${API_URL}/projects/sync/group/${GROUP_ID}?refresh_status=true \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "To run these commands, replace the placeholders at the top of the script with your actual values."
echo "You can run individual commands by copying and pasting them into your terminal." 