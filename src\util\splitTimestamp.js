function splitTimestamp(isoString) {
  // Convert ISO string to Date object
  const date = new Date(isoString);
  
  // Get seconds since Unix epoch
  const seconds = Math.floor(date.getTime() / 1000);
  
  // Get remaining nanoseconds
  // Convert milliseconds to nanoseconds (1ms = 1,000,000ns)
  const nanoseconds = (date.getTime() % 1000) * 1000000;

  return {
    _seconds: seconds,
    _nanoseconds: nanoseconds
  };
}

module.exports = splitTimestamp;
