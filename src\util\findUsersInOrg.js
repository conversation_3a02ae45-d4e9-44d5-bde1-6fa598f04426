const { firestoreModule } = require('../config/index');

async function findAllUserInOrg(orgIds) {
  // Get all users first since Firestore doesn't support regex/pattern matching directly
  const snapshot = await firestoreModule.getUsers();

  let usersWithOrgData=[];
  let usersInOrg=[]

  let usersData = snapshot.docs.map(doc=>{
      let data=doc.data();
      if(data.organizations.length){
        usersWithOrgData.push({uid:data.uid,org:data.organizations})
      }
      return data;
  })

  orgIds.forEach(org=>{
      let user = usersWithOrgData.find(u=>u.org[0]===org.id)
      if(user){
        let userData = usersData.find(u=>u.uid===user.uid)
        if(userData){
          usersInOrg.push(userData)
        }
      }
  })
  
  return usersInOrg;
}

module.exports = findAllUserInOrg;
