const { redisCache } = require('../config');
//Check if the user can access the panel middleware
const canAccessProductDetails = async (req, res, next) => {
    const userId = req.user.uid;
    const product_id = req.body.product_id || req.params.product_id;
    console.log("Product ID: ", product_id);
    const user = await redisCache.getUser(userId);
    //console.log("User: ", user, userId, panel_id);
    const userOrgs = user.organizations || [];
    const product = await redisCache.getProductData(product_id);
    //console.log("Panel: ", panel);
    if(userOrgs.includes(product.organization_id)){
      req.product = product;
      req.userData = user;
      next();
    } else {
      res.status(403).send('User does not have access to this product');
    }
  }

  module.exports = canAccessProductDetails;