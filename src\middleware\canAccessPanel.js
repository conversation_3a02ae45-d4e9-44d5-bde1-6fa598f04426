const { redisCache } = require('../config');
const hydratePanel = require('../util/hydratePanel');
//Check if the user can access the panel middleware
const canAccessPanel = async (req, res, next) => {
    const userId = req.user.uid;
    const panel_id = req.body.panel_id || req.params.panel_id;
    const user = await redisCache.getUser(userId);
    //console.log("User: ", user, userId, panel_id);
    const userOrgs = user.organizations || [];
    const panel =  req.query.hydrate ? await hydratePanel(panel_id, [], [], userId) : await redisCache.getPanelData(panel_id);
    //console.log("Panel: ", panel);
    if(panel.isPublic || userOrgs.includes(panel.organization_id)){
      req.panel = panel;
      req.userData = user;
      next();
    } else {
      res.status(403).send('User does not have access to this panel');
    }
  }

  module.exports = canAccessPanel;