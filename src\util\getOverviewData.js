const { redisCache, firestoreModule } = require('../config');
const getMetricAggregateData = require('./getMetricAggregateData');

const getOverviewData = async (userId) => {
    const user = await redisCache.getUser(userId);
    const userOrg = "M5fMtSZfQVafZpNhv8o7";//user['organizations'][0]; //TODO: Make this a single "primaryOrganization" field for insights/admin users
    const metricAggregateData = await getMetricAggregateData(userOrg);
    const aggregatedPanelData = await firestoreModule.getAggregateData('panels', [
        ['organization_id', '==', userOrg],
        ['active', '==', true],
    ], {
        numPanels: firestoreModule.AggregateField.count(),
    });
    return {
        metricAggregateData,
        aggregatedPanelData
    };
    /*Get aggregated data for the organization in the following types:
      - Active Panel Count - db.collection('panels').where('organization_id', '==', userOrg).where('active', '==', true).count().get()
      - Response Count - db.collection('responses').where('organization_id', '==', userOrg).count().get()
      - Average Satisfaction Score of responses
      - Average quality score of responses
      - Average taste score of responses
      - Average Potency score of responses
    */
    /* Also need to figure out how to handle the following:
      - Responses over time
      - Demographic overview
      - Aroma Distribution across all products
    */
}

module.exports = getOverviewData;