/**
 * @swagger
 * components:
 *   schemas:
 *     Question:
 *       type: object
 *       required:
 *         - name
 *         - organization_id
 *         - question_id
 *         - isDefault
 *         - stepType
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the question
 *         organization_id:
 *           type: string
 *           description: ID of the organization this question belongs to
 *         question_id:
 *           type: string
 *           description: Unique identifier for the question
 *         isDefault:
 *           type: boolean
 *           description: Whether this is a default question
 *         required:
 *           type: boolean
 *           default: false
 *           description: Whether this question is required to be answered
 *         stepType:
 *           type: string
 *           description: The type of question (e.g., range-slider, multiple-choice)
 *         stepInstructions:
 *           type: string
 *           description: Instructions for the question
 *         optionIds:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of option IDs belonging to this question
 *         options:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Option'
 *           description: Array of hydrated option objects (when requested with hydration)
 *       example:
 *         name: "Aroma Evaluation"
 *         organization_id: "org123456789"
 *         question_id: "question123456789"
 *         isDefault: false
 *         required: true
 *         stepType: "range-slider"
 *         stepInstructions: "Please evaluate the aroma of the product"
 *         optionIds: ["option123", "option456", "option789"]
 */

const Joi = require('joi');

// Define question schema using Joi for robust validation
const questionSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the question'),

  organization_id: Joi.string()
    .required()
    .description('ID of the organization this question belongs to'),

  question_id: Joi.string()
    .required()
    .description('Unique identifier for the question'),

  isDefault: Joi.boolean()
    .required()
    .description('Whether this is a default question'),

  required: Joi.boolean()
    .optional()
    .default(false)
    .description('Whether this question is required'),

  stepType: Joi.string()
    .required()
    .description('The type of question'),

  stepInstructions: Joi.string()
    .optional()
    .description('Instructions for the question'),

  optionIds: Joi.array()
    .optional()
    .description('Option IDs for the question'),
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake question document for testing
const generateFakeQuestion = (overrides = {}) => {
  const fakeQuestion = {
    name: faker.lorem.words(2),
    organization_id: faker.datatype.boolean() ? faker.string.alphanumeric(20) : null,
    question_id: faker.string.alphanumeric(20),
    isDefault: faker.datatype.boolean(),
    stepType: faker.helpers.arrayElement(['range-slider', 'multiple-choice', 'text-input']),
    ...overrides
  };

  // Only add stepInstructions if not explicitly excluded in overrides
  if (!overrides.hasOwnProperty('stepInstructions')) {
    fakeQuestion.stepInstructions = faker.lorem.sentence();
  }

  // Only add optionIds if not explicitly excluded in overrides
  if (!overrides.hasOwnProperty('optionIds')) {
    fakeQuestion.optionIds = Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
      faker.string.alphanumeric(20)
    );
  }

  // Validate the generated data
  const { error } = validateQuestion(fakeQuestion);
  if (error) {
    throw new Error(`Generated invalid question: ${error.message}`);
  }

  return fakeQuestion;
};

// Generate multiple fake questions
const generateFakeQuestions = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeQuestion(overrides));
};

// Validate question document against schema
const validateQuestion = (question) => {
  return questionSchema.validate(question, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

const validateQuestionUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(questionSchema.describe().keys).reduce((acc, [key]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to question model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateQuestion(data);

  if (error) {
    throw new Error(`Invalid question data from Firestore: ${error.message}`);
  }

  return value;
};

// Convert question model to Firestore document
const toFirestore = (question) => {
  const { value, error } = validateQuestion(question);

  if (error) {
    throw new Error(`Invalid question data for Firestore: ${error.message}`);
  }

  return value;
};

module.exports = {
  questionSchema,
  generateFakeQuestion,
  generateFakeQuestions,
  validateQuestion,
  validateQuestionUpdate,
  fromFirestore,
  toFirestore
};
