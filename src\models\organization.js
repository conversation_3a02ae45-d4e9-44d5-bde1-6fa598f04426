const Joi = require('joi');

/**
 * @swagger
 * components:
 *   schemas:
 *     Organization:
 *       type: object
 *       required:
 *         - organization_id
 *         - name
 *         - website
 *       properties:
 *         organization_id:
 *           type: string
 *           description: Unique identifier for the organization
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: Display name of the organization
 *         website:
 *           type: string
 *           format: uri
 *           description: Organization website URL
 *         affiliate_code:
 *           type: string
 *           pattern: ^[A-Z0-9]{4,20}$
 *           description: Unique affiliate code for the organization
 */

// Define organization schema using Joi for robust validation
const organizationSchema = Joi.object({
  organization_id: Joi.string()
    .optional()
    .allow('')
    .description('Unique identifier for the organization'),

  name: Joi.string()
    .required()
    .min(2)
    .max(100)
    .description('Display name of the organization'),

  website: Joi.string()
    .required()
    .uri()
    .description('Organization website URL'),

  affiliate_code: Joi.string()
    .optional()
    .pattern(/^[A-Z0-9]{4,20}$/)
    .description('Unique affiliate code for the organization')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake organization document for testing
const generateFakeOrganization = (overrides = {}) => {
  const fakeOrg = {
    organization_id: faker.string.alphanumeric(20),
    name: faker.company.name(),
    website: faker.internet.url(),
    ...overrides
  };

  // Only add affiliate_code if not explicitly excluded in overrides
  if (!overrides.hasOwnProperty('affiliate_code')) {
    fakeOrg.affiliate_code = faker.string.alphanumeric(8).toUpperCase();
  }

  // Validate the generated data
  const { error } = validateOrganization(fakeOrg);
  if (error) {
    throw new Error(`Generated invalid organization: ${error.message}`);
  }

  return fakeOrg;
};

// Generate multiple fake organizations
const generateFakeOrganizations = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeOrganization(overrides));
};

// Validate organization document against schema
const validateOrganization = (organization) => {
  return organizationSchema.validate(organization, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Add this new validation function for updates
const validateOrganizationUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(organizationSchema.describe().keys).reduce((acc, [key]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to organization model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateOrganization(data);

  if (error) {
    throw new Error(`Invalid organization data from Firestore: ${error.message}`);
  }

  return value;
};

// Convert organization model to Firestore document
const toFirestore = (organization) => {
  const { value, error } = validateOrganization(organization);

  if (error) {
    throw new Error(`Invalid organization data for Firestore: ${error.message}`);
  }

  return value;
};

module.exports = {
  organizationSchema,
  generateFakeOrganization,
  generateFakeOrganizations,
  validateOrganization,
  validateOrganizationUpdate,
  fromFirestore,
  toFirestore
};

