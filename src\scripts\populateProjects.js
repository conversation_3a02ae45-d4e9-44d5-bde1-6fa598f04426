#!/usr/bin/env node

require('dotenv').config();
const { Command } = require('commander');
const ProjectGenerator = require('./dataGenerators/projectGenerator');
const { firestoreModule } = require('../config');

// Dynamic import for inquirer (ESM module)
let inquirer;

const program = new Command();

program
  .name('populate-projects')
  .description('Generate and populate project data for development')
  .version('1.0.0');

program
  .option('-c, --count <number>', 'Number of projects to generate', '2')
  .option('-o, --organization <id>', 'Specific organization ID to use')
  .option('-u, --user <id>', 'Specific user ID to use as owner')
  .option('-f, --force', 'Skip confirmation prompts', false)
  .option('-v, --verbose', 'Enable verbose output', false);

program.parse(process.argv);

const options = program.opts();

async function selectOrganization() {
  const generator = new ProjectGenerator();
  const organizations = await generator.fetchOrganizations();
  
  if (organizations.length === 0) {
    console.error('No organizations found!');
    process.exit(1);
  }
  
  const choices = organizations.map(org => ({
    name: `${org.name} (${org.organization_id || org.id})`,
    value: org
  }));
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { selectedOrg } = await inquirer.prompt([
    {
      type: 'list',
      name: 'selectedOrg',
      message: 'Select an organization:',
      choices
    }
  ]);
  
  return selectedOrg;
}

async function selectUser(organizationId) {
  const generator = new ProjectGenerator();
  const users = await generator.fetchOrganizationUsers(organizationId);
  
  if (users.length === 0) {
    console.error('No users found for this organization!');
    process.exit(1);
  }
  
  const choices = users.map(user => ({
    name: `${user.first_name} ${user.last_name} (${user.uid})`,
    value: user
  }));
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { selectedUser } = await inquirer.prompt([
    {
      type: 'list',
      name: 'selectedUser',
      message: 'Select an owner:',
      choices
    }
  ]);
  
  return selectedUser;
}

async function confirmGeneration(count) {
  if (options.force) return true;
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { confirm } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirm',
      message: `You are about to generate ${count} projects. Continue?`,
      default: false
    }
  ]);
  
  return confirm;
}

async function generateProjectsForOrganization(organization, owner, count) {
  const generator = new ProjectGenerator();
  
  // Fetch panels for this organization
  const panels = await generator.fetchOrganizationPanels(organization.organization_id || organization.id);
  if (panels.length === 0) {
    console.error('No panels found for this organization!');
    return [];
  }
  
  // Fetch all users for collaborators
  const users = await generator.fetchOrganizationUsers(organization.organization_id || organization.id);
  
  const createdProjects = [];
  
  for (let i = 0; i < count; i++) {
    // Generate collaborators
    const collaborators = generator.selectCollaborators(users, owner.uid);
    
    // Generate the project
    const status = generator.generateStatus();
    const projectData = generator.generateProject({
      organization,
      owner,
      panels,
      collaborators,
      status
    });
    
    // Add development flag to identify test data
    projectData.is_development = true;
    
    // Create the project in Firestore
    try {
      const createdProject = await generator.createProject(projectData);
      createdProjects.push(createdProject);
    } catch (error) {
      console.error(`Failed to create project ${i + 1}:`, error.message);
    }
  }
  
  return createdProjects;
}

async function generateRandomProjects(count) {
  const generator = new ProjectGenerator();
  return await generator.generateProjects(count);
}

async function cleanupProjects() {
  if (options.force) return true;
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { shouldCleanup } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'shouldCleanup',
      message: 'Do you want to delete existing development projects first?',
      default: false
    }
  ]);
  
  if (shouldCleanup) {
    console.log('Fetching existing projects...');
    // Only fetch development projects based on naming convention or tag
    // This is a safety measure to prevent accidental deletion of real data
    const projectsQuery = await firestoreModule.db.collection('projects')
      .where('is_development', '==', true)
      .get();
    
    const count = projectsQuery.size;
    
    if (count === 0) {
      console.log('No development projects found to clean up.');
      return true;
    }
    
    const { confirmCleanup } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmCleanup',
        message: `Found ${count} development projects. Delete them?`,
        default: false
      }
    ]);
    
    if (confirmCleanup) {
      console.log('Deleting development projects...');
      const batch = firestoreModule.db.batch();
      projectsQuery.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
      console.log(`Deleted ${count} development projects.`);
    }
  }
  
  return true;
}

async function main() {
  console.log('Project Generator');
  console.log('================');
  
  try {
    // Make sure inquirer is loaded before continuing
    if (!inquirer) {
      inquirer = (await import('inquirer')).default;
    }
    
    const count = parseInt(options.count, 10);
    if (isNaN(count) || count <= 0) {
      console.error('Count must be a positive number');
      return;
    }
    
    // Cleanup step
    await cleanupProjects();
    
    // Check if specific organization is specified
    if (options.organization) {
      const generator = new ProjectGenerator();
      const organizations = await generator.fetchOrganizations();
      const organization = organizations.find(org => 
        (org.organization_id || org.id) === options.organization
      );
      
      if (!organization) {
        console.error(`Organization with ID ${options.organization} not found`);
        return;
      }
      
      let owner;
      if (options.user) {
        const users = await generator.fetchOrganizationUsers(organization.organization_id || organization.id);
        owner = users.find(user => user.uid === options.user);
        if (!owner) {
          console.error(`User with ID ${options.user} not found in organization ${options.organization}`);
          return;
        }
      } else {
        owner = await selectUser(organization.organization_id || organization.id);
      }
      
      const confirmResult = await confirmGeneration(count);
      if (!confirmResult) {
        console.log('Operation cancelled.');
        return;
      }
      
      console.log(`Generating ${count} projects for organization ${organization.name}...`);
      const projects = await generateProjectsForOrganization(organization, owner, count);
      console.log(`Successfully generated ${projects.length} projects.`);
    } else {
      // Interactive mode
      const confirmResult = await confirmGeneration(count);
      if (!confirmResult) {
        console.log('Operation cancelled.');
        return;
      }
      
      // Let the user decide if they want to select a specific organization
      const { useSpecificOrg } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'useSpecificOrg',
          message: 'Do you want to select a specific organization?',
          default: true
        }
      ]);
      
      if (useSpecificOrg) {
        const organization = await selectOrganization();
        const owner = await selectUser(organization.organization_id || organization.id);
        
        console.log(`Generating ${count} projects for organization ${organization.name}...`);
        const projects = await generateProjectsForOrganization(organization, owner, count);
        console.log(`Successfully generated ${projects.length} projects.`);
      } else {
        console.log(`Generating ${count} random projects across organizations...`);
        const projects = await generateRandomProjects(count);
        console.log(`Successfully generated ${projects.length} projects.`);
      }
    }
  } catch (error) {
    console.error('Error:', error.message);
    if (options.verbose) {
      console.error(error);
    }
  }
}

// Wrap everything in an async IIFE to ensure inquirer is loaded
(async () => {
  try {
    // Load inquirer
    inquirer = (await import('inquirer')).default;
    
    // Run the main function
    await main();
  } catch (error) {
    console.error('Error:', error.message);
    if (options.verbose) {
      console.error(error);
    }
    process.exit(1);
  }
})(); 