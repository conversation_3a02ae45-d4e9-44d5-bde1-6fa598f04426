const admin = require('firebase-admin');
const dotenv = require('dotenv');

dotenv.config();

// Initialize Firebase Admin
const serviceAccount = require(process.env.UTIL_FIREBASE_SERVICE_ACCOUNT_PATH);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: process.env.FIREBASE_DATABASE_URL
});

const db = admin.firestore();

const updatePanelsWithSteps = async () => {
  try {
    // Get all panel documents
    const panelsSnapshot = await db.collection('panels').get();

    // Update each panel document
    const updatePromises = panelsSnapshot.docs.map(async (doc) => {
      const panelData = doc.data();
      
      // Only update if steps array doesn't exist
      if (!panelData.steps) {
        await db.collection('panels').doc(doc.id).update({
          steps: [
            'a9k1bpXX4H57Bdl7XhaX', // Aromas
            'wjA1uhqbIMFuZwAaCoJ8', // Metrics
            'awja0ameTALoE0D4GlWD'  // Comments
          ]
        });
      }
    });

    await Promise.all(updatePromises);
    console.log('Successfully updated all panel documents with steps array');

  } catch (error) {
    console.error('Error updating panel documents:', error);
    throw error;
  }
};


updatePanelsWithSteps();