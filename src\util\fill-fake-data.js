#!/usr/bin/env node

const admin = require('firebase-admin');
const dotenv = require('dotenv');
const fs = require('fs');
const csv = require('csv-parse/sync');
const { generateFakeOrganization, validateOrganization, organizationSchema } = require('../models/organization');
const { generateFakePanel, validatePanel, panelSchema } = require('../models/panel');
const { generateFakeProduct, validateProduct, productSchema } = require('../models/product');
const { generateFakeUser, validateUser, userSchema } = require('../models/user');
const { generateFakeFlavor, validateFlavor, flavorSchema } = require('../models/flavor');
const { generateFakeEffect, validateEffect, effectSchema } = require('../models/effect');
const { generateFakeDemographic, validateDemographic, demographicSchema } = require('../models/demographic');
const { generateFakeMetric, validateMetric, metricSchema } = require('../models/metric');
const { generateFakeQuestion, validateQuestion, questionSchema } = require('../models/question');
const { generateFakeMatrix, validateMatrix, matrixSchema } = require('../models/matrix');
const { generateFakePackaging, validatePackaging, packagingSchema } = require('../models/packaging');
const { generateFakeConsumption, validateConsumption, consumptionSchema } = require('../models/consumption');

// Load environment variables
dotenv.config();

// Initialize Firebase Admin
const serviceAccount = require("/Users/<USER>/work/abstrax/sensei/panel-registration/src/sensei.json");
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: process.env.FIREBASE_DATABASE_URL
});

const db = admin.firestore();

async function generateFakeData(collection, count, overrides = {}, csvFile = null) {
  const results = [];
  let generator, validator, id_field, schema;

  // Get the appropriate generator, validator and schema based on collection
  switch (collection) {
    case 'organizations':
      generator = generateFakeOrganization;
      validator = validateOrganization;
      schema = organizationSchema;
      id_field = 'organization_id';
      break;
    case 'users':
      generator = generateFakeUser;
      validator = validateUser;
      schema = userSchema;
      id_field = 'uid';
      break;
    case 'panels':
      generator = generateFakePanel;
      validator = validatePanel;
      schema = panelSchema;
      id_field = 'panel_id';
      break;
    case 'products':
      generator = generateFakeProduct;
      validator = validateProduct;
      schema = productSchema;
      id_field = 'product_id';
      break;
    case 'flavorOptions':
      generator = generateFakeFlavor;
      validator = validateFlavor;
      schema = flavorSchema;
      id_field = 'option_id';
      break;
    case 'effectOptions':
      generator = generateFakeEffect;
      validator = validateEffect;
      schema = effectSchema;
      id_field = 'option_id';
      break;
    case 'demographicOptions':
      generator = generateFakeDemographic;
      validator = validateDemographic;
      schema = demographicSchema;
      id_field = 'option_id';
      break;
    case 'metricOptions':
      generator = generateFakeMetric;
      validator = validateMetric;
      schema = metricSchema;
      id_field = 'option_id';
      break;
    case 'questions':
      generator = generateFakeQuestion;
      validator = validateQuestion;
      schema = questionSchema;
      id_field = 'question_id';
      break;
    case 'matrixOptions':
      generator = generateFakeMatrix;
      validator = validateMatrix;
      schema = matrixSchema;
      id_field = 'option_id';
      break;
    case 'packagingOptions':
      generator = generateFakePackaging;
      validator = validatePackaging;
      schema = packagingSchema;
      id_field = 'option_id';
      break;
    case 'consumptionOptions':
      generator = generateFakeConsumption;
      validator = validateConsumption;
      schema = consumptionSchema;
      id_field = 'option_id';
      break;
    default:
      throw new Error(`Unsupported collection: ${collection}`);
  }

  // If CSV file provided, use its data as overrides for fake data generation
  if (csvFile) {
    const fileContent = fs.readFileSync(csvFile, 'utf-8');
    const records = csv.parse(fileContent, {
      columns: true,
      skip_empty_lines: true
    });

    for (const csvRecord of records) {
      // Generate fake data first
      console.log("Record: ", csvRecord);
      const fakeData = generator({});
      console.log("Fake data: ", fakeData);
      // Pre-process array fields based on schema
      const processedRecord = {...csvRecord};
      const schemaDescription = schema.describe();

      Object.entries(schemaDescription.keys).forEach(([key, value]) => {
        if (value.type === 'array' && processedRecord[key]) {
          processedRecord[key] = processedRecord[key].split(',').map(item => item.trim());
        }
      });

      // Typecast CSV data using schema
      let check = {...processedRecord};
      check[id_field] = fakeData[id_field];
      const { value: castedCsvData, error: castError } = schema.validate(check, {
        stripUnknown: true,
        presence: 'optional'
      });

      if (castError) {
        console.warn(`Warning: Some CSV data failed validation for ${collection}:`, castError.message);
      }

      // Merge data with precedence: overrides > CSV data > fake data
      const data = {
        ...fakeData,
        ...(castedCsvData || {}),
        ...overrides
      };

      const { error } = validator(data);
      if (error) {
        console.error(`Invalid ${collection} data:`, error.message);
        continue;
      }

      const docRef = await db.collection(collection).doc(data[id_field]).set(data);
      results.push({ id: docRef.id, ...data });
      console.log(`Created ${collection} from CSV ${results.length}/${records.length}`);
    }
  } else {
    // Generate purely fake data if no CSV provided
    for (let i = 0; i < count; i++) {
      const data = generator(overrides);
      const { error } = validator(data);
      if (error) {
        console.error(`Invalid ${collection} data:`, error.message);
        continue;
      }

      const docRef = await db.collection(collection).doc(data[id_field]).set(data);
      results.push({ id: docRef.id, ...data });
      console.log(`Created ${collection} ${i + 1}/${count}`);
    }
  }

  return results;
}

// Get command line arguments
const args = process.argv.slice(2);
const collection = args[0];
const countOrFile = args[1];
const overrides = {};

let count = 10;
let csvFile = null;

// Check if second argument is a number or CSV file
if (countOrFile) {
  if (countOrFile.endsWith('.csv')) {
    csvFile = countOrFile;
  } else {
    count = parseInt(countOrFile) || 10;
  }
}

// Parse any override arguments (format: key=value)
args.slice(2).forEach(arg => {
  const [key, value] = arg.split('=');
  if (key && value) {
    // Convert string "true"/"false" to boolean
    if (value.toLowerCase() === 'true') overrides[key] = true;
    else if (value.toLowerCase() === 'false') overrides[key] = false;
    // Convert string "null" to null
    else if (value.toLowerCase() === 'null') overrides[key] = null;
    // Try to convert to number, otherwise keep as string
    else overrides[key] = isNaN(value) ? value : Number(value);
  }
});

if (!collection) {
  console.error(`
Usage: node fill-fake-data.js <collection> [count|csv_file] [overrides...]

Collections:
  - organizations
  - users
  - panels
  - products
  - questions

Options:
  count: Number of fake records to generate (default: 10)
  csv_file: Path to CSV file containing partial data to combine with fake data
  
Overrides:
  Specify field overrides using key=value pairs
  
Examples:
  node fill-fake-data.js organizations 5 name="Test Org" isPublic=true
  node fill-fake-data.js products data.csv category="Test Category"
`);
  process.exit(1);
}

// Run the script
generateFakeData(collection, count, overrides, csvFile)
  .then(results => {
    console.log(`\nSuccessfully created ${results.length} ${collection}`);
    process.exit(0);
  })
  .catch(error => {
    console.error('Error:', error.message);
    process.exit(1);
  });
