const admin = require('firebase-admin');
const dotenv = require('dotenv');
const fs = require('fs');
const csv = require('csv-parse/sync');
const { generateFakeOrganization, validateOrganization, organizationSchema } = require('../models/organization');
const { generateFakePanel, validatePanel, panelSchema } = require('../models/panel');
const { generateFakeProduct, validateProduct, productSchema } = require('../models/product');
const { generateFakeUser, validateUser, userSchema } = require('../models/user');
const { generateFakeFlavor, validateFlavor, flavorSchema } = require('../models/flavor');
const { generateFakeEffect, validateEffect, effectSchema } = require('../models/effect');
const { generateFakeDemographic, validateDemographic, demographicSchema } = require('../models/demographic');
const { faker } = require('@faker-js/faker');

// Load environment variables
dotenv.config();

// Initialize Firebase Admin
const serviceAccount = require(process.env.UTIL_FIREBASE_SERVICE_ACCOUNT_PATH);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: process.env.FIREBASE_DATABASE_URL
});

const db = admin.firestore();

async function updateCollectionWithFakeData(collectionName, propertyName) {
  let schema;
  
  // Get the appropriate schema based on collection
  switch (collectionName) {
    case 'organizations':
      schema = organizationSchema;
      break;
    case 'users':
      schema = userSchema; 
      break;
    case 'panels':
      schema = panelSchema;
      break;
    case 'products':
      schema = productSchema;
      break;
    case 'flavorOptions':
      schema = flavorSchema;
      break;
    case 'effectOptions':
      schema = effectSchema;
      break;
    case 'demographicOptions':
      schema = demographicSchema;
      break;
    default:
      throw new Error(`Unsupported collection: ${collectionName}`);
  }

  // Verify property exists in schema
  const schemaDescription = schema.describe();
  if (!schemaDescription.keys[propertyName]) {
    throw new Error(`Property ${propertyName} does not exist in schema for ${collectionName}`);
  }

  // Get property type from schema
  const propertyType = schemaDescription.keys[propertyName].type;

  // Get all documents in collection
  const snapshot = await db.collection(collectionName).get();
  
  // Update each document
  const batch = db.batch();
  snapshot.docs.forEach(doc => {
    let fakeValue;
    
    // Generate appropriate fake value based on type
    switch (propertyType) {
      case 'string':
        fakeValue = faker.lorem.word();
        break;
      case 'number':
        fakeValue = faker.number.int(100);
        break;
      case 'boolean':
        fakeValue = faker.datatype.boolean();
        break;
      case 'date':
        fakeValue = faker.date.future();
        break;
      default:
        fakeValue = faker.lorem.word();
    }

    batch.update(doc.ref, { [propertyName]: fakeValue });
  });

  await batch.commit();
  console.log(`Updated ${snapshot.size} documents in ${collectionName}`);
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length !== 2) {
    console.error('Usage: node script.js <collectionName> <propertyName>');
    console.error('Example: node script.js panels description');
    process.exit(1);
  }

  const [collectionName, propertyName] = args;

  updateCollectionWithFakeData(collectionName, propertyName)
    .then(() => {
      console.log('Update completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
}
