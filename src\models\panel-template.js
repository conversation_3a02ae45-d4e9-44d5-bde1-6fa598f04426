const Joi = require('joi');
const { faker } = require('@faker-js/faker');

// Define panel template schema using Joi for robust validation
const panelTemplateSchema = Joi.object({
    // Required fields
    name: Joi.string()
      .required()
      .min(2)
      .max(100)
      .description('Display name of the panel template'),

    template_id: Joi.string()
      .required()
      .description('ID of the panel template'),

    organization_id: Joi.string()
      .required()
      .description('ID of the organization that owns this template'),

    // Optional fields with defaults
    active: Joi.boolean()
      .default(true)
      .optional()
      .description('Whether the panel template is currently active'),

    allowedOrganizations: Joi.array()
      .items(Joi.string())
      .default([])
      .optional()
      .description('List of organization IDs that can access this template'),

    description: Joi.string()
      .min(2)
      .max(500)
      .allow('')
      .default('')
      .optional()
      .description('Description of the panel template'),

    isPublic: Joi.boolean()
      .default(false)
      .optional()
      .description('Whether the panel template is publicly accessible'),

    value: Joi.number()
      .default(0)
      .optional()
      .description('Value of the panel template'),

    budget: Joi.number()
      .default(0)
      .optional()
      .description('Budget of the panel template'),

    minutes: Joi.number()
      .default(30)
      .optional()
      .description('Number of minutes to complete the panel'),

    start_date: Joi.date()
      .default(() => new Date())
      .optional()
      .description('Date and time when the panel template will be available'),

    end_date: Joi.date()
      .default(() => new Date('4200-01-01T00:00:00Z'))
      .optional()
      .description('Date and time when the panel template will no longer be available'),

    steps: Joi.array()
      .items(Joi.string())
      .default([])
      .optional()
      .description('List of step IDs that make up the panel template'),

    consumption_option: Joi.string()
      .default('per_use')
      .optional()
      .description('Consumption option of the panel template'),

    minimum_certification_level: Joi.number()
      .default(0)
      .optional()
      .description('Minimum certification level required to access the panel template'),

    experiment_type: Joi.string()
      .valid('Triangle Test', 'Normal Sensory')
      .default('Normal Sensory')
      .optional()
      .description('Type of experiment for this panel template'),

    usedBy: Joi.array()
      .items(Joi.string())
      .default([])
      .optional()
      .description('List of panel IDs that use this template')
  });

// Generate a fake panel template for testing
const generateFakePanelTemplate = (overrides = {}) => {
  let id = overrides.organization_id || faker.string.alphanumeric(20);
  const fakePanelTemplate = {
    name: faker.company.name(),
    organization_id: id,
    template_id: faker.string.alphanumeric(20),
    experiment_type: faker.helpers.arrayElement(['Triangle Test', 'Normal Sensory']),
    usedBy: [],
    // Optional fields with random values
    active: faker.datatype.boolean(),
    allowedOrganizations: [id], 
    description: faker.lorem.sentence(),
    invite_code: faker.string.alphanumeric(8).toUpperCase(),
    isPublic: faker.datatype.boolean(),
    value: faker.number.int({ min: 1, max: 20 }),
    minutes: faker.number.int({ min: 1, max: 60 }),
    end_date: faker.date.future(),
    steps: ["a9k1bpXX4H57Bdl7XhaX", "wjA1uhqbIMFuZwAaCoJ8", "awja0ameTALoE0D4GlWD"],
    consumption_option: "per_use",
    minimum_certification_level: faker.number.int({ min: 0, max: 3 }),
    budget: faker.number.int({ min: 1, max: 1000 }),
    start_date: faker.date.recent(),
    ...overrides
  };

  // Validate the generated data
  const { error } = validatePanelTemplate(fakePanelTemplate);
  if (error) {
    throw new Error(`Generated invalid panel template: ${error.message}`);
  }

  return fakePanelTemplate;
};

// Generate multiple fake panel templates
const generateFakePanelTemplates = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakePanelTemplate(overrides));
};

// Validate panel template against schema
const validatePanelTemplate = (template) => {
  return panelTemplateSchema.validate(template, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Validate panel template updates
const validatePanelTemplateUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(panelTemplateSchema.describe().keys).reduce((acc, [key]) => {
      // Make all fields optional for updates, but keep template_id as non-updatable
      if (key !== 'template_id') {
        acc[key] = Joi.any().optional();
      }
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to panel template model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validatePanelTemplate({
    ...panelTemplateSchema.validate({}).value, // Get defaults
    ...data
  });

  if (error) {
    throw new Error(`Invalid panel template data from Firestore: ${error.message}`);
  }

  return value;
};

// Convert panel template model to Firestore document
const toFirestore = (template) => {
  const { value, error } = validatePanelTemplate({
    ...panelTemplateSchema.validate({}).value, // Get defaults
    ...template
  });

  if (error) {
    throw new Error(`Invalid panel template data for Firestore: ${error.message}`);
  }

  return value;
};

module.exports = {
  panelTemplateSchema,
  generateFakePanelTemplate,
  generateFakePanelTemplates,
  validatePanelTemplate,
  validatePanelTemplateUpdate,
  fromFirestore,
  toFirestore
};