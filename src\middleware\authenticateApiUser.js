const { firestoreModule } = require('../config');

/**
 * Middleware to authenticate API users via API key
 * Checks the request headers for an API key and validates it against
 * the Firestore 'integrations' collection
 */
const authenticateApiUser = async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    return res.status(401).send('API key is required');
  }
  
  try {
    // Query Firestore to find the integration with the provided API key
    const integrationsRef = firestoreModule.db.collection('integrations');
    const snapshot = await integrationsRef.where('apiKey', '==', apiKey).limit(1).get();
    
    if (snapshot.empty) {
      return res.status(401).send('Invalid API key');
    }
    
    // Get the first matching integration
    const integration = snapshot.docs[0].data();
    
    // Add integration data to the request object
    req.integration = {
      id: snapshot.docs[0].id,
      ...integration
    };
    req.user = {
        uid: integration.user_id
    };

    next();
  } catch (error) {
    console.error('API authentication error:', error);
    res.status(500).send('Authentication failed');
  }
};

module.exports = authenticateApiUser;
