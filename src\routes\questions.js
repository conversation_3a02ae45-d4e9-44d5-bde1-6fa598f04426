const express = require('express');
const router = express.Router();


const { authenticateUser, checkPermissions, paginationMiddleware, canAccessOrganization } = require('../middleware');
const { QuestionService } = require('../services');

// Apply authentication to all question routes
router.all('*', authenticateUser, checkPermissions(['survey', 'admin', 'insights']));

/**
 * @swagger
 * /questions:
 *   get:
 *     summary: Get all questions for user's organization
 *     description: Retrieves all questions belonging to the user's organization
 *     tags: [Questions]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter questions
 *       - in: query
 *         name: includeOptions
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include detailed option data
 *     responses:
 *       200:
 *         description: Questions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Question'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', async (req, res) => {
  try {
    // Get organization either from query or use user's primary organization
    const organizationId = req.query.organization || req.userData.organizations[0];
    
    // Check if user can access this organization
    if (!req.userData.organizations.includes(organizationId)) {
      return res.status(403).json({ error: 'You do not have access to this organization' });
    }
    
    // Get options for hydration
    const options = {
      includeOptions: req.query.includeOptions === 'true'
    };
    
    const questions = await QuestionService.getQuestionsByOrganization(organizationId, options);
    res.status(200).json(questions);
  } catch (error) {
    console.error('Error getting questions:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /questions/{questionId}:
 *   get:
 *     summary: Get a specific question
 *     description: Retrieves a specific question by ID with optional reference hydration
 *     tags: [Questions]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: questionId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the question to retrieve
 *       - in: query
 *         name: includeOptions
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether to include detailed option data
 *     responses:
 *       200:
 *         description: Question retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Question'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this question
 *       404:
 *         description: Question not found
 *       500:
 *         description: Server error
 */
router.get('/:questionId', async (req, res) => {
  try {
    const questionId = req.params.questionId;
    
    // Get the question first to check organization access
    const question = await QuestionService.getQuestion(questionId);
    
    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }
    
    // Check if user can access this organization
    if (!req.userData.organizations.includes(question.organization_id)) {
      return res.status(403).json({ error: 'You do not have access to this question' });
    }
    
    // Get options for hydration
    const includeOptions = req.query.includeOptions !== 'false'; // Default to true
    
    // Get question with references
    const hydratedQuestion = await QuestionService.getQuestionWithReferences(questionId, {
      includeOptions
    });
    
    res.status(200).json(hydratedQuestion);
  } catch (error) {
    console.error('Error getting question:', error);
    if (error.message === 'Question not found') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

/**
 * @swagger
 * /questions:
 *   post:
 *     summary: Create a new question with options
 *     description: Creates a new question with associated options
 *     tags: [Questions]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the question
 *               stepInstructions:
 *                 type: string
 *                 description: Instructions for the question
 *               stepType:
 *                 type: string
 *                 description: Type of question (e.g., multiple-choice, slider)
 *               required:
 *                 type: boolean
 *                 description: Whether the question is required
 *               options:
 *                 type: array
 *                 description: Array of option objects
 *                 items:
 *                   type: object
 *                   properties:
 *                     label:
 *                       type: string
 *                     instructions:
 *                       type: string
 *                     sliderLeft:
 *                       type: string
 *                     sliderCenter:
 *                       type: string
 *                     sliderRight:
 *                       type: string
 *             required:
 *               - name
 *               - stepInstructions
 *               - stepType
 *     responses:
 *       201:
 *         description: Question created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', async (req, res) => {
  try {
    const { name, stepInstructions, stepType, required = false, options = [], organization } = req.body;
    
    // Use organization from request or default to user's primary
    const organizationId = organization || req.userData.organizations[0];
    
    // Check if user can access this organization
    if (!req.userData.organizations.includes(organizationId)) {
      return res.status(403).json({ error: 'You do not have access to this organization' });
    }
    
    // Prepare question data
    const questionData = {
      name,
      organization_id: organizationId,
      question_id: await QuestionService.firestoreModule.getDocumentKey('questions'),
      isDefault: false,
      required,
      stepType,
      stepInstructions,
      optionIds: []
    };
    
    // Create question with options
    const createdQuestion = await QuestionService.createQuestion(questionData, options);
    
    res.status(201).json(createdQuestion);
  } catch (error) {
    console.error('Error creating question:', error);
    if (error.message.includes('Invalid question data')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

/**
 * @swagger
 * /questions/{questionId}:
 *   put:
 *     summary: Update a question
 *     description: Updates basic properties of a question (name, instructions, required)
 *     tags: [Questions]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: questionId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the question to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: New name for the question
 *               stepInstructions:
 *                 type: string
 *                 description: New instructions for the question
 *               required:
 *                 type: boolean
 *                 description: Whether the question is required
 *     responses:
 *       200:
 *         description: Question updated successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this question
 *       404:
 *         description: Question not found
 *       500:
 *         description: Server error
 */
router.put('/:questionId', async (req, res) => {
  try {
    const questionId = req.params.questionId;
    
    // Get the question first to check organization access
    const question = await QuestionService.getQuestion(questionId);
    
    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }
    
    // Check if user can access this organization
    if (!req.userData.organizations.includes(question.organization_id)) {
      return res.status(403).json({ error: 'You do not have access to this question' });
    }
    
    // Update the question
    const updatedQuestion = await QuestionService.updateQuestion(questionId, req.body);
    
    // Return the updated question with options
    const hydratedQuestion = await QuestionService.getQuestionWithReferences(questionId, {
      includeOptions: true
    });
    
    res.status(200).json(hydratedQuestion);
  } catch (error) {
    console.error('Error updating question:', error);
    if (error.message === 'No valid fields to update') {
      res.status(400).json({ error: error.message });
    } else if (error.message === 'Question not found') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

/**
 * @swagger
 * /questions/{questionId}:
 *   delete:
 *     summary: Delete a question
 *     description: Marks a question as deleted
 *     tags: [Questions]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: questionId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the question to delete
 *     responses:
 *       200:
 *         description: Question deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this question
 *       404:
 *         description: Question not found
 *       500:
 *         description: Server error
 */
router.delete('/:questionId', async (req, res) => {
  try {
    const questionId = req.params.questionId;
    
    // Get the question first to check organization access
    const question = await QuestionService.getQuestion(questionId);
    
    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }
    
    // Check if user can access this organization
    if (!req.userData.organizations.includes(question.organization_id)) {
      return res.status(403).json({ error: 'You do not have access to this question' });
    }
    
    // Delete the question
    await QuestionService.deleteQuestion(questionId);
    
    res.status(200).json({ success: true, message: 'Question deleted successfully' });
  } catch (error) {
    console.error('Error deleting question:', error);
    if (error.message === 'Question not found') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

module.exports = router;