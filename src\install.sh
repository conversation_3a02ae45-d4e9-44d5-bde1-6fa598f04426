#!/bin/bash

# Exit on error
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in install script at line $1"
    exit 1
}

# Set error trap
trap 'handle_error $LINENO' ERR

# Check if nvm is installed
if [ ! -d "$HOME/.nvm" ]; then
    echo "NVM not found. Please install NVM first."
    exit 1
fi

# Load NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# Check if node is available
if ! command -v node >/dev/null 2>&1; then
    echo "Node.js not found. Please install Node.js using NVM."
    exit 1
fi

# Check if pm2 is installed
if ! command -v pm2 >/dev/null 2>&1; then
    echo "PM2 not found. Installing PM2..."
    npm install -g pm2
fi

# Navigating to the script's directory
cd "$(dirname "$0")" || exit 1

echo "Installing dependencies..."
# Running npm install to install dependencies with error checking
if ! npm install; then
    echo "Failed to install dependencies"
    exit 1
fi

echo "Starting/restarting service..."
# Check if sensei is in pm2 list and start/restart accordingly
if pm2 list | grep -q "sensei"; then
    if ! pm2 restart sensei; then
        echo "Failed to restart sensei service"
        exit 1
    fi
else
    if ! pm2 start index.js --name sensei; then
        echo "Failed to start sensei service"
        exit 1
    fi
fi

echo "Installation completed successfully"
