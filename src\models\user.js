const Joi = require('joi');
const Table = require('cli-table3');

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - email
 *         - first_name
 *         - last_name
 *         - organizations
 *         - uid
 *         - roles
 *         - zipCode
 *         - firstLogin
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User email address
 *         first_name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: User first name
 *         last_name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: User last name
 *         organizations:
 *           type: array
 *           items:
 *             type: string
 *           description: List of organization IDs the user belongs to
 *         uid:
 *           type: string
 *           description: Unique identifier for the user
 *         roles:
 *           type: array
 *           items:
 *             type: string
 *             enum: [survey, admin, insights]
 *           description: List of roles the user has
 *         zipCode:
 *           type: string
 *           description: User zip code
 *         firstLogin:
 *           type: boolean
 *           description: Whether the user is logging in for the first time
 */

// Define user schema using Joi for robust validation
const userSchema = Joi.object({
  email: Joi.string()
    .required()
    .email()
    .description('User email address'),
    
  first_name: Joi.string()
    .required()
    .min(2)
    .max(50)
    .description('User first name'),
    
  last_name: Joi.string()
    .required()
    .min(2)
    .max(50)
    .description('User last name'),
    
  organizations: Joi.array()
    .items(Joi.string())
    .required()
    .description('List of organization IDs the user belongs to'),
    
  uid: Joi.string()
    .required()
    .description('Unique identifier for the user'),

  roles: Joi.array()
    .items(Joi.string().valid('survey', 'admin', 'insights'))
    .required()
    .description('List of roles the user has'),

  zipCode: Joi.string()
    .required()
    .description('User zip code'),

  firstLogin: Joi.boolean()
    .required()
    .description('Whether the user is logging in for the first time')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake user document for testing
const generateFakeUser = (overrides = {}) => {
  const fakeUser = {
    email: faker.internet.email(),
    first_name: faker.person.firstName(),
    last_name: faker.person.lastName(),
    organizations: [faker.string.alphanumeric(20)],
    uid: faker.string.alphanumeric(28),
    roles: ['survey'],
    zipCode: faker.location.zipCode(),
    firstLogin: true,
    ...overrides
  };

  // Validate the generated data
  const { error } = validateUser(fakeUser);
  if (error) {
    throw new Error(`Generated invalid user: ${error.message}`);
  }

  return fakeUser;
};

// Generate multiple fake users
const generateFakeUsers = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeUser(overrides));
};

// Validate user document against schema
const validateUser = (user) => {
  return userSchema.validate(user, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Add this new validation function for updates
const validateUserUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(userSchema.describe().keys).reduce((acc, [key, value]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to user model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateUser(data);
  
  if (error) {
    throw new Error(`Invalid user data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert user model to Firestore document
const toFirestore = (user) => {
  const { value, error } = validateUser(user);
  
  if (error) {
    throw new Error(`Invalid user data for Firestore: ${error.message}`);
  }
  
  return value;
};

// Format user data into a CLI-friendly table
const formatUsersTable = (users) => {
  const table = new Table({
    head: ['Name', 'Email', 'Roles', 'Organizations'],
    colWidths: [30, 35, 20, 30]
  });

  users.forEach(user => {
    table.push([
      `${user.first_name} ${user.last_name}`,
      user.email,
      (user.roles || []).join(', '),
      (user.organizations || []).join('\n')
    ]);
  });

  return table.toString();
};

// Format a single user's details into a CLI-friendly table
const formatUserDetails = (user) => {
  const table = new Table();

  table.push(
    { 'Full Name': `${user.first_name} ${user.last_name}` },
    { 'Email': user.email },
    { 'User ID': user.uid },
    { 'Roles': (user.roles || []).join(', ') },
    { 'Organizations': (user.organizations || []).join('\n') },
    { 'Zip Code': user.zipCode },
    { 'First Login': user.firstLogin ? 'Yes' : 'No' }
  );

  return table.toString();
};

module.exports = {
  userSchema,
  generateFakeUser,
  generateFakeUsers,
  validateUser,
  validateUserUpdate,
  fromFirestore,
  toFirestore,
  formatUsersTable,
  formatUserDetails
};
