const hydrateProduct = require('./hydrateProduct');

const paginateProducts = async (productIds, page, limit, startIndex) => {
  const paginatedProductIds = productIds.slice(startIndex, startIndex + limit);

  let organizations = {};

  //TODO: Optimize this to batch requests and cut down on round-trip
  const products = await Promise.all(paginatedProductIds.map(async (productId) => hydrateProduct(productId, organizations)));
  const totalProducts = productIds.length;
  const totalPages = Math.ceil(totalProducts / limit);
  const hasMore = page < totalPages;
  return { products, currentPage: page, totalPages, hasMore };
}

module.exports = paginateProducts;