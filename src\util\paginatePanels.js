const hydratePanel = require('./hydratePanel');

//Paginate the panel IDs
const paginatePanels = async (panelIds, page, limit, startIndex, userId=null) => {
  // Paginate the panel IDs
  const paginatedPanelIds = panelIds.slice(startIndex, startIndex + limit);
    
  let products = {};
  let organizations = {};

  // Fetch panel details for each ID in the current page
  //TODO: Optimize this to batch requests and cut down on round-trips
  const panels = await Promise.all(paginatedPanelIds.map(async (panelId) => {
    console.log("Hydrating panel:", panelId);
    try {
      return await hydratePanel(panelId, products, organizations, userId)
    } catch (error) {
      console.error("Error hydrating panel:", panelId, error);
      throw error;
    }
  }));
  const totalPanels = panelIds.length;
  const totalPages = Math.ceil(totalPanels / limit);
  const hasMore = page < totalPages;
  return { panels, currentPage: page, totalPages, hasMore };
}

module.exports = paginatePanels;
