const dotenv = require('dotenv');
dotenv.config();
const { firestoreModule } = require('../config');

async function addDeletedFieldToCollection(collectionName) {
  try {
    console.log(`Processing ${collectionName} collection...`);
    const snapshot = await firestoreModule.db.collection(collectionName).get();
    
    let updateCount = 0;
    const batch = firestoreModule.db.batch();
    
    snapshot.forEach(doc => {
      const data = doc.data();
      if (data.deleted === undefined) {
        batch.update(doc.ref, { deleted: false });
        updateCount++;
      }
    });

    if (updateCount > 0) {
      await batch.commit();
      console.log(`Updated ${updateCount} documents in ${collectionName}`);
    } else {
      console.log(`No updates needed in ${collectionName}`);
    }
    
    return updateCount;
  } catch (error) {
    console.error(`Error processing ${collectionName}:`, error);
    throw error;
  }
}

async function main() {
  try {
    const collections = ['panels', 'products', 'users'];
    let totalUpdates = 0;

    console.log('Starting update process...');
    
    for (const collection of collections) {
      const updates = await addDeletedFieldToCollection(collection);
      totalUpdates += updates;
    }

    console.log(`Update complete. Total documents updated: ${totalUpdates}`);
    process.exit(0);
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  }
}

main();
