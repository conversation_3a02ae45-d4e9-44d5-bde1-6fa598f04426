/**
 * <PERSON><PERSON>t to validate that all Express routes are documented in the Swagger spec
 * Run with: node scripts/validateSwagger.js
 */

const fs = require('fs');
const path = require('path');
const express = require('express');
const { Router } = express;
const YAML = require('js-yaml');

// Function to extract routes from an Express router
function extractRoutes(router, basePath = '') {
  const routes = [];
  
  if (!router.stack) return routes;
  
  router.stack.forEach(layer => {
    if (layer.route) {
      // Simple route
      const routePath = basePath + layer.route.path;
      const methods = Object.keys(layer.route.methods).map(m => m.toLowerCase());
      methods.forEach(method => {
        routes.push({ method, path: normalizePath(routePath) });
      });
    } else if (layer.name === 'router' && layer.handle.stack) {
      // Nested router
      let nestedBasePath = basePath;
      if (layer.regexp && layer.regexp.source !== '^\\/(?=\\/|$)') {
        // Extract the path from the regexp
        const match = layer.regexp.toString().match(/^\\\/([^\\\/]+)/);
        if (match) {
          nestedBasePath += '/' + match[1];
        }
      }
      routes.push(...extractRoutes(layer.handle, nestedBasePath));
    } else if (layer.name !== 'bound dispatch' && layer.name !== 'middleware') {
      // This could be middleware, we'll skip it
    }
  });
  
  return routes;
}

// Normalize path parameters to match Swagger format
function normalizePath(path) {
  return path.replace(/:([^/]+)/g, '{$1}');
}

// Main execution
try {
  // Load the Swagger spec
  const swaggerPath = path.resolve(__dirname, '../swagger.yaml');
  if (!fs.existsSync(swaggerPath)) {
    console.error('❌ Swagger YAML file not found. Run npm run generate:swagger first.');
    process.exit(1);
  }
  
  const swaggerSpec = YAML.load(fs.readFileSync(swaggerPath, 'utf8'));
  const swaggerPaths = Object.keys(swaggerSpec.paths || {});
  
  // Load the routes module
  const routesModule = require('../routes');
  
  // Extract all Express routes
  const expressRoutes = extractRoutes(routesModule, '');
  
  // Find undocumented routes
  const undocumentedRoutes = expressRoutes.filter(route => {
    const swaggerPath = swaggerPaths.find(p => {
      // Simple path comparison
      if (p === route.path) return true;
      
      // More complex comparison accounting for path parameters
      const expressSegments = route.path.split('/').filter(Boolean);
      const swaggerSegments = p.split('/').filter(Boolean);
      
      if (expressSegments.length !== swaggerSegments.length) return false;
      
      return expressSegments.every((segment, i) => {
        if (segment === swaggerSegments[i]) return true;
        if (segment.startsWith('{') && segment.endsWith('}') &&
            swaggerSegments[i].startsWith('{') && swaggerSegments[i].endsWith('}')) {
          return true;
        }
        return false;
      });
    });
    
    if (!swaggerPath) return true;
    
    // Check if the HTTP method is documented
    const swaggerPathObj = swaggerSpec.paths[swaggerPath];
    return !Object.keys(swaggerPathObj).map(k => k.toLowerCase()).includes(route.method);
  });
  
  // Print results
  console.log('📊 Swagger Validation Results:');
  console.log(`- ${expressRoutes.length} total Express routes found`);
  console.log(`- ${swaggerPaths.length} total Swagger paths documented`);
  
  if (undocumentedRoutes.length === 0) {
    console.log('\n✅ All Express routes are documented in the Swagger specification!');
  } else {
    console.log(`\n⚠️  Found ${undocumentedRoutes.length} undocumented routes:`);
    undocumentedRoutes.forEach(route => {
      console.log(`- ${route.method.toUpperCase()} ${route.path}`);
    });
    
    console.log('\nTo document these routes, add JSDoc comments above each route handler:');
    console.log(`
/**
 * @swagger
 * ${undocumentedRoutes[0]?.path || '/example/{param}'}:
 *   ${undocumentedRoutes[0]?.method || 'get'}:
 *     summary: Route description
 *     description: Detailed explanation of what this route does
 *     tags: [Category]
 *     parameters:
 *       - name: paramName
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Success response
 */
router.${undocumentedRoutes[0]?.method || 'get'}('${undocumentedRoutes[0]?.path.replace(/\{([^}]+)\}/g, ':$1') || '/example/:param'}', async (req, res) => {
  // Route implementation
});
`);
  }
} catch (error) {
  console.error('❌ Error validating Swagger documentation:', error);
  process.exit(1);
} 