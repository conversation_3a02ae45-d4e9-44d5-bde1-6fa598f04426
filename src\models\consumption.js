const Joi = require('joi');

// Define consumption schema using <PERSON><PERSON> for robust validation
const consumptionSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the consumption option'),
    
  type: Joi.string()
    .valid('per_use', 'per_serving', 'per_package')
    .required()
    .description('Type of the consumption option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the consumption option'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the consumption option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake consumption document for testing
const generateFakeConsumption = (overrides = {}) => {
  const fakeConsumption = {
    name: faker.lorem.word(),
    type: faker.helpers.arrayElement(['per_use', 'per_serving', 'per_package']),
    priority: faker.number.int({min: 1, max: 100}),
    option_id: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateConsumption(fakeConsumption);
  if (error) {
    throw new Error(`Generated invalid consumption: ${error.message}`);
  }

  return fakeConsumption;
};

// Generate multiple fake consumptions
const generateFakeConsumptions = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeConsumption(overrides));
};

// Validate consumption document against schema
const validateConsumption = (consumption) => {
  return consumptionSchema.validate(consumption, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to consumption model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateConsumption(data);
  
  if (error) {
    throw new Error(`Invalid consumption data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert consumption model to Firestore document
const toFirestore = (consumption) => {
  const { value, error } = validateConsumption(consumption);
  
  if (error) {
    throw new Error(`Invalid consumption data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  consumptionSchema,
  generateFakeConsumption,
  generateFakeConsumptions,
  validateConsumption,
  fromFirestore,
  toFirestore
};
