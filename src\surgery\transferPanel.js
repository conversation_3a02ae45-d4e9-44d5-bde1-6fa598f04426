const { PanelRepository, ProductRepository } = require('../repositories');
const transferProduct = require('./transferProduct');

async function transferPanel(panelId, newOrganizationId){
    try {
        // Get the panel data
        const panel = await PanelRepository.getPanelByID(panelId);
        
        console.log(panel.name);

        // Check if panel has an associated product
        if (panel.product_id) {
            // Transfer the associated product
            await transferProduct(panel.product_id, newOrganizationId);
        }
        
        // Update the panel owner
        const updatedPanel = await PanelRepository.update(panelId, { organization_id: newOrganizationId });
    
        return true;
    } catch (error) {
        console.error('Error transferring panel:', error);
        throw new Error('Error transferring panel');
    }
}

module.exports = transferPanel;