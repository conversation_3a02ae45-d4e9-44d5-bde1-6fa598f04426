/**
 * Reference maps for entity hydration
 * 
 * This file defines standard reference maps for each entity type in the system.
 * These maps can be used with the referenceHydrator to populate references.
 * 
 * Format:
 * {
 *   "fieldPath": {
 *     collection: "collectionName",    // Required: the collection to fetch from
 *     isArray: true/false,             // Optional: whether field is an array of IDs, default false
 *     target: "resultFieldPath",       // Optional: where to store the result (defaults to fieldPath)
 *     fields: ["field1", "field2"],    // Optional: fields to include (null = all fields)
 *     hydrate: { nestedReferenceMap }  // Optional: nested references to hydrate 
 *   }
 * }
 */

// Project references
const PROJECT_REFERENCES = {
  // Basic references
  "organization_id": {
    collection: "organizations",
    target: "organization",
    fields: ["name", "organization_id", "website"],
    id_field: "organization_id"
  },
  "owner": {
    collection: "users",
    target: "owner_details", 
    fields: ["first_name", "last_name", "email", "uid"],
    id_field: "uid"
  },
  "created_by": {
    collection: "users",
    target: "creator_details",
    fields: ["first_name", "last_name", "email", "uid"],
    id_field: "uid"
  },
  
  // Array references
  "assigned_panels": {
    collection: "panels",
    isArray: true,
    target: "panels",
    fields: ["name", "panel_id", "active", "product_id", "invite_code", "value"],
    id_field: "panel_id"
  },
  "assigned_users": {
    collection: "users",
    isArray: true,
    target: "users",
    fields: ["first_name", "last_name", "email", "uid"],
    id_field: "uid"
  },
  "assigned_samples": {
    collection: "products",
    isArray: true,
    target: "samples",
    fields: ["name", "product_id", "image", "description"],
    id_field: "product_id"
  },
  "collaborators": {
    collection: "users",
    isArray: true,
    target: "collaborator_details",
    fields: ["first_name", "last_name", "email", "uid"],
    id_field: "uid"
  }
};

// Panel references
const PANEL_REFERENCES = {
  "organization_id": {
    collection: "organizations",
    target: "organization",
    fields: ["name", "organization_id", "website"],
    id_field: "organization_id"
  },
  "product_id": {
    collection: "products",
    target: "product",
    fields: ["name", "product_id", "image", "description"],
    id_field: "product_id"
  },
  "steps": {
    collection: "steps",
    isArray: true,
    fields: ["title", "description", "type"],
    id_field: "option_id"
  },
  "allowedOrganizations": {
    collection: "organizations",
    isArray: true,
    target: "allowed_organizations",
    fields: ["name", "organization_id"],
    id_field: "organization_id"
  }
};

// User references
const USER_REFERENCES = {
  "organizations": {
    collection: "organizations",
    isArray: true,
    target: "organization_details",
    fields: ["name", "organization_id", "website"],
    id_field: "organization_id"
  }
};

const TRIANGLE_TEST_REFERENCES  = {
  "product_ids": {
    collection: "products",
    isArray: true,
    target: "products",
    fields: ["name", "product_id", "image", "description"],
    id_field: "product_id"
  }
}

// Product/sample references
const PRODUCT_REFERENCES = {
  "organization_id": {
    collection: "organizations",
    target: "organization",
    fields: ["name", "organization_id", "website"],
    id_field: "organization_id"
  }
};

// Response references
const RESPONSE_REFERENCES = {
  "user_id": {
    collection: "users",
    target: "user",
    fields: ["first_name", "last_name", "email", "uid", "profile_image"],
    id_field: "uid"
  }//,
  // "panel_id": {
  //   collection: "panels",
  //   target: "panel",
  //   fields: ["name", "panel_id", "active", "description"],
  //   id_field: "panel_id"
  // },
  // "product_id": {
  //   collection: "products",
  //   target: "product",
  //   fields: ["name", "product_id", "image", "description"],
  //   id_field: "product_id"
  // },
  // "organization_id": {
  //   collection: "organizations",
  //   target: "organization",
  //   fields: ["name", "organization_id", "website"],
  //   id_field: "organization_id"
  // }
};

// Full response references with nested hydration
const RESPONSE_FULL_REFERENCES = {
  ...RESPONSE_REFERENCES,
  "panel_id": {
    collection: "panels",
    target: "panel",
    fields: ["name", "panel_id", "active", "description", "product_id"],
    id_field: "panel_id",
    hydrate: {
      "product_id": {
        collection: "products",
        target: "product",
        fields: ["name", "product_id", "image", "description"],
        id_field: "product_id"
      }
    }
  }
};

// Nested reference examples
const PROJECT_FULL_REFERENCES = {
  ...PROJECT_REFERENCES,
  "assigned_panels": {
    collection: "panels",
    isArray: true,
    target: "panels",
    hydrate: {
      "product_id": {
        collection: "products",
        target: "product",
        fields: ["name", "product_id", "image"],
        id_field: "product_id"
      }
    },
    id_field: "panel_id"
  }
};

// Group references
const GROUP_REFERENCES = {
  "createdBy": {
    collection: "users",
    target: "owner",
    fields: ["first_name", "last_name", "email", "uid", "profile_image"],
    id_field: "uid"
  }
};

// Full group references with nested hydration
const GROUP_FULL_REFERENCES = {
  ...GROUP_REFERENCES,
  "users": {
    collection: "users",
    isArray: true,
    target: "user_details",
    fields: ["first_name", "last_name", "email", "uid", "profile_image"],
    id_field: "uid"
  },
  "panels": {
    collection: "panels",
    isArray: true,
    target: "panel_details",
    fields: ["name", "panel_id", "active", "description"],
    id_field: "panel_id"
  },
  "samples": {
    collection: "products",
    isArray: true,
    target: "sample_details",
    fields: ["name", "product_id", "image", "description"],
    id_field: "product_id"
  }
};

// Question references
const QUESTION_REFERENCES = {
  "organization_id": {
    collection: "organizations",
    target: "organization",
    fields: ["name", "organization_id", "website"],
    id_field: "organization_id"
  }
};

// Full question references with option hydration
const QUESTION_FULL_REFERENCES = {
  ...QUESTION_REFERENCES,
  "optionIds": {
    collection: "options",
    isArray: true,
    target: "options",
    fields: ["label", "instructions", "sliderLeft", "sliderCenter", "sliderRight", "option_id"],
    id_field: "option_id"
  }
};

module.exports = {
  PROJECT_REFERENCES,
  PROJECT_FULL_REFERENCES,
  PANEL_REFERENCES,
  USER_REFERENCES,
  PRODUCT_REFERENCES,
  RESPONSE_REFERENCES,
  RESPONSE_FULL_REFERENCES,
  GROUP_REFERENCES,
  GROUP_FULL_REFERENCES,
  QUESTION_REFERENCES,
  QUESTION_FULL_REFERENCES
}; 