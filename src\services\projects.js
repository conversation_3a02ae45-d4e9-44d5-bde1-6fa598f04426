const { ProjectRepository, UserRepository, PanelRepository, ProductRepository, GroupRepository } = require("../repositories");
const BaseService = require("./base");
const referenceHydrator = require('../util/referenceHydrator');
const { PROJECT_REFERENCES, PROJECT_FULL_REFERENCES } = require('../util/referenceMap');

class ProjectService extends BaseService {
  constructor() {
    super();
    this.ProjectRepository = new ProjectRepository();
    this.UserRepository = UserRepository;
    this.PanelRepository = PanelRepository;
    this.ProductRepository = new ProductRepository();
    this.GroupRepository = GroupRepository;
  }


  
  async getProjects(userId, organization, userOrgs=[]) {
    if (userOrgs.length === 0) {
      const user = await this.UserRepository.getUserById(userId);
      userOrgs =
        organization && user["organizations"].includes(organization)
        ? [organization]
        : user["organizations"];
    }

    let projects = [];
    // Get projects for organizations
    for (const orgId of userOrgs) {
      console.log("Getting projects for organization: ", orgId);
      const orgProjects = await this.ProjectRepository.getProjectsByOrganization(orgId);
      projects = [...projects, ...orgProjects];
    }
    console.log("Projects: ", projects);
    // Get projects directly assigned to user
    const userProjects = await this.ProjectRepository.findProjectsForEntity(userId, 'user');
    console.log("User projects: ", userProjects);
    // Merge and remove duplicates
    const allProjects = [...projects, ...userProjects];
    const uniqueProjects = Array.from(
      new Map(allProjects.map(project => [project.project_id, project])).values()
    );

    return uniqueProjects;
  }

  async getProject(projectId) {
    const projectDetails = await this.ProjectRepository.read(projectId);
    return projectDetails;
  }

  async getProjectsByUserId(userId) {
    const projects = await this.ProjectRepository.findProjectsForEntity(userId, 'user');
    return projects;
  }

  async createProject(projectData, userId) {
    try {
      const new_project_id = await this.firestoreModule.getDocumentKey("projects");

      // Set up the new project with required fields
      const newProject = {
        project_id: new_project_id,
        created_by: userId,
        owner: userId,
        status: projectData.status || 'draft',
        is_public: projectData.is_public !== undefined ? projectData.is_public : false,
        completion_percentage: 0,
        total_responses: 0,
        ...projectData
      };

      // Ensure required date fields
      if (!newProject.start_date) {
        newProject.start_date = this.firestoreModule.Timestamp.now();
      } else if (newProject.start_date instanceof Date) {
        // Convert Date object to Firestore Timestamp if needed
        newProject.start_date = this.firestoreModule.Timestamp.fromDate(newProject.start_date);
      }
      
      if (!newProject.end_date) {
        // Set end date to 30 days from start
        const startDate = newProject.start_date instanceof this.firestoreModule.Timestamp 
          ? newProject.start_date.toDate() 
          : new Date();
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 30);
        newProject.end_date = this.firestoreModule.Timestamp.fromDate(endDate);
      } else if (newProject.end_date instanceof Date) {
        // Convert Date object to Firestore Timestamp if needed
        newProject.end_date = this.firestoreModule.Timestamp.fromDate(newProject.end_date);
      }

      // Ensure the assigned arrays are initialized
      newProject.assigned_panels = newProject.assigned_panels || [];
      newProject.assigned_users = newProject.assigned_users || [];
      newProject.assigned_samples = newProject.assigned_samples || [];
      newProject.collaborators = newProject.collaborators || [];
      
      // Always add the creator as an assigned user if not already included
      if (!newProject.assigned_users.includes(userId)) {
        newProject.assigned_users.push(userId);
      }
      
      // Always add the owner as a collaborator if not already included
      if (!newProject.collaborators.includes(userId)) {
        newProject.collaborators.push(userId);
      }

      // Ensure assigned_groups is properly initialized
      newProject.assigned_groups = newProject.assigned_groups || {
        user_groups: [],
        panel_groups: [],
        sample_groups: []
      };

      // Process all assigned groups to add their entities to the project
      if (newProject.assigned_groups) {
        // Process user groups
        if (newProject.assigned_groups.user_groups && newProject.assigned_groups.user_groups.length > 0) {
          for (const groupId of newProject.assigned_groups.user_groups) {
            try {
              const group = await this.GroupRepository.read('group', groupId);
              if (group && group.users && group.users.length > 0) {
                // Add all users from the group
                for (const userId of group.users) {
                  if (!newProject.assigned_users.includes(userId)) {
                    newProject.assigned_users.push(userId);
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing user group ${groupId}: ${error.message}`);
            }
          }
        }

        // Process panel groups
        if (newProject.assigned_groups.panel_groups && newProject.assigned_groups.panel_groups.length > 0) {
          for (const groupId of newProject.assigned_groups.panel_groups) {
            try {
              const group = await this.GroupRepository.read('group', groupId);
              if (group && group.panels && group.panels.length > 0) {
                // Add all panels from the group
                for (const panelId of group.panels) {
                  if (!newProject.assigned_panels.includes(panelId)) {
                    newProject.assigned_panels.push(panelId);
                    
                    // Also add the panel's product if it has one
                    try {
                      const panel = await this.PanelRepository.read(panelId);
                      if (panel.product_id && !newProject.assigned_samples.includes(panel.product_id)) {
                        newProject.assigned_samples.push(panel.product_id);
                      }
                    } catch (panelError) {
                      console.error(`Error fetching panel ${panelId}: ${panelError.message}`);
                    }
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing panel group ${groupId}: ${error.message}`);
            }
          }
        }

        // Process sample groups
        if (newProject.assigned_groups.sample_groups && newProject.assigned_groups.sample_groups.length > 0) {
          for (const groupId of newProject.assigned_groups.sample_groups) {
            try {
              const group = await this.GroupRepository.read('group', groupId);
              if (group && group.samples && group.samples.length > 0) {
                // Add all samples from the group
                for (const sampleId of group.samples) {
                  if (!newProject.assigned_samples.includes(sampleId)) {
                    newProject.assigned_samples.push(sampleId);
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing sample group ${groupId}: ${error.message}`);
            }
          }
        }
      }

      // Create the project in the repository with all entities from groups
      await this.ProjectRepository.create(newProject);

      return newProject;
    } catch (error) {
      console.error(error);
      throw new Error("Error creating project: " + error.message);
    }
  }

  async updateProject(projectId, updateData) {
    try {
      // Convert Date objects to Timestamps if they exist in updateData
      if (updateData.start_date instanceof Date) {
        updateData.start_date = this.firestoreModule.Timestamp.fromDate(updateData.start_date);
      }
      
      if (updateData.end_date instanceof Date) {
        updateData.end_date = this.firestoreModule.Timestamp.fromDate(updateData.end_date);
      }

      // If assigned_groups are being updated, process them to synchronize entities
      if (updateData.assigned_groups) {
        // Get current project state
        const currentProject = await this.ProjectRepository.read(projectId);
        
        // Initialize tracking arrays for entities to add
        const usersToAdd = [];
        const panelsToAdd = [];
        const samplesToAdd = [];
        
        // Process user groups
        if (updateData.assigned_groups.user_groups) {
          // Find new groups that weren't in the original project
          const newUserGroups = updateData.assigned_groups.user_groups.filter(
            groupId => !currentProject.assigned_groups?.user_groups?.includes(groupId)
          );
          
          // For each new group, process its users
          for (const groupId of newUserGroups) {
            try {
              const group = await this.GroupRepository.read('group', groupId);
              if (group && group.users) {
                // Add users from the group to our tracking array
                for (const userId of group.users) {
                  if (!usersToAdd.includes(userId) && !currentProject.assigned_users?.includes(userId)) {
                    usersToAdd.push(userId);
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing user group ${groupId}: ${error.message}`);
            }
          }
        }
        
        // Process panel groups
        if (updateData.assigned_groups.panel_groups) {
          // Find new groups that weren't in the original project
          const newPanelGroups = updateData.assigned_groups.panel_groups.filter(
            groupId => !currentProject.assigned_groups?.panel_groups?.includes(groupId)
          );
          
          // For each new group, process its panels
          for (const groupId of newPanelGroups) {
            try {
              const group = await this.GroupRepository.read('group', groupId);
              if (group && group.panels) {
                // Process panels from the group
                for (const panelId of group.panels) {
                  if (!panelsToAdd.includes(panelId) && !currentProject.assigned_panels?.includes(panelId)) {
                    panelsToAdd.push(panelId);
                    
                    // Also track the panel's product if it has one
                    try {
                      const panel = await this.PanelRepository.read(panelId);
                      if (panel.product_id && 
                          !samplesToAdd.includes(panel.product_id) && 
                          !currentProject.assigned_samples?.includes(panel.product_id)) {
                        samplesToAdd.push(panel.product_id);
                      }
                    } catch (panelError) {
                      console.error(`Error fetching panel ${panelId}: ${panelError.message}`);
                    }
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing panel group ${groupId}: ${error.message}`);
            }
          }
        }
        
        // Process sample groups
        if (updateData.assigned_groups.sample_groups) {
          // Find new groups that weren't in the original project
          const newSampleGroups = updateData.assigned_groups.sample_groups.filter(
            groupId => !currentProject.assigned_groups?.sample_groups?.includes(groupId)
          );
          
          // For each new group, process its samples
          for (const groupId of newSampleGroups) {
            try {
              const group = await this.GroupRepository.read('group', groupId);
              if (group && group.samples) {
                // Add samples from the group to our tracking array
                for (const sampleId of group.samples) {
                  if (!samplesToAdd.includes(sampleId) && !currentProject.assigned_samples?.includes(sampleId)) {
                    samplesToAdd.push(sampleId);
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing sample group ${groupId}: ${error.message}`);
            }
          }
        }
        
        // Update the assigned entities arrays with the new ones from groups
        if (usersToAdd.length > 0) {
          updateData.assigned_users = [
            ...(updateData.assigned_users || currentProject.assigned_users || []),
            ...usersToAdd
          ];
        }
        
        if (panelsToAdd.length > 0) {
          updateData.assigned_panels = [
            ...(updateData.assigned_panels || currentProject.assigned_panels || []),
            ...panelsToAdd
          ];
        }
        
        if (samplesToAdd.length > 0) {
          updateData.assigned_samples = [
            ...(updateData.assigned_samples || currentProject.assigned_samples || []),
            ...samplesToAdd
          ];
        }
      }

      const updated = await this.ProjectRepository.update(projectId, updateData);
      return updated;
    } catch (error) {
      throw new Error("Error updating project: " + error.message);
    }
  }

  async deleteProject(projectId) {
    try {
      await this.ProjectRepository.delete(projectId);
    } catch (error) {
      throw new Error("Error deleting project: " + error.message);
    }
  }

  async addPanelToProject(projectId, panelId) {
    try {
      // Fetch the panel data and verify it exists
      const panel = await this.PanelRepository.read(panelId);
      
      // Check if panel has a product that should be associated
      const productId = panel.product_id || null;
      
      // Use transaction to atomically add panel and its product
      const updatedProject = await this.ProjectRepository.updatePanelAndProductAssociation(
        projectId,
        panelId,
        productId,
        true, // isAdding = true
        false // shouldRemoveProduct = false (not applicable for adding)
      );
      
      return updatedProject;
    } catch (error) {
      throw new Error(`Error adding panel to project: ${error.message}`);
    }
  }

  async removePanelFromProject(projectId, panelId) {
    try {
      // We'll need to fetch both panel and project data for validation
      const [panel, project] = await Promise.all([
        this.PanelRepository.read(panelId),
        this.ProjectRepository.read(projectId)
      ]);
      
      // Early exit if panel isn't in the project to avoid unnecessary processing
      if (!project.assigned_panels?.includes(panelId)) {
        console.log(`Panel ${panelId} not in project ${projectId}, nothing to remove`);
        return project;
      }
      
      // Get the panel's product ID
      const productId = panel.product_id || null;
      
      // Only proceed with product validation if the panel has a product and that product is in the project
      let shouldRemoveProduct = false;
      if (productId && project.assigned_samples?.includes(productId)) {
        // Get panels that would remain after removing this one
        const otherPanelIds = project.assigned_panels.filter(id => id !== panelId);
        
        if (otherPanelIds.length === 0) {
          // If there are no other panels, we can definitely remove the product
          shouldRemoveProduct = true;
          console.log(`No other panels in project ${projectId}, will remove product ${productId}`);
        } else {
          // Fetch other panels in a single batch query if possible to minimize reads
          const batchSize = 10; // Firestore 'in' query limitation
          let allPanelsUseProduct = true;
          
          // Process in batches if necessary
          for (let i = 0; i < otherPanelIds.length && allPanelsUseProduct; i += batchSize) {
            const batch = otherPanelIds.slice(i, i + batchSize);
            try {
              const otherPanels = await this.PanelRepository.batchGetPanels(batch);
              // Check if any panel in this batch uses the product
              const anyPanelUsesProduct = otherPanels.some(p => p.product_id === productId);
              if (anyPanelUsesProduct) {
                allPanelsUseProduct = false;
                break; // Exit early if we find any panel using the product
              }
            } catch (error) {
              console.error(`Error checking batch of panels: ${error.message}`);
              // Err on the side of caution if we hit errors
              allPanelsUseProduct = false;
              break;
            }
          }
          
          // If no panel uses the product, we should remove it
          shouldRemoveProduct = allPanelsUseProduct;
          
          console.log(shouldRemoveProduct 
            ? `No other panels using product ${productId}, will remove it from project ${projectId}`
            : `Product ${productId} is still used by other panels in project ${projectId}, not removing`);
        }
      }
      
      // Use transaction to atomically remove panel and potentially its product
      const updatedProject = await this.ProjectRepository.updatePanelAndProductAssociation(
        projectId,
        panelId,
        productId,
        false, // isAdding = false
        shouldRemoveProduct // Whether to also remove the product
      );
      
      return updatedProject;
    } catch (error) {
      throw new Error(`Error removing panel from project: ${error.message}`);
    }
  }

  async addUserToProject(projectId, userId) {
    try {
      // Verify that user exists
      await this.UserRepository.read(userId);
      
      // Use atomic operation to add user
      return await this.ProjectRepository.addUserAtomically(projectId, userId);
    } catch (error) {
      throw new Error(`Error adding user to project: ${error.message}`);
    }
  }

  async removeUserFromProject(projectId, userId) {
    try {
      // Use atomic operation to remove user
      return await this.ProjectRepository.removeUserAtomically(projectId, userId);
    } catch (error) {
      throw new Error(`Error removing user from project: ${error.message}`);
    }
  }

  async addSampleToProject(projectId, sampleId) {
    try {
      // Verify that sample exists
      await this.ProductRepository.read(sampleId);
      
      // Use atomic operation to add sample
      return await this.ProjectRepository.addSampleAtomically(projectId, sampleId);
    } catch (error) {
      throw new Error(`Error adding sample to project: ${error.message}`);
    }
  }

  async removeSampleFromProject(projectId, sampleId) {
    try {
      // Use atomic operation to remove sample
      return await this.ProjectRepository.removeSampleAtomically(projectId, sampleId);
    } catch (error) {
      throw new Error(`Error removing sample from project: ${error.message}`);
    }
  }

  async addCollaboratorToProject(projectId, userId) {
    try {
      // Verify that user exists
      await this.UserRepository.read(userId);
      
      // Use atomic operation to add collaborator
      return await this.ProjectRepository.addCollaboratorAtomically(projectId, userId);
    } catch (error) {
      throw new Error(`Error adding collaborator to project: ${error.message}`);
    }
  }

  async removeCollaboratorFromProject(projectId, userId) {
    try {
      // Use atomic operation to remove collaborator
      return await this.ProjectRepository.removeCollaboratorAtomically(projectId, userId);
    } catch (error) {
      throw new Error(`Error removing collaborator from project: ${error.message}`);
    }
  }

  async addGroupToProject(projectId, groupId, groupType) {
    try {
      // Verify that group exists by trying to read it
      await this.GroupRepository.read('group', groupId);
      
      // Validate group type
      if (!['user_groups', 'panel_groups', 'sample_groups'].includes(groupType)) {
        throw new Error(`Invalid group type: ${groupType}`);
      }
      
      // Use atomic operation to add group and all its members
      return await this.ProjectRepository.addGroupAtomically(projectId, groupId, groupType);
    } catch (error) {
      throw new Error(`Error adding group to project: ${error.message}`);
    }
  }

  async removeGroupFromProject(projectId, groupId, groupType) {
    try {
      // Verify that group exists by trying to read it
      await this.GroupRepository.read('group', groupId);
      
      // Validate group type
      if (!['user_groups', 'panel_groups', 'sample_groups'].includes(groupType)) {
        throw new Error(`Invalid group type: ${groupType}`);
      }
      
      // Use atomic operation to remove group and update members if needed
      return await this.ProjectRepository.removeGroupAtomically(projectId, groupId, groupType);
    } catch (error) {
      throw new Error(`Error removing group from project: ${error.message}`);
    }
  }

  async calculateProjectCompletionPercentage(projectId) {
    try {
      return await this.ProjectRepository.calculateCompletionPercentage(projectId);
    } catch (error) {
      throw new Error(`Error calculating project completion percentage: ${error.message}`);
    }
  }

  async refreshProjectStatus(projectId) {
    try {
      return await this.ProjectRepository.refreshProjectStatus(projectId);
    } catch (error) {
      throw new Error(`Error refreshing project status: ${error.message}`);
    }
  }

  /**
   * Get project with hydrated references
   * @param {string} projectId - The project ID
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object>} - Hydrated project
   */
  async getProjectWithReferences(projectId, referenceOptions = {}) {
    try {
      // Get base project
      const project = await this.ProjectRepository.read(projectId);
      if (!project) {
        throw new Error("Project not found");
      }
      
      // Determine which references to hydrate
      const { 
        references = PROJECT_REFERENCES,
        includeFullDetails = false
      } = referenceOptions;
      
      // Use the full reference map if requested
      const referenceMap = includeFullDetails ? PROJECT_FULL_REFERENCES : references;
      
      // Hydrate references
      const hydratedProject = await referenceHydrator.hydrateDocument(project, referenceMap);
      
      return hydratedProject;
    } catch (error) {
      console.error(`Error fetching project with references: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get projects with hydrated references
   * @param {string} userId - The user ID
   * @param {string} organization - Optional organization ID filter
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object[]>} - Hydrated projects
   */
  async getProjectsWithReferences(userId, organization = null, referenceOptions = {}) {
    try {
      // Get base projects
      const projects = await this.getProjects(userId, organization);
      
      // Determine which references to hydrate
      const { 
        references = PROJECT_REFERENCES,
        includeFullDetails = false,
        includeFields = null  // Optional fields to include in result
      } = referenceOptions;
      
      // Use the full reference map if requested
      const referenceMap = includeFullDetails ? PROJECT_FULL_REFERENCES : references;
      
      // Hydrate all projects in parallel
      const hydratedProjects = await referenceHydrator.hydrateDocuments(projects, referenceMap);
      
      // Filter fields if requested
      if (includeFields) {
        return hydratedProjects.map(project => {
          return includeFields.reduce((result, field) => {
            if (project[field] !== undefined) {
              result[field] = project[field];
            }
            return result;
          }, {});
        });
      }
      
      return hydratedProjects;
    } catch (error) {
      console.error(`Error fetching projects with references: ${error.message}`);
      throw error;
    }
  }

  // This method can replace the previous implementation
  async getProjectCollaborators(projectId) {
    try {
      const project = await this.getProjectWithReferences(projectId, {
        references: {
          "owner": {
            collection: "users",
            target: "owner_details",
            fields: ["first_name", "last_name", "email", "uid"] 
          },
          "collaborators": {
            collection: "users",
            isArray: true,
            target: "collaborator_details",
            fields: ["first_name", "last_name", "email", "uid"]
          },
          "assigned_users": {
            collection: "users",
            isArray: true,
            target: "users",
            fields: ["first_name", "last_name", "email", "uid"]
          }
        }
      });
      
      // Combine all user references
      const allCollaborators = [];
      
      // Add owner
      if (project.owner_details) {
        allCollaborators.push({
          ...project.owner_details,
          is_owner: true
        });
      }
      
      // Add collaborators
      if (project.collaborator_details) {
        project.collaborator_details.forEach(collaborator => {
          if (!allCollaborators.some(c => c.uid === collaborator.uid)) {
            allCollaborators.push({
              ...collaborator,
              is_owner: false
            });
          }
        });
      }
      
      // Add assigned users
      if (project.users) {
        project.users.forEach(user => {
          if (!allCollaborators.some(c => c.uid === user.uid)) {
            allCollaborators.push({
              ...user,
              is_owner: false
            });
          }
        });
      }
      
      return allCollaborators;
    } catch (error) {
      throw new Error(`Error getting project collaborators: ${error.message}`);
    }
  }

  // This method can replace the previous implementation
  async getProjectPanelDetails(projectId) {
    try {
      const project = await this.getProjectWithReferences(projectId, {
        references: {
          "assigned_panels": {
            collection: "panels",
            isArray: true,
            target: "panels",
            fields: ["name", "panel_id", "active", "product_id"],
            id_field: "panel_id",
            hydrate: {
              "product_id": {
                collection: "products",
                target: "product",
                fields: ["name", "image", "product_id"],
                id_field: "product_id"
              }
            }
          }
        }
      });
      console.log("Project:", project);
      if (!project.panels || project.panels.length === 0) {
        return [];
      }
      
      // Get response counts for each panel
      const panelDetails = await Promise.all(project.panels.map(async panel => {
        const responseCount = await this.firestoreModule.countPanelResponses(panel.panel_id);
        
        return {
          panel_id: panel.panel_id,
          name: panel.name,
          status: panel.active ? 'ongoing' : 'inactive',
          product: panel.product || { name: 'Unknown', product_id: panel.product_id },
          response_count: responseCount
        };
      }));
      
      return panelDetails;
    } catch (error) {
      throw new Error(`Error getting project panel details: ${error.message}`);
    }
  }

  // Get projects that are ending soon (within 7 days)
  async getProjectsEndingSoon(userId, organization, daysThreshold = 7) {
    try {
      const projects = await this.getProjects(userId, organization);
      const now = this.firestoreModule.Timestamp.now();
      const thresholdDate = new Date();
      thresholdDate.setDate(new Date().getDate() + daysThreshold);
      const thresholdTimestamp = this.firestoreModule.Timestamp.fromDate(thresholdDate);
      
      return projects.filter(project => {
        let endDate;
        // Check if end_date is a Firestore Timestamp
        if (project.end_date && typeof project.end_date.toDate === 'function') {
          endDate = project.end_date;
        } else if (project.end_date) {
          // Convert string or Date to Timestamp
          endDate = project.end_date instanceof Date 
            ? this.firestoreModule.Timestamp.fromDate(project.end_date)
            : this.firestoreModule.Timestamp.fromDate(new Date(project.end_date));
        } else {
          return false;
        }
        
        return project.status === 'ongoing' && 
               endDate.toMillis() > now.toMillis() && 
               endDate.toMillis() <= thresholdTimestamp.toMillis();
      });
    } catch (error) {
      throw new Error(`Error getting projects ending soon: ${error.message}`);
    }
  }

  // Get count of open projects (ongoing or scheduled)
  async getOpenProjectsCount(userId, organization) {
    try {
      const projects = await this.getProjects(userId, organization);
      return projects.filter(project => 
        project.status === 'ongoing' || project.status === 'scheduled'
      ).length;
    } catch (error) {
      throw new Error(`Error counting open projects: ${error.message}`);
    }
  }

  // Get count of completed projects
  async getCompletedProjectsCount(userId, organization) {
    try {
      const projects = await this.getProjects(userId, organization);
      return projects.filter(project => project.status === 'completed').length;
    } catch (error) {
      throw new Error(`Error counting completed projects: ${error.message}`);
    }
  }

  // Get product type distribution for all projects
  async getProductTypeDistribution(userId, organization) {
    try {
      const projects = await this.getProjects(userId, organization);
      
      // Get all sample IDs from all projects
      const sampleIds = projects.reduce((ids, project) => {
        return [...ids, ...(project.assigned_samples || [])];
      }, []);
      
      // Remove duplicates
      const uniqueSampleIds = [...new Set(sampleIds)];
      
      if (uniqueSampleIds.length === 0) {
        return [];
      }
      
      // Fetch product details
      const products = await Promise.all(
        uniqueSampleIds.map(id => this.ProductRepository.read(id))
      );
      
      // Count by product type
      const typeCounts = products.reduce((counts, product) => {
        if (!product || !product.product_type) return counts;
        
        const type = product.product_type;
        counts[type] = (counts[type] || 0) + 1;
        return counts;
      }, {});
      
      // Convert to array with percentages
      const total = Object.values(typeCounts).reduce((sum, count) => sum + count, 0);
      const distribution = Object.entries(typeCounts).map(([type, count]) => ({
        type,
        count,
        percentage: Math.round((count / total) * 100)
      }));
      
      // Sort by count descending
      return distribution.sort((a, b) => b.count - a.count);
    } catch (error) {
      throw new Error(`Error generating product type distribution: ${error.message}`);
    }
  }

  // Get dashboard stats in a single call
  async getDashboardStats(userId, organization) {
    try {
      const projects = await this.getProjects(userId, organization);
      const now = this.firestoreModule.Timestamp.now();
      const thresholdDate = new Date();
      thresholdDate.setDate(new Date().getDate() + 7); // Default 7 days for ending soon
      const thresholdTimestamp = this.firestoreModule.Timestamp.fromDate(thresholdDate);
      console.log("thresholdTimestamp: ", thresholdTimestamp);
      
      // Count by status
      const openCount = projects.filter(project => 
        project.status === 'ongoing' || project.status === 'scheduled'
      ).length;
      console.log("openCount: ", openCount);
      const completedCount = projects.filter(project => 
        project.status === 'completed'
      ).length;
      console.log("completedCount: ", completedCount);
      const endingSoonCount = projects.filter(project => {
        let endDate;
        // Check if end_date is a Firestore Timestamp
        console.log("project.end_date: ", project.end_date);
        if (project.end_date && typeof project.end_date.toDate === 'function') {
          console.log("project.end_date is a Firestore Timestamp");
          endDate = project.end_date;
        } else if (typeof project.end_date === 'string' || project.end_date instanceof Date) {
          // Convert string or Date to Timestamp
          console.log("project.end_date is a string or Date");
          endDate = project.end_date instanceof Date 
            ? this.firestoreModule.Timestamp.fromDate(project.end_date)
            : this.firestoreModule.Timestamp.fromDate(new Date(project.end_date));
        } else if (typeof project.end_date === 'object' && project.end_date._seconds) {
          // End date is a firestore timestamp but is improperly typed as such
          console.log("project.end_date is a firestore timestamp but is improperly typed as such");
          //TODO: convert _seconds and _nanoseconds to a date object
          endDate = new Date(project.end_date._seconds * 1000);
        } else {
          console.log("project.end_date is not a valid date");
          return false;
        }
        
        return project.status === 'ongoing' && 
               endDate.toMillis() > now.toMillis() && 
               endDate.toMillis() <= thresholdTimestamp.toMillis();
      }).length;
      console.log("endingSoonCount: ", endingSoonCount);
      // Get product distribution
      const sampleIds = projects.reduce((ids, project) => {
        return [...ids, ...(project.assigned_samples || [])];
      }, []);
      
      // Remove duplicates
      const uniqueSampleIds = [...new Set(sampleIds)];
      console.log("uniqueSampleIds: ", uniqueSampleIds);
      let productDistribution = [];
      
      if (uniqueSampleIds.length > 0) {
        // Fetch product details
        const products = await Promise.all(
          uniqueSampleIds.map(id => this.ProductRepository.read(id))
        );
        
        // Count by product type
        const typeCounts = products.reduce((counts, product) => {
          if (!product || !product.product_type) return counts;
          console.log("product type: ", product.product_type, product.product_id);
          const type = product.product_type;
          counts[type] = (counts[type] || 0) + 1;
          return counts;
        }, {});
        
        // Convert to array with percentages
        const total = Object.values(typeCounts).reduce((sum, count) => sum + count, 0);
        productDistribution = Object.entries(typeCounts).map(([type, count]) => ({
          type,
          count,
          percentage: Math.round((count / total) * 100)
        }));
        console.log("productDistribution: ", productDistribution);
        // Sort by count descending
        productDistribution.sort((a, b) => b.count - a.count);
      }
      
      return {
        openCount,
        completedCount,
        endingSoonCount,
        productDistribution
      };
    } catch (error) {
      throw new Error(`Error generating dashboard stats: ${error.message}`);
    }
  }

  // Handle group changes and update related projects
  async syncProjectsWithGroupChanges(groupId, groupData) {
    try {
      // Find all projects that use this group
      const affectedProjects = await this.findProjectsByGroupId(groupId);
      
      if (affectedProjects.length === 0) {
        console.log(`No projects found using group ${groupId}`);
        return { updated: 0 };
      }
      
      // Update access records for each affected project
      const updatePromises = affectedProjects.map(project => 
        this.ProjectRepository._updateProjectAccessRecords(project)
      );
      
      await Promise.all(updatePromises);
      
      console.log(`Updated access records for ${affectedProjects.length} projects using group ${groupId}`);
      return { 
        updated: affectedProjects.length,
        projects: affectedProjects.map(p => p.project_id)
      };
    } catch (error) {
      console.error(`Error syncing projects with group changes: ${error.message}`);
      throw new Error(`Failed to update projects after group change: ${error.message}`);
    }
  }
  
  // Find all projects that use a specific group
  async findProjectsByGroupId(groupId) {
    try {
      // Query Firestore for projects that use this group
      const userGroupProjects = await this.firestoreModule.queryDocuments(
        "projects",
        [["assigned_groups.user_groups", "array-contains", groupId]]
      );
      
      const panelGroupProjects = await this.firestoreModule.queryDocuments(
        "projects",
        [["assigned_groups.panel_groups", "array-contains", groupId]]
      );
      
      const sampleGroupProjects = await this.firestoreModule.queryDocuments(
        "projects",
        [["assigned_groups.sample_groups", "array-contains", groupId]]
      );
      
      // Combine and deduplicate
      const allProjects = [
        ...userGroupProjects,
        ...panelGroupProjects,
        ...sampleGroupProjects
      ];
      
      // Remove duplicates by project_id
      const uniqueProjects = Array.from(
        new Map(allProjects.map(project => [project.project_id, project])).values()
      );
      
      return uniqueProjects;
    } catch (error) {
      console.error(`Error finding projects by group ID: ${error.message}`);
      throw new Error(`Failed to find projects using group ${groupId}: ${error.message}`);
    }
  }
  
  // Refresh all projects associated with a group
  async refreshProjectsForGroup(groupId) {
    try {
      const projects = await this.findProjectsByGroupId(groupId);
      
      if (projects.length === 0) {
        return { refreshed: 0 };
      }
      
      const refreshPromises = projects.map(project => 
        this.calculateProjectCompletionPercentage(project.project_id)
          .then(() => this.refreshProjectStatus(project.project_id))
      );
      
      await Promise.all(refreshPromises);
      
      return {
        refreshed: projects.length,
        projects: projects.map(p => p.project_id)
      };
    } catch (error) {
      console.error(`Error refreshing projects for group: ${error.message}`);
      throw new Error(`Failed to refresh projects for group ${groupId}: ${error.message}`);
    }
  }
}

module.exports = ProjectService; 