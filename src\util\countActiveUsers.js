const { firestoreModule } = require('../config/index');

async function countActiveUsers(excludePatterns = []) {
  // Get all users first since Firestore doesn't support regex/pattern matching directly
  const snapshot = await firestoreModule.getUsers();
  
  // Filter users whose emails don't match any of the blacklist patterns
  const filteredUsers = snapshot.docs.filter(doc => {
    const userData = doc.data();
    const userEmail = userData.email || '';
    
    // Check if email matches any blacklist pattern
    return !excludePatterns.some(pattern => {
      // Convert wildcard pattern to RegExp
      const regexPattern = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
      return regexPattern.test(userEmail);
    });
  });

  return filteredUsers.length;
}

module.exports = countActiveUsers;
