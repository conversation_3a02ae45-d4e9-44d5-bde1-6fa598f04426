class FirebaseListeners {
    constructor(firestoreModule) {
      this.firestoreModule = firestoreModule;
      this.demographicsOptions = [];
      this.listeners = new Map();
    }
  
    createListener(collection, type) {
      const listener = this.firestoreModule.db.collection(collection)
        .onSnapshot(snapshot => {
          this[collection] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this[`${type}Keys`] = this[collection].map(option => option.id);
          console.log(`${type} options updated. Number of options:`, this[collection].length);
        }, error => {
          console.error('Error syncing demographics options:', error);
        });
      this.listeners.set(type, listener);
    }

    initializeListeners() {
      // Demographics options listener
      const demographicOptionsListener = this.firestoreModule.db.collection('demographicOptions')
        .onSnapshot(snapshot => {
          this.demographicOptions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this.demographicKeys = this.demographicOptions.map(option => option.id);
          console.log('Demographics options updated. Number of options:', this.demographicOptions.length );
        }, error => {
          console.error('Error syncing demographics options:', error);
        });
  
      this.listeners.set('demographics', demographicOptionsListener);
  
      const flavorOptionsListener = this.firestoreModule.db.collection('flavorOptions')
        .onSnapshot(snapshot => {
          this.flavorOptions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this.flavorKeys = this.flavorOptions.map(option => option.id);
          console.log('Flavor options updated. Number of options:', this.flavorOptions.length );
        }, error => {
          console.error('Error syncing flavor options:', error);
        });
  
      this.listeners.set('flavor', flavorOptionsListener);
  
      const effectOptionsListener = this.firestoreModule.db.collection('effectOptions')
        .onSnapshot(snapshot => {
          this.effectOptions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this.effectKeys = this.effectOptions.map(option => option.id);
          console.log('Effect options updated. Number of options:', this.effectOptions.length );
        }, error => {
          console.error('Error syncing effect options:', error);
        });
  
      this.listeners.set('effect', effectOptionsListener);
  
      const metricOptionsListener = this.firestoreModule.db.collection('metricOptions')
        .onSnapshot(snapshot => {
          this.metricOptions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this.metricKeys = this.metricOptions.map(option => option.id);
          console.log('Metric options updated. Number of options:', this.metricOptions.length );
        }, error => {
          console.error('Error syncing metric options:', error);
        });
  
      this.listeners.set('metric', metricOptionsListener);
  
      const questionOptionsListener = this.firestoreModule.db.collection('questions').where('isDefault', '==', true)
        .onSnapshot(snapshot => {
          this.questionOptions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this.questionKeys = this.questionOptions.map(option => option.id);
        }, error => {
          console.error('Error syncing question options:', error);
        });
  
      this.listeners.set('question', questionOptionsListener);

      this.createListener('matrixOptions', 'matrix');
      this.createListener('packagingOptions', 'packaging');
      this.createListener('consumptionOptions', 'consumption');
    }
  
    getConstants(type) {
      switch (type) {
        case 'demographics':
          return this.demographicOptions;
        case 'flavor':
          return this.flavorOptions;
        case 'effect':
          return this.effectOptions;
        case 'metric':
          return this.metricOptions;
        case 'question':
          return this.questionOptions;
        case 'matrix':
          return this.matrixOptions;
        case 'packaging':
          return this.packagingOptions;
        case 'consumption':
          return this.consumptionOptions;
        default:
          return [];
      }
    }
  
    getKeys(type) {
      switch (type) {
        case 'demographics':
          return this.demographicKeys;
        case 'flavor':
          return this.flavorKeys;
        case 'effect':
          return this.effectKeys;
        case 'metric':
          return this.metricKeys;
        case 'question':
          return this.questionKeys;
        case 'matrix':
          return this.matrixKeys;
        case 'packaging':
          return this.packagingKeys;
        case 'consumption':
          return this.consumptionKeys;
        default:
          return [];
      }
    }
  
    getOptionByName(type, name) {
      const options = this.getConstants(type);
      return options.find(option => option.name === name);
    }
  
    // Clean up listeners on shutdown
    cleanup() {
      for (const unsubscribe of this.listeners.values()) {
        unsubscribe();
      }
    }
  }
  
  module.exports = FirebaseListeners;