#!/bin/bash

# This script contains curl commands to test the group-project synchronization functionality
# Replace the placeholders with actual values before running

TOKEN="YOUR_AUTH_TOKEN_HERE"
API_URL="http://localhost:7890"
GROUP_ID="YOUR_GROUP_ID"
PROJECT_ID="YOUR_PROJECT_ID"
USER_ID="YOUR_USER_ID"

echo "==== Project-Group Integration Test Commands ===="
echo ""

echo "1. Create a new group"
echo "curl -X POST ${API_URL}/groups \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -d '{\"name\": \"Test Group\", \"description\": \"Group for testing\", \"users\": [\"${USER_ID}\"], \"panels\": [], \"samples\": []}'"
echo ""

echo "2. Create a project with group assignment"
echo "curl -X POST ${API_URL}/projects \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -d '{\"name\": \"Test Project with Group\", \"description\": \"Project for testing groups\", \"status\": \"draft\", \"assigned_groups\": {\"user_groups\": [\"${GROUP_ID}\"], \"panel_groups\": [], \"sample_groups\": []}, \"start_date\": \"$(date -Iseconds)\", \"end_date\": \"$(date -d '+30 days' -Iseconds)\"}'"
echo ""

echo "3. Update group members"
echo "curl -X PUT ${API_URL}/groups/${GROUP_ID} \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -d '{\"users\": [\"${USER_ID}\", \"NEW_USER_ID_HERE\"]}'"
echo ""

echo "4. Manually sync projects with group changes"
echo "curl -X POST ${API_URL}/projects/sync/group/${GROUP_ID}?refresh_status=true \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "5. Get project details to verify group changes"
echo "curl -X GET ${API_URL}/projects/${PROJECT_ID}?include_references=true \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "6. Delete a group and check that projects are updated"
echo "curl -X DELETE ${API_URL}/groups/${GROUP_ID} \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\""
echo ""

echo "To run these commands, replace the placeholders at the top of the script with your actual values."
echo "You can run individual commands by copying and pasting them into your terminal." 