const BaseRepository = require('./base');
const { validatePanel, validatePanelUpdate } = require('../models/panel');

class PanelRepository extends BaseRepository {
    constructor() {
        super();
    }

    async read(panelId){
        return this.getPanelByID(panelId);
    }

    async create(panelData){
        const { error } = validatePanel(panelData);
        if (error) {
            throw new Error(`Invalid panel data: ${error.message}`);
        }
        await this.firestoreModule.createDocument('panels', panelData);
        const key = `panel:${panelData.id}`;
        await this.redisCache.client.setex(key, 3600, JSON.stringify(panelData));
    }

    async getPanelByID(panelId){
        const key = `panel:${panelId}`;
        let panelData = await this.redisCache.client.get(key);

        if (!panelData) {
            // Cache miss: Fetch from Firestore (implement this method separately)
            console.log("Cache miss for panel: ", panelId);
            panelData = await this.firestoreModule.getDocument('panels', panelId);
            // Cache the result with a TTL of 1 hour
            await this.redisCache.client.setex(key, 3600, JSON.stringify(panelData));
        } else {
            panelData = JSON.parse(panelData);
        }
        return panelData;
    }

    async update(panelId, updateData){
        try {
            const { error } = validatePanelUpdate(updateData);
            if (error) {
                throw new Error(`Invalid panel data: ${error.message}`);
            }
            await this.firestoreModule.updateDocument('panels', panelId, updateData);
            const key = `panel:${panelId}`;
            await this.redisCache.client.setex(key, 3600, JSON.stringify(updateData));
            return updateData;
        } catch (error) {
            throw new Error(`Error updating panel: ${error.message}`);
        }
    }

    async delete(){}

    /**
     * Efficiently fetch multiple panels by their IDs in a single query
     * 
     * @param {string[]} panelIds - Array of panel IDs to fetch
     * @returns {Promise<Array>} - Array of panel objects
     */
    async batchGetPanels(panelIds) {
        if (!panelIds || panelIds.length === 0) {
            return [];
        }

        try {
            // Check cache first
            const pipeline = this.redisCache.client.pipeline();
            
            panelIds.forEach(id => {
                const key = `panel:${id}`;
                pipeline.get(key);
            });
            
            const results = await pipeline.exec();
            const cachedPanels = [];
            const missingIds = [];
            
            // Process cache results
            results.forEach((result, index) => {
                const [error, value] = result;
                if (!error && value) {
                    cachedPanels.push(JSON.parse(value));
                } else {
                    missingIds.push(panelIds[index]);
                }
            });
            
            // If all panels were in cache, return them
            if (missingIds.length === 0) {
                return cachedPanels;
            }
            
            // Fetch missing panels from Firestore in batches (Firestore has a limit for 'in' queries)
            const MAX_BATCH_SIZE = 10;
            let firestorePanels = [];
            
            for (let i = 0; i < missingIds.length; i += MAX_BATCH_SIZE) {
                const batch = missingIds.slice(i, i + MAX_BATCH_SIZE);
                
                const conditions = [["panel_id", "in", batch]];
                const fetchedPanels = await this.firestoreModule.queryDocuments("panels", conditions);
                
                // Cache the fetched panels
                const cachePipeline = this.redisCache.client.pipeline();
                fetchedPanels.forEach(panel => {
                    const key = `panel:${panel.panel_id}`;
                    cachePipeline.setex(key, 3600, JSON.stringify(panel));
                });
                await cachePipeline.exec();
                
                firestorePanels = [...firestorePanels, ...fetchedPanels];
            }
            
            // Combine cached and freshly fetched panels
            return [...cachedPanels, ...firestorePanels];
        } catch (error) {
            console.error(`Error in batchGetPanels: ${error.message}`);
            throw new Error(`Failed to batch fetch panels: ${error.message}`);
        }
    }

    // USER PANEL ACCESS MANAGEMENT
    
    async getUserPanelIds(userId, status = 'all') {
        try {
            let inProgressPanelIds = [];
            let completedPanelIds = [];
            
            // Get in-progress panels
            if (status === 'all' || status === 'inProgress') {
                const inProgressKey = `userPanels:${userId}`;
                inProgressPanelIds = await this.redisCache.client.smembers(inProgressKey);
                
                if (inProgressPanelIds.length === 0) {
                    // Cache miss - fetch from Firestore
                    const query = ['completed', '==', false];
                    const panels = await this.firestoreModule.getDocumentsByQuery(
                        `users/${userId}/panels`, 
                        [query]
                    );
                    inProgressPanelIds = panels.map(panel => panel.id);
                    
                    // Cache the results
                    if (inProgressPanelIds.length > 0) {
                        await this.redisCache.client.sadd(inProgressKey, ...inProgressPanelIds);
                        await this.redisCache.client.expire(inProgressKey, 3600);
                    }
                }
            }
            
            // Get completed panels
            if (status === 'all' || status === 'completed') {
                const completedKey = `completedPanels:${userId}`;
                completedPanelIds = await this.redisCache.client.smembers(completedKey);
                
                if (completedPanelIds.length === 0) {
                    // Cache miss - fetch from Firestore
                    const query = ['completed', '==', true];
                    const panels = await this.firestoreModule.getDocumentsByQuery(
                        `users/${userId}/panels`, 
                        [query]
                    );
                    completedPanelIds = panels.map(panel => panel.id);
                    
                    // Cache the results
                    if (completedPanelIds.length > 0) {
                        await this.redisCache.client.sadd(completedKey, ...completedPanelIds);
                        await this.redisCache.client.expire(completedKey, 3600);
                        
                        // Cache individual panel data
                        const pipeline = this.redisCache.client.pipeline();
                        panels.forEach(panel => {
                            pipeline.setex(
                                `completedPanels:${userId}:${panel.id}`, 
                                3600, 
                                JSON.stringify(panel)
                            );
                        });
                        await pipeline.exec();
                    }
                }
            }
            
            // Return based on requested status
            switch(status) {
                case 'inProgress':
                    return inProgressPanelIds;
                case 'completed':
                    return completedPanelIds;
                case 'all':
                default:
                    return [...inProgressPanelIds, ...completedPanelIds];
            }
        } catch (error) {
            throw new Error(`Error fetching user panel IDs: ${error.message}`);
        }
    }

    async getUserPanelData(userId, panelId) {
        try {
            // Try completed panels first
            const completedKey = `completedPanels:${userId}:${panelId}`;
            let panel = await this.redisCache.client.get(completedKey);
            
            if (!panel) {
                // Try in-progress panels
                const progressKey = `progressPanels:${userId}:${panelId}`;
                panel = await this.redisCache.client.get(progressKey);
                
                if (!panel) {
                    // Cache miss - fetch from Firestore
                    panel = await this.firestoreModule.getDocument(`users/${userId}/panels`, panelId);
                    
                    // Cache the result in the appropriate location
                    const key = panel.completed ? completedKey : progressKey;
                    await this.redisCache.client.setex(key, 3600, JSON.stringify(panel));
                } else {
                    panel = JSON.parse(panel);
                }
            } else {
                panel = JSON.parse(panel);
            }
            
            return panel;
        } catch (error) {
            throw new Error(`Error fetching user panel data: ${error.message}`);
        }
    }

    async updateUserPanelAccess(userId, panelIds) {
        try {
            // Update in Firestore
            await this.firestoreModule.updateDocument('users', userId, {
                accessiblePanels: panelIds
            });
            
            // Update in Redis
            const key = `userPanels:${userId}`;
            
            // Clear existing set and add new panel IDs
            await this.redisCache.client.del(key);
            if (panelIds.length > 0) {
                await this.redisCache.client.sadd(key, ...panelIds);
                await this.redisCache.client.expire(key, 3600);
            }
            
            return { success: true, message: 'User panel access updated successfully' };
        } catch (error) {
            throw new Error(`Error updating user panel access: ${error.message}`);
        }
    }

    // USER PANEL INTERACTION
    
    async recordPanelStart(userId, panelId, panelData = {}) {
        try {
            // Add to in-progress panels in Firestore
            panelData.completed = false;
            panelData.started = this.firestoreModule.Timestamp.now();
            
            await this.firestoreModule.createDocument(
                `users/${userId}/panels`, 
                panelData, 
                panelId
            );
            
            // Update Redis cache
            const key = `userPanels:${userId}`;
            await this.redisCache.client.sadd(key, panelId);
            
            // Cache the panel data
            const panelKey = `progressPanels:${userId}:${panelId}`;
            await this.redisCache.client.setex(
                panelKey, 
                3600, 
                JSON.stringify({ id: panelId, ...panelData })
            );
            
            return { id: panelId, ...panelData };
        } catch (error) {
            throw new Error(`Error recording panel start: ${error.message}`);
        }
    }

    async recordPanelCompletion(userId, panelId, completionData = {}) {
        try {
            // Update panel status in Firestore
            completionData.completed = true;
            completionData.completedAt = this.firestoreModule.Timestamp.now();
            
            await this.firestoreModule.updateDocument(
                `users/${userId}/panels`, 
                panelId, 
                completionData
            );
            
            // Update Redis cache
            const inProgressKey = `userPanels:${userId}`;
            const completedKey = `completedPanels:${userId}`;
            
            // Remove from in-progress and add to completed
            await this.redisCache.client.srem(inProgressKey, panelId);
            await this.redisCache.client.sadd(completedKey, panelId);
            
            // Get the updated panel data
            const panelData = await this.firestoreModule.getDocument(
                `users/${userId}/panels`, 
                panelId
            );
            
            // Cache the completed panel data
            await this.redisCache.client.setex(
                `completedPanels:${userId}:${panelId}`,
                3600,
                JSON.stringify(panelData)
            );
            
            // Remove from in-progress cache
            await this.redisCache.client.del(`progressPanels:${userId}:${panelId}`);
            
            return panelData;
        } catch (error) {
            throw new Error(`Error recording panel completion: ${error.message}`);
        }
    }

    // AVAILABLE PANELS
    
    async getAvailablePanelIds(userId, organizations=null) {
        try {
            const key = `availablePanels:${userId}`;
            let panelIds = await this.redisCache.client.smembers(key);
            console.log("Panel ids: ", panelIds);
            if (panelIds.length === 0) {
                // Get user data to find organizations
                const userOrganizations = organizations || [];
                
                if (userOrganizations.length === 0) {
                    console.log("No organizations found");
                    return [];
                }
                
                // Firestore has a limit for 'in' queries, so we need to batch
                const MAX_IN_QUERY_ELEMENTS = 10;
                let allPanelIds = [];
                
                for (let i = 0; i < userOrganizations.length; i += MAX_IN_QUERY_ELEMENTS) {
                    const batch = userOrganizations.slice(i, i + MAX_IN_QUERY_ELEMENTS);
                    const queries = [
                        ['active', '==', true],
                        ['organization_id', 'in', batch],
                        ['deleted', '==', false]
                    ];
                    console.log("Batch: ", batch);
                    const batchPanels = await this.firestoreModule.getDocumentsByQuery('panels', queries);
                    console.log("Batch panels: ", batchPanels);
                    const batchIds = batchPanels.map(panel => panel.id);
                    console.log("Batch ids: ", batchIds);
                    allPanelIds = [...allPanelIds, ...batchIds];
                    
                    // Cache individual panels
                    const pipeline = this.redisCache.client.pipeline();
                    batchPanels.forEach(panel => {
                        pipeline.setex(`panel:${panel.id}`, 3600, JSON.stringify(panel));
                    });
                    pipeline.exec();
                }
                
                // Cache the panel IDs
                if (allPanelIds.length > 0) {
                    this.redisCache.client.sadd(key, ...allPanelIds);
                    this.redisCache.client.expire(key, 3600);
                }
                
                panelIds = allPanelIds;
            }
            
            return panelIds;
        } catch (error) {
            throw new Error(`Error fetching available panel IDs: ${error.message}`);
        }
    }

}

module.exports = new PanelRepository();