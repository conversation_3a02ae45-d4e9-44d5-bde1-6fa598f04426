const { validateOrganization } = require("../models/organization");
const BaseRepository = require("./base");

class OrganizationRepository extends BaseRepository {
  constructor() {
    super();
  }

  async create(orgData, userData) {
    // Validate organization data
    const { error } = validateOrganization(orgData);
    if (error) {
      throw new Error(`Invalid organization data: ${error.message}`);
    }

    // Fetch a document key for the organization
    const _orgId = await this.firestoreModule.getDocumentKey("organizations");
    orgData.organization_id = _orgId;

    // Create org in Firestore
    const orgId = await this.firestoreModule
      .createDocument("organizations", orgData, _orgId)
      .then((docRef) => docRef.id);

    // Cache the new org data
    await this.redisCache.setOrganizationData(orgId, orgData);

    // Add org to user's organizations
    const user = { ...userData };
    user.organizations = [...new Set([...user.organizations, orgId])];
    await this.firestoreModule.updateDocument("users", user.uid, {
      organizations: user.organizations,
    });
    await this.redisCache.setUser(user.uid, user);

    return orgId;
  }

  async read(id) {
    let orgDetails = await this.redisCache.getUserOrganizationData(id);
    if (!orgDetails) {
      // If not in cache, fetch from Firestore
      orgDetails = await this.firestoreModule.getDocument("organizations", id);
      if (!orgDetails) {
        throw new Error("Organization not found");
      }
      // Cache the result
      await this.redisCache.setUserOrganizationData(id, orgDetails);
    }
    return orgDetails;
  }

  async update(orgId, updateData) {

    const currentOrg = await this.read(orgId);

    const updatedOrg = { ...currentOrg, ...updateData };
    const { error } = validateOrganization(updatedOrg);
    if (error) {
      throw new Error(`Invalid organization data: ${error.message}`);
    }

    // Update org in Firestore
    const updated = await this.firestoreModule.updateDocument(
      "organizations",
      orgId,
      updateData
    );
    if (!updated) {
      throw new Error("Organization not found");
    }

    // Update Redis cache
    await this.redisCache.setOrganizationData(orgId, updatedOrg);

    return updatedOrg;
  }

  async delete() {}
}

module.exports = OrganizationRepository;
