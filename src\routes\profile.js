const express = require('express');
const router = express.Router();

const { redisCache, firestoreModule, firebaseListeners } = require('../config');
const { authenticateUser, checkPermissions } = require('../middleware');

//Validate an affiliate code and add the organizations to the user's organizations list in firestore
//be sure to update the redis cache as well

router.all('*', authenticateUser, checkPermissions(['survey', 'admin', 'insights']));

/**
 * @swagger
 * /profile/affiliate:
 *   post:
 *     summary: Validate and add affiliate code
 *     description: Validates an affiliate code and adds associated organizations to the user's organization list
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - affiliateCode
 *             properties:
 *               affiliateCode:
 *                 type: string
 *                 description: The affiliate code to validate and add
 *     responses:
 *       200:
 *         description: Affiliate code validated and added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid affiliate code
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.post('/affiliate', async (req, res) => {
    const userId = req.user.uid;
    const affiliateCode = req.body.affiliateCode;
    console.log("Affiliate code: ", affiliateCode, req.body);
    const validatedCode = await firestoreModule.validateAffiliateCode(affiliateCode);
    if (validatedCode) {
        let user = await redisCache.getUser(userId);
        user.organizations = [...new Set([...user.organizations, ...validatedCode.organizations])];
        //console.log("User: ", user);
        await firestoreModule.updateDocument('users', userId, { organizations: user.organizations });
        await redisCache.setUser(userId, user);
        res.status(200).send({ message: 'Affiliate code validated and added to user organizations' });
    } else {
        res.status(400).send({ message: 'Invalid affiliate code' });
    }
});

/**
 * @swagger
 * /profile/affiliate:
 *   delete:
 *     summary: Remove affiliate code
 *     description: Removes the organizations associated with the affiliate code from the user's organization list
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - affiliateCode
 *             properties:
 *               affiliateCode:
 *                 type: string
 *                 description: The affiliate code to remove
 *     responses:
 *       200:
 *         description: Affiliate code removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid affiliate code
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.delete('/affiliate', async (req, res) => {
    const userId = req.user.uid;
    const affiliateCode = req.body.affiliateCode;
    const validatedCode = await firestoreModule.validateAffiliateCode(affiliateCode);
    if (validatedCode) {
        let user = await redisCache.getUser(userId);
        user.organizations = user.organizations.filter(org => org !== validatedCode.organizationId);
        await firestoreModule.updateDocument('users', userId, { organizations: user.organizations });
        await redisCache.setUser(userId, user);
        res.status(200).send({ message: 'Affiliate code removed from user organizations' });
    } else {
        res.status(400).send({ message: 'Invalid affiliate code' });
    }
});

/**
 * @swagger
 * /profile/first-login:
 *   put:
 *     summary: Update first login status
 *     description: Sets the user's firstLogin flag to false after their first login
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: First login status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.put('/first-login', async (req, res) => {
    const userId = req.user.uid;
    await firestoreModule.updateDocument('users', userId, { firstLogin: false });
    await redisCache.setUser(userId, { ...req.userData, firstLogin: false });
    res.status(200).send({ message: 'First login set to false' });
});

/**
 * @swagger
 * /profile/me:
 *   get:
 *     summary: Get user profile
 *     description: Retrieves the current user's profile information
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.get('/me', async (req, res) => {
    const userId = req.user.uid;
    console.log("Getting user: ", userId);
    const user = await redisCache.getUser(userId);
    //console.log("User: ", user);
    res.status(200).send(user);
});

/**
 * @swagger
 * /profile/me:
 *   delete:
 *     summary: Delete user account
 *     description: Deletes the current user's account and all associated data
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: User account deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.delete('/me', async (req, res) => {
    const userId = req.user.uid;
    await firestoreModule.deleteDocument('users', userId);
    await firestoreModule.auth.deleteUser(userId);
    res.status(200).send({ message: 'User profile deleted' });
});

/**
 * @swagger
 * /profile/me:
 *   put:
 *     summary: Update user profile
 *     description: Updates the current user's profile information
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *                 description: User's first name
 *               last_name:
 *                 type: string
 *                 description: User's last name
 *               demographics:
 *                 type: object
 *                 properties:
 *                   gender:
 *                     type: string
 *                   age-group:
 *                     type: string
 *                   ethnicity:
 *                     type: string
 *                   income:
 *                     type: string
 *                   hispanic:
 *                     type: string
 *     responses:
 *       200:
 *         description: User profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - must provide at least one field to update or invalid demographics
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.put('/me', async (req, res) => {
    const userId = req.user.uid;
    const { first_name, last_name, demographics } = req.body;

    // Build update data object
    const updateData = {};
    if (first_name !== undefined) updateData.first_name = first_name;
    if (last_name !== undefined) updateData.last_name = last_name;
    if (demographics !== undefined) {
        // Validate each demographic matches schema
        try {
            let demographic_docs = firebaseListeners.getConstants('demographics');
            let missingDocID = demographic_docs.find(doc => doc.type === 'missing').id;
            updateData.demographics = {
                "gender": demographics.gender ? demographics.gender : missingDocID,
                "age-group": demographics['age-group'] ? demographics['age-group'] : missingDocID,
                "ethnicity": demographics.ethnicity ? demographics.ethnicity : missingDocID,
                "income": demographics.income ? demographics.income : missingDocID,
                "hispanic": demographics.hispanic ? demographics.hispanic : missingDocID
            };
            let demographic_keys = Object.keys(updateData.demographics);
            demographic_keys.forEach(key => {
                console.log("Key: ", key, updateData.demographics[key], demographic_docs.map(doc => doc.id));
                const _key = demographic_docs.find(doc => (doc.id === updateData.demographics[key]) && ((doc.type === key) || (updateData.demographics[key] === missingDocID)));
                console.log("Key: ", _key);
                if (!_key) {
                    throw new Error(`Invalid demographic: ${key}`);
                    //return res.status(400).send({ message: `Invalid demographic: ${key}` });
                }
            });
        } catch (error) {
            return res.status(400).send({ message: error.message });
        }
    }

    if (Object.keys(updateData).length === 0) {
        return res.status(400).send({ message: 'Must provide first_name, last_name, or demographics to update' });
    }

    await firestoreModule.updateDocument('users', userId, updateData);

    // Get current user data and merge with updates
    const currentUser = await redisCache.getUser(userId);
    const updatedUser = { ...currentUser, ...updateData };
    await redisCache.setUser(userId, updatedUser);

    res.status(200).send({ message: 'User profile updated' });
});

/**
 * @swagger
 * /profile/email:
 *   put:
 *     summary: Update email address
 *     description: Updates the user's email address in both Firebase Auth and Firestore
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: New email address
 *     responses:
 *       200:
 *         description: Email updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.put('/email', async (req, res) => {
    const userId = req.user.uid;
    const email = req.body.email;
    await firestoreModule.auth.updateUser(userId, { email });
    await firestoreModule.updateDocument('users', userId, { email, emailVerified: false });
    // Get current user data and merge with updates
    const currentUser = await redisCache.getUser(userId);
    const updatedUser = { ...currentUser, email };
    await redisCache.setUser(userId, updatedUser);
    res.status(200).send({ message: 'Email updated successfully' });
});

/**
 * @swagger
 * /profile/me/organizations:
 *   get:
 *     summary: Get user organizations
 *     description: Retrieves all organizations the user belongs to
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Organizations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Organization'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.get('/me/organizations', async (req, res) => {
    const userId = req.user.uid;
    const user = await redisCache.getUser(userId);
    const organizations = await Promise.all(user.organizations.map(hydrateOrganization));
    res.status(200).send(organizations);
});

/**
 * @swagger
 * /profile/me/organizations/{organization_id}:
 *   delete:
 *     summary: Remove organization from user
 *     description: Removes the specified organization from the user's list of organizations
 *     tags: [Profile]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organization_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the organization to remove
 *     responses:
 *       200:
 *         description: Organization removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Organization not found in user organizations
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.delete('/me/organizations/:organization_id', async (req, res) => {
    const userId = req.user.uid;
    const organizationId = req.params.organization_id;
    let user = await redisCache.getUser(userId);
    const originalOrgLength = user.organizations.length;
    user.organizations = user.organizations.filter(org => org !== organizationId);
    if (user.organizations.length !== originalOrgLength) {
        await firestoreModule.updateDocument('users', userId, { organizations: user.organizations });
        await redisCache.setUser(userId, user);
        res.status(200).send({ message: 'Organization removed from user organizations' });
    } else {
        res.status(400).send({ message: 'Organization not found in user organizations' });
    }
});

module.exports = router;