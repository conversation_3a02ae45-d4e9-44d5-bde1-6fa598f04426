const Joi = require('joi');
const Table = require('cli-table3');

/**
 * @swagger
 * components:
 *   schemas:
 *     Panel:
 *       type: object
 *       required:
 *         - active
 *         - allowedOrganizations
 *         - description
 *         - invite_code
 *         - isPublic
 *         - name
 *         - organization_id
 *         - panel_id
 *         - value
 *         - budget
 *         - minutes
 *         - start_date
 *         - end_date
 *         - steps
 *         - consumption_option
 *         - minimum_certification_level
 *       properties:
 *         active:
 *           type: boolean
 *           description: Whether the panel is currently active
 *         allowedOrganizations:
 *           type: array
 *           items:
 *             type: string
 *           description: List of organization IDs that can access this panel
 *         description:
 *           type: string
 *           minLength: 2
 *           maxLength: 500
 *           description: Description of the panel
 *         invite_code:
 *           type: string
 *           pattern: ^[A-Z0-9]{4,20}$
 *           description: Unique invite code for the panel
 *         isPublic:
 *           type: boolean
 *           description: Whether the panel is publicly accessible
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: Display name of the panel
 *         organization_id:
 *           type: string
 *           description: ID of the organization that owns this panel
 *         panel_id:
 *           type: string
 *           description: ID of the panel
 *         product_id:
 *           type: string
 *           nullable: true
 *           description: ID of the primary product that this panel is associated with
 *         product_ids:
 *           type: array
 *           items:
 *             type: string
 *           description: List of additional product IDs associated with this panel
 *         value:
 *           type: number
 *           description: Value of the panel
 *         budget:
 *           type: number
 *           description: Budget of the panel
 *         minutes:
 *           type: number
 *           description: Number of minutes to complete the panel
 *         start_date:
 *           type: string
 *           format: date-time
 *           description: Date and time when the panel will be available
 *         end_date:
 *           type: string
 *           format: date-time
 *           description: Date and time when the panel will no longer be available
 *         steps:
 *           type: array
 *           items:
 *             type: string
 *           description: List of step IDs that make up the panel
 *         consumption_option:
 *           type: string
 *           description: Consumption option of the panel
 *         minimum_certification_level:
 *           type: number
 *           default: 0
 *           description: Minimum certification level required to access the panel
 */

// Define panel schema using Joi for robust validation
const panelSchema = Joi.object({
  active: Joi.boolean()
    .required()
    .description('Whether the panel is currently active'),
    
  allowedOrganizations: Joi.array()
    .items(Joi.string())
    .required()
    .description('List of organization IDs that can access this panel'),
    
  description: Joi.string()
    .required()
    .min(2)
    .max(500)
    .description('Description of the panel'),
    
  invite_code: Joi.string()
    .required()
    .pattern(/^[A-Z0-9]{4,20}$/)
    .description('Unique invite code for the panel'),
    
  isPublic: Joi.boolean()
    .required()
    .description('Whether the panel is publicly accessible'),
    
  name: Joi.string()
    .required()
    .min(2)
    .max(100)
    .description('Display name of the panel'),
    
  organization_id: Joi.string()
    .required()
    .description('ID of the organization that owns this panel'),

  panel_id: Joi.string()
    .required()
    .description('ID of the panel'),

  product_id: Joi.string()
    .optional()
    .allow(null, '')  // Allow null or empty string
    .default(null)
    .description('ID of the primary product that this panel is associated with'),

  // Add new product_ids array field
  product_ids: Joi.array()
  .items(Joi.string())
  .default([])
  .optional()
  .description('List of additional product IDs associated with this panel'),

  product_ids: Joi.array()
    .items(Joi.string())
    .required()
    .description('List of product IDs that this panel is associated with'),

  value: Joi.number()
    .required()
    .description('Value of the panel'),

  budget: Joi.number()
    .required()
    .description('Budget of the panel'),

  minutes: Joi.number()
    .required()
    .description('Number of minutes to complete the panel'),

  start_date: Joi.date()
    .required()
    .description('Date and time when the panel will be available'),

  end_date: Joi.date()
    .required()
    .default(() => new Date('4200-01-01T00:00:00Z'))
    .description('Date and time when the panel will no longer be available'),

  steps: Joi.array()
    .items(Joi.string())
    .required()
    .description('List of step IDs that make up the panel'),

  consumption_option: Joi.string()
    .required()
    .description('Consumption option of the panel'),

  minimum_certification_level: Joi.number()
    .required()
    .default(0)
    .description('Minimum certification level required to access the panel'),
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake panel document for testing
const generateFakePanel = (overrides = {}) => {
  let id = overrides.organization_id || faker.string.alphanumeric(20);
  const fakePanel = {
    active: true,
    allowedOrganizations: [id],
    description: faker.lorem.sentence(),
    invite_code: faker.string.alphanumeric(8).toUpperCase(),
    isPublic: false,
    name: faker.company.name(),
    organization_id: id,
    panel_id: faker.string.alphanumeric(20),
    product_id: null,  // Default to null since it's optional
    product_ids: [], 
    value: faker.number.int({ min: 1, max: 20 }),
    minutes: faker.number.int({ min: 1, max: 60 }),
    end_date: faker.date.future(),
    steps: ["a9k1bpXX4H57Bdl7XhaX", "wjA1uhqbIMFuZwAaCoJ8", "awja0ameTALoE0D4GlWD"],
    consumption_option: "per_use",
    minimum_certification_level: 0,
    budget: faker.number.int({ min: 1, max: 1000 }),
    start_date: faker.date.recent(),
    ...overrides
  };

  // Validate the generated data
  const { error } = validatePanel(fakePanel);
  if (error) {
    throw new Error(`Generated invalid panel: ${error.message}`);
  }

  return fakePanel;
};

// Generate multiple fake panels
const generateFakePanels = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakePanel(overrides));
};

// Validate panel document against schema
const validatePanel = (panel) => {
  return panelSchema.validate(panel, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Add this new validation function for updates
const validatePanelUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(panelSchema.describe().keys).reduce((acc, [key, value]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to panel model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validatePanel(data);
  
  if (error) {
    throw new Error(`Invalid panel data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert panel model to Firestore document
const toFirestore = (panel) => {
  const { value, error } = validatePanel(panel);
  
  if (error) {
    throw new Error(`Invalid panel data for Firestore: ${error.message}`);
  }
  
  return value;
};

// Format panel data into a CLI-friendly table
const formatPanelsTable = (panels) => {
  const table = new Table({
    head: ['Name', 'Organization', 'Public', 'Active', 'Value', 'Description'],
    colWidths: [30, 25, 10, 10, 10, 40]
  });

  panels.forEach(panel => {
    table.push([
      panel.name,
      panel.organization_id,
      panel.isPublic ? 'Yes' : 'No',
      panel.active ? 'Yes' : 'No',
      panel.value,
      panel.description?.substring(0, 37) + '...'
    ]);
  });

  return table.toString();
};

// Format detailed panel information
const formatPanelDetails = (panel) => {
  const table = new Table();

  table.push(
    { 'Name': panel.name },
    { 'Organization ID': panel.organization_id },
    { 'Panel ID': panel.panel_id },
    { 'Public': panel.isPublic ? 'Yes' : 'No' },
    { 'Active': panel.active ? 'Yes' : 'No' },
    { 'Value': panel.value },
    { 'Description': panel.description },
    { 'Start Date': panel.start_date?.toDate?.() || panel.start_date },
    { 'End Date': panel.end_date?.toDate?.() || panel.end_date },
    { 'Invite Code': panel.invite_code },
    { 'Product ID': panel.product_id }
  );

  return table.toString();
};

module.exports = {
  panelSchema,
  generateFakePanel,
  generateFakePanels,
  validatePanel,
  validatePanelUpdate,
  fromFirestore,
  toFirestore,
  formatPanelsTable,
  formatPanelDetails
};
