const { OrganizationRepository, UserRepository } = require("../repositories");
const BaseService = require("./base");

class OrganizationService extends BaseService {
  constructor() {
    super();
    this.OrganizationRepository = new OrganizationRepository();
    this.UserRepository = UserRepository;
  }

  async getOrganizationsByUserId(userId) {
    try {
      const user = await this.UserRepository.getUserById(userId);
      console.log(user);
      const userOrgs = user["organizations"];
      // Get organization details for each org ID
      let organizations = [];

      for (const orgId of userOrgs) {
        let orgDetails = await this.OrganizationRepository.read(orgId);
        if (orgDetails) {
          organizations.push({
            organization_id: orgId,
            ...orgDetails,
          });
        }
      }

      return organizations;
    } catch (error) {
      throw new Error("Error fetching organizations: " + error.message);
    }
  }

  async getOrganizationById(orgId) {
    try {
      let orgDetails = await this.redisCache.getOrganizationData(orgId);
      if (!orgDetails) {
        // If not in cache, fetch from Firestore
        orgDetails = await this.firestoreModule.getDocument(
          "organizations",
          orgId
        );
        if (!orgDetails) {
          return res.status(404).send("Organization not found");
        }
        // Cache the result
        await this.redisCache.setOrganizationData(orgId, orgDetails);
      }

      return orgDetails;
    } catch (error) {
      throw new Error("Error fetching organization details: " + error.message);
    }
  }

  async createOrganization(userId, orgData) {
    try {
      let userData = null;
      if(userId){
        userData = await this.UserRepository.getUserById(userId);
      }
      // const userData = await this.UserRepository.getUserById(userId);
      console.log("userdata", userData);

      return await this.OrganizationRepository.create(orgData, userData);
    } catch (error) {
      throw new Error("Error creating organization: " + error.message);
    }
  }

  async updateOrganization(orgId, orgData) {
    try {
      return await this.OrganizationRepository.update(orgId, orgData);
    } catch (error) {
      throw new Error("Error updating organization: " + error.message);
    }
  }
}

module.exports = new OrganizationService();
