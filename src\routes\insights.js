const express = require('express');
const router = express.Router();
const { redisCache, firestoreModule } = require('../config');
const { authenticateUser, checkPermissions, paginationMiddleware, canAccessPanelDetails, canAccessProductDetails } = require('../middleware');
const { getOverviewData, paginatePanels, paginateProducts } = require('../util');

router.all('*', authenticateUser);

/**
 * @swagger
 * /insights/overview:
 *   get:
 *     summary: Get insights overview
 *     description: Retrieves overview data for insights dashboard
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Overview data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Insights overview data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/overview', checkPermissions(['insights']), async (req, res) => {
    try {
        const userId = req.user.uid;
        const overviewData = await getOverviewData(userId);
        res.status(200).send(overviewData);
    } catch (error) {
        res.status(500).send('Error fetching overview data: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/panels:
 *   get:
 *     summary: Get panels for insights
 *     description: Retrieves all panels available for insights analysis with pagination
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter panels
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Panels retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 panels:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Panel'
 *                 currentPage:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasMore:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/panels', paginationMiddleware, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    try {
        const userId = req.user.uid;
        const organization = req.query.organization;
        const userOrgs = organization && req.userData.organizations.includes(organization) ? [organization] : req.userData.organizations;
        console.log("Insights Panel User orgs: ", userOrgs, req.userData.uid);

        // Fetch panel IDs for each organization from Redis/Firestore
        let orgPanelIds = await redisCache.getBatchOrganizationsPanelIds(userOrgs);
        let publicPanelIds = [];

        const panel_id_set = new Set([...orgPanelIds, ...publicPanelIds]);
        let panelIds = Array.from(panel_id_set);

        let paginatedPanels = await paginatePanels(panelIds, req.pagination.page, req.pagination.limit, req.pagination.startIndex);

        res.status(200).send(paginatedPanels);
    } catch (error) {
        res.status(500).send('Error fetching panels: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/products:
 *   get:
 *     summary: Get products for insights
 *     description: Retrieves all products available for insights analysis with pagination
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Optional organization ID to filter products
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 currentPage:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasMore:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/products', paginationMiddleware, checkPermissions(['insights']), async (req, res) => {
    try {
        const userId = req.user.uid;
        const organization = req.query.organization;
        // Get user's organizations from Firestore
        const userOrgs = organization && req.userData['organizations'].includes(organization) ? [organization] : req.userData['organizations'];
        console.log("Insights Product User orgs: ", userOrgs, req.userData.uid);

        // Fetch product IDs for each organization from Redis/Firestore
        let orgProductIds = await redisCache.getBatchOrganizationsProductIds(userOrgs);
        let publicProductIds = [];

        const product_id_set = new Set([...orgProductIds, ...publicProductIds]);
        let productIds = Array.from(product_id_set);
        //console.log("Pagination: ", req.pagination);
        let paginatedProducts = await paginateProducts(productIds, req.pagination.page, req.pagination.limit, req.pagination.startIndex);

        res.status(200).send(paginatedProducts);
    } catch (error) {
        res.status(500).send('Error fetching products: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/count/panels-using-product/{product_id}:
 *   get:
 *     summary: Count panels using a product
 *     description: Returns the count of panels that use a specific product
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product
 *     responses:
 *       200:
 *         description: Count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/count/panels-using-product/:product_id', checkPermissions(['insights']), async (req, res) => {
    try {
        const productId = req.params.product_id;
        const count = await redisCache.countPanelsUsingProduct(productId);
        res.status(200).send({count: count});
    } catch (error) {
        res.status(500).send('Error fetching panel count: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/panels/count/{panel_id}:
 *   get:
 *     summary: Get panel response count
 *     description: Returns the count of responses for a specific panel
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel
 *     responses:
 *       200:
 *         description: Count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/panels/count/:panel_id', checkPermissions(['insights']), async (req, res) => {
    try {
        const panelId = req.params.panel_id;
        const aggregateData = await redisCache.getAnyData("aggregates", panelId);
        //console.log("Aggregate data: ", aggregateData);
        res.status(200).send({count: aggregateData.count});
    } catch (error) {
        res.status(500).send('Error fetching panel count: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/panels/comments/{panel_id}:
 *   get:
 *     summary: Get panel comments
 *     description: Retrieves all comments for a specific panel
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel
 *     responses:
 *       200:
 *         description: Comments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   text:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel or does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/panels/comments/:panel_id', checkPermissions(['insights']), canAccessPanelDetails, async (req, res) => {
    try {
        const panelId = req.params.panel_id;
        const comments = await redisCache.getComments(panelId);
        res.status(200).send(comments);
    } catch (error) {
        res.status(500).send('Error fetching panel comments: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/panels/{panel_id}:
 *   get:
 *     summary: Get panel aggregate data
 *     description: Retrieves aggregated data for a specific panel
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel
 *     responses:
 *       200:
 *         description: Panel data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Aggregated panel data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel or does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/panels/:panel_id', checkPermissions(['insights']), canAccessPanelDetails, async (req, res) => {
    try {
        const panelId = req.params.panel_id;
        const panelData = await redisCache.getAnyData("aggregates", panelId);
        res.status(200).send(panelData);
    } catch (error) {
        res.status(500).send('Error fetching panel data: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/products/count/{product_id}:
 *   get:
 *     summary: Get product response count
 *     description: Returns the count of responses for a specific product
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product
 *     responses:
 *       200:
 *         description: Count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/products/count/:product_id', checkPermissions(['insights']), async (req, res) => {
    try {
        const productId = req.params.product_id;
        const aggregateData = await redisCache.getAnyData("aggregates", productId);
        res.status(200).send({count: aggregateData.count});
    } catch (error) {
        res.status(500).send('Error fetching product count: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/products/comments/{product_id}:
 *   get:
 *     summary: Get product comments
 *     description: Retrieves all comments for a specific product
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product
 *     responses:
 *       200:
 *         description: Comments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   text:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this product or does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/products/comments/:product_id', checkPermissions(['insights']), canAccessProductDetails, async (req, res) => {
    try {
        const productId = req.params.product_id;
        const comments = await redisCache.getComments(productId, false);
        res.status(200).send(comments);
    } catch (error) {
        res.status(500).send('Error fetching product comments: ' + error.message);
    }
});

/**
 * @swagger
 * /insights/products/{product_id}:
 *   get:
 *     summary: Get product aggregate data
 *     description: Retrieves aggregated data for a specific product
 *     tags: [Insights]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product
 *     responses:
 *       200:
 *         description: Product data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Aggregated product data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this product or does not have insights permission
 *       500:
 *         description: Server error
 */
router.get('/products/:product_id', checkPermissions(['insights']), canAccessProductDetails, async (req, res) => {
    try {
        const productId = req.params.product_id;
        const productData = await redisCache.getAnyData("aggregates", productId);
        res.status(200).send(productData);
    } catch (error) {
        res.status(500).send('Error fetching product data: ' + error.message);
    }
});

module.exports = router;