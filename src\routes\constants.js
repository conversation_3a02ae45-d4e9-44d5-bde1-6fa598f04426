const express = require('express');
const router = express.Router();

const { firebaseListeners } = require('../config');
const { authenticateUser, checkPermissions } = require('../middleware');

router.all('*', authenticateUser, checkPermissions(['survey', 'admin', 'insights']));

/**
 * @swagger
 * /constants/{type}:
 *   get:
 *     summary: Get constants by type
 *     description: Retrieves constants of a specific type (e.g., demographics, flavors, effects)
 *     tags: [Constants]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *         description: Type of constants to retrieve (e.g., demographics, flavors, effects)
 *     responses:
 *       200:
 *         description: Constants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 */
router.get("/:type", (req, res) => {
  return res.status(200).send(firebaseListeners.getConstants(req.params.type));
});

module.exports = router;