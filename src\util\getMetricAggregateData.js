const { firestoreModule } = require('../config');

const getMetricAggregateData = async (organizationId, panelId = null) => {
    const satisfaction_key = "EPWnKabDghcaN3CzDjVI";
    const quality_key = "5SoZmBEhT3CJFO4O87BF";
    const taste_key = "u3lH3C15wFX771nZ6vyk";
    const potency_key = "nAsdrjdAA1X8yqSVoLLV";
    let filters = [
        ['organization_id', '==', organizationId],
        ["question_id", "==", "wjA1uhqbIMFuZwAaCoJ8"],//Overall/Metrics question key
    ];
    if (panelId) {
        filters.push(["panel_id", "==", panelId]);
    }
    const aggregatedResponseData = await firestoreModule.getAggregateData('responses', filters, {
        numResponses: firestoreModule.AggregateField.count(),
        averageSatisfaction: firestoreModule.AggregateField.average(satisfaction_key),
        averageQuality: firestoreModule.AggregateField.average(quality_key),
        averageTaste: firestoreModule.AggregateField.average(taste_key),
        averagePotency: firestoreModule.AggregateField.average(potency_key),
    });
    return aggregatedResponseData;
}

module.exports = getMetricAggregateData;