const express = require('express');
const router = express.Router();

const { redisCache, firestoreModule, cloudStorage } = require('../config');
const { authenticateUser, checkPermissions, paginationMiddleware } = require('../middleware');
const { validateProduct } = require('../models/product');
const { ProductService } = require('../services');

// Get all products
//TODO: Fix pagination
//TODO: Implement organization products
//TODO: Think more about authorization to see products

router.all('*', authenticateUser);

/**
 * @swagger
 * /products:
 *   get:
 *     summary: Get all products
 *     description: Retrieves all products accessible to the user with pagination
 *     tags: [Products]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization
 *         schema:
 *           type: string
 *         description: Organization ID to filter products by
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 total:
 *                   type: integer
 *                   description: Total number of products
 *                 page:
 *                   type: integer
 *                   description: Current page number
 *                 limit:
 *                   type: integer
 *                   description: Number of items per page
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 *       500:
 *         description: Server error
 */
router.get('/', paginationMiddleware, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const organization = req.query.organization || null;

    const products = await ProductService.getProducts(userId, organization)

    const start = req.pagination.startIndex;
    const end = start + req.pagination.limit;
    const paginatedProducts = products.slice(start, end);

    res.status(200).send({
      products: paginatedProducts,
      total: products.length,
      page: req.pagination.page,
      limit: req.pagination.limit
    });

  } catch (error) {
    res.status(500).send('Error fetching products: ' + error.message);
  }
});

/**
 * @swagger
 * /products:
 *   post:
 *     summary: Create a new product
 *     description: Creates a new product with the provided data
 *     tags: [Products]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Product'
 *     responses:
 *       201:
 *         description: Product created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Product'
 *                 - type: object
 *                   properties:
 *                     product_id:
 *                       type: string
 *                     created:
 *                       type: object
 *                     updated:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have admin/insights permissions
 *       500:
 *         description: Server error
 */
router.post('/', checkPermissions(['admin', 'insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const productData = req.body;
    productData["organization_id"]= req.userData.organizations[0];

    const new_product = await ProductService.postProduct(productData);

    res.status(201).send({
      product_id: new_product['product_id'],
      ...new_product,
      created: {
        _seconds: new Date().getTime() / 1000,
        _nanoseconds: 0
      },
      updated: {
        _seconds: new Date().getTime() / 1000,
        _nanoseconds: 0
      }
    });

  } catch (error) {
    res.status(500).send('Error creating product: ' + error.message);
  }
});

/**
 * @swagger
 * /products/{product_id}:
 *   delete:
 *     summary: Delete a product
 *     description: Deletes a specific product by ID
 *     tags: [Products]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product to delete
 *     responses:
 *       200:
 *         description: Product deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have admin/insights permissions
 *       500:
 *         description: Server error
 */
router.delete('/:product_id', checkPermissions(['admin', 'insights']), async (req, res) => {
  try {  
      await ProductService.removeProduct(req.params.product_id);
      res.status(200).send({success: true});
  } catch (error) {
      res.status(500).send('Error deleting product: ' + error.message);
  }
});

/**
 * @swagger
 * /products/{product_id}:
 *   put:
 *     summary: Update product details
 *     description: Updates an existing product with new data
 *     tags: [Products]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Product'
 *     responses:
 *       200:
 *         description: Product updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Product'
 *                 - type: object
 *                   properties:
 *                     product_id:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have admin/insights permissions
 *       500:
 *         description: Server error
 */
router.put('/:product_id', checkPermissions(['admin', 'insights']), async (req, res) => {
  try {
    const userId = req.user.uid;
    const productId = req.params.product_id;
    const updateData = req.body;

    const updated = await ProductService.updateProduct(productId, updateData);

    res.status(200).send({
      product_id: productId,
      ...updateData
    });

  } catch (error) {
    res.status(500).send('Error updating product: ' + error.message);
  }
});

/**
 * @swagger
 * /products/{product_id}:
 *   get:
 *     summary: Get product details
 *     description: Retrieves detailed information about a specific product
 *     tags: [Products]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the product to retrieve
 *     responses:
 *       200:
 *         description: Product details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 *       500:
 *         description: Server error
 */
router.get('/:product_id', checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
  try {
    const productId = req.params.product_id;

    const productDetails = await ProductService.getProduct(productId)

    res.status(200).send(productDetails);
  } catch (error) {
    res.status(500).send('Error fetching product details: ' + error.message);
  }
});

module.exports = router;