const { validateProject, validateProjectUpdate } = require("../models/project");
const { convertFirestoreTimestampToDate } = require("../util");
const BaseRepository = require("./base");

class ProjectRepository extends BaseRepository {
  constructor() {
    super();
  }

  async create(projectData) {
    const { error } = validateProject(projectData);
    if (error) {
      throw new Error(`Invalid project data: ${error.message}`);
    }

    // Create project in Firestore
    const projectId = await this.firestoreModule.createDocument(
      "projects",
      projectData,
      projectData.project_id
    );

    // Add project ID to organization's project list in Redis
    await this.redisCache.addProjectToOrganization(
      projectData.organization_id,
      projectData.project_id
    );

    // Cache the new project data
    await this.redisCache.setProjectData(projectData.project_id, projectData);

    // Create access records for direct assignments and groups
    await this._updateProjectAccessRecords(projectData);

    return projectId;
  }

  async read(projectId) {
    let projectDetails = await this.redisCache.getProjectData(projectId);

    if (!projectDetails) {
      console.log("Project not in cache, fetching from Firestore");
      // If not in cache, fetch from Firestore
      projectDetails = await this.firestoreModule.getDocument("projects", projectId);
      if (!projectDetails) {
        throw new Error("Project not found");
      }
      // Cache the result
      await this.redisCache.setProjectData(projectId, projectDetails);
    }

    return projectDetails;
  }

  async update(id, updateData) {
    // Get current project data to merge with updates
    const currentProject = await this.read(id);
    console.log("Current project: ", currentProject);
    const updatedProject = { ...currentProject, ...updateData, start_date: convertFirestoreTimestampToDate(updateData.start_date || currentProject.start_date), end_date: convertFirestoreTimestampToDate(updateData.end_date || currentProject.end_date) };
    console.log("Updated project: ", updatedProject);
    // Validate the merged data
    const { error } = validateProject(updatedProject);
    if (error) {
      throw new Error(`Invalid project data: ${error.message}`);
    }

    const updated = await this.firestoreModule.updateDocument(
      "projects",
      id,
      updatedProject
    );
    
    if (!updated) {
      throw new Error("Project not found");
    }

    // Update Redis cache with new data
    await this.redisCache.setProjectData(id, updatedProject);

    // Update access records if assignments have changed
    if (updateData.assigned_panels || 
        updateData.assigned_users || 
        updateData.assigned_samples || 
        updateData.assigned_groups) {
      await this._updateProjectAccessRecords(updatedProject);
    }

    return updatedProject;
  }

  async delete(id) {
    // Delete access records first
    await this._deleteProjectAccessRecords(id);
    
    // Mark project as deleted in Firestore
    await this.firestoreModule.markDeleted(id, "projects");
    
    // Remove from Redis cache
    await this.redisCache.deleteProject(id);
  }

  async getProjectsByOrganization(organizationId) {
    const projectIds = await this.redisCache.getOrganizationProjectIds(organizationId);
    const projects = [];

    for (const projectId of projectIds) {
      try {
        const project = await this.read(projectId);
        projects.push(project);
      } catch (error) {
        console.error(`Error fetching project ${projectId}: ${error.message}`);
      }
    }

    return projects;
  }

  async findProjectsForEntity(entityId, entityType) {
    // Query the access index to find all projects this entity has access to
    const accessRecords = await this.firestoreModule.queryDocuments(
      "project_access",
      [
        ["entity_id", "==", entityId],
        ["entity_type", "==", entityType]
      ]
    );

    // Extract unique project IDs
    const projectIds = [...new Set(accessRecords.map(doc => doc.project_id))];
    
    // Fetch the actual projects
    const projects = [];
    for (const projectId of projectIds) {
      try {
        const project = await this.read(projectId);
        projects.push(project);
      } catch (error) {
        console.error(`Error fetching project ${projectId}: ${error.message}`);
      }
    }

    return projects;
  }

  // Private helper to update access records
  async _updateProjectAccessRecords(project) {
    // Clear existing access records
    await this._deleteProjectAccessRecords(project.project_id);
    
    // Add direct assignments
    await this._addDirectAccessRecords(project);
    
    // Add group-based assignments (to be implemented when groups are available)
    // This will be expanded when the groups functionality is available
    await this._addGroupAccessRecords(project);
  }

  // Delete all access records for a project
  async _deleteProjectAccessRecords(projectId) {
    await this.firestoreModule.deleteDocumentsWhere(
      "project_access",
      [["project_id", "==", projectId]]
    );
  }

  // Add direct access records
  async _addDirectAccessRecords(project) {
    const accessRecords = [];
    
    // Add panel access records
    for (const panelId of project.assigned_panels) {
      accessRecords.push({
        project_id: project.project_id,
        entity_id: panelId,
        entity_type: 'panel',
        access_type: 'direct',
        group_id: null
      });
    }
    
    // Add user access records
    for (const userId of project.assigned_users) {
      accessRecords.push({
        project_id: project.project_id,
        entity_id: userId,
        entity_type: 'user',
        access_type: 'direct',
        group_id: null
      });
    }

    // Add collaborator access records
    for (const userId of project.collaborators) {
      // Skip if user is already in assigned_users to avoid duplicates
      if (!project.assigned_users.includes(userId)) {
        accessRecords.push({
          project_id: project.project_id,
          entity_id: userId,
          entity_type: 'user',
          access_type: 'collaborator',
          group_id: null
        });
      }
    }
    
    // Add sample/product access records
    for (const sampleId of project.assigned_samples) {
      accessRecords.push({
        project_id: project.project_id,
        entity_id: sampleId,
        entity_type: 'sample',
        access_type: 'direct',
        group_id: null
      });
    }
    
    // Batch create the access records
    if (accessRecords.length > 0) {
      await this.firestoreModule.batchCreateDocuments("project_access", accessRecords);
    }
  }

  // Add group-based access records - this will need to be expanded when groups are implemented
  async _addGroupAccessRecords(project) {
    // Now that groups are implemented, we need to:
    // 1. Query each group to get its members
    // 2. Create access records for each member of each group
    const accessRecords = [];
    
    const { assigned_groups } = project;
    if (!assigned_groups) return;
    
    // Process user groups
    if (assigned_groups.user_groups && assigned_groups.user_groups.length > 0) {
      for (const groupId of assigned_groups.user_groups) {
        try {
          const group = await this.firestoreModule.getDocument("groups", groupId);
          if (!group) continue;
          
          // Create access records for each user in the group
          for (const userId of group.users) {
            accessRecords.push({
              project_id: project.project_id,
              entity_id: userId,
              entity_type: 'user',
              access_type: 'group',
              group_id: groupId
            });
          }
        } catch (error) {
          console.error(`Error processing user group ${groupId}: ${error.message}`);
        }
      }
    }
    
    // Process panel groups
    if (assigned_groups.panel_groups && assigned_groups.panel_groups.length > 0) {
      for (const groupId of assigned_groups.panel_groups) {
        try {
          const group = await this.firestoreModule.getDocument("groups", groupId);
          if (!group) continue;
          
          // Create access records for each panel in the group
          for (const panelId of group.panels) {
            accessRecords.push({
              project_id: project.project_id,
              entity_id: panelId,
              entity_type: 'panel',
              access_type: 'group',
              group_id: groupId
            });
          }
        } catch (error) {
          console.error(`Error processing panel group ${groupId}: ${error.message}`);
        }
      }
    }
    
    // Process sample groups
    if (assigned_groups.sample_groups && assigned_groups.sample_groups.length > 0) {
      for (const groupId of assigned_groups.sample_groups) {
        try {
          const group = await this.firestoreModule.getDocument("groups", groupId);
          if (!group) continue;
          
          // Create access records for each sample in the group
          for (const sampleId of group.samples) {
            accessRecords.push({
              project_id: project.project_id,
              entity_id: sampleId,
              entity_type: 'sample',
              access_type: 'group',
              group_id: groupId
            });
          }
        } catch (error) {
          console.error(`Error processing sample group ${groupId}: ${error.message}`);
        }
      }
    }
    
    // Batch create all access records
    if (accessRecords.length > 0) {
      await this.firestoreModule.batchCreateDocuments("project_access", accessRecords);
    }
  }

  // Calculate and update project completion percentage
  async calculateCompletionPercentage(projectId) {
    try {
      const project = await this.read(projectId);
      
      if (!project.assigned_panels || project.assigned_panels.length === 0) {
        console.log(`Project ${projectId} has no panels, completion is 0%`);
        
        // Still update the percentages to reflect this
        await this.withTransaction(async (transaction) => {
          const projectRef = this.firestoreModule.db.collection("projects").doc(projectId);
          const projectDoc = await transaction.get(projectRef);
          
          if (!projectDoc.exists) return;
          
          transaction.update(projectRef, {
            completion_percentage: 0,
            total_responses: 0
          });
        });
        
        return 0;
      }
      
      // Fetch all panels in a single batch query if possible
      const panelIds = project.assigned_panels;
      const batchSize = 10; // Firestore 'in' query limitation
      let panelDetails = [];
      
      // Split into batches if necessary due to Firestore limitations
      for (let i = 0; i < panelIds.length; i += batchSize) {
        const batch = panelIds.slice(i, i + batchSize);
        
        try {
          const batchPanels = await this.firestoreModule.queryDocuments(
            "panels",
            [["panel_id", "in", batch]],
            null,
            batch.length
          );
          
          panelDetails = [...panelDetails, ...batchPanels];
        } catch (error) {
          console.error(`Error fetching batch of panels: ${error.message}`);
        }
      }
      
      // Now fetch response counts in parallel
      const panelResponseCounts = await Promise.all(
        panelDetails.map(panel => 
          this.firestoreModule.countPanelResponses(panel.panel_id).catch(err => {
            console.error(`Error counting responses for panel ${panel.panel_id}: ${err.message}`);
            return 0;
          })
        )
      );
      
      // Calculate totals
      let totalResponses = 0;
      let totalPotentialResponses = 0;
      
      panelDetails.forEach((panel, index) => {
        const targetResponses = panel.target_responses || 10; // Default if not specified
        totalPotentialResponses += targetResponses;
        totalResponses += panelResponseCounts[index] || 0;
      });
      
      // Calculate percentage
      const percentage = totalPotentialResponses > 0 
        ? Math.min(100, Math.round((totalResponses / totalPotentialResponses) * 100))
        : 0;
      
      // Use transaction to update the project
      const projectRef = this.firestoreModule.db.collection("projects").doc(projectId);
      
      await this.withTransaction(async (transaction) => {
        const projectDoc = await transaction.get(projectRef);
        
        if (!projectDoc.exists) {
          throw new Error(`Project ${projectId} not found`);
        }
        
        transaction.update(projectRef, {
          completion_percentage: percentage,
          total_responses: totalResponses
        });
      });
      
      // Update cache with new state
      const updatedProject = {
        ...project,
        completion_percentage: percentage,
        total_responses: totalResponses
      };
      
      // Update Redis cache with new data in a single operation
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      return percentage;
    } catch (error) {
      console.error(`Error calculating completion percentage for project ${projectId}: ${error.message}`);
      return 0;
    }
  }

  async addCollaborator(projectId, userId) {
    try {
      const project = await this.read(projectId);
      
      // Check if user is already a collaborator
      if (project.collaborators.includes(userId)) {
        return project;
      }
      
      // Add user to collaborators
      project.collaborators.push(userId);
      
      // Update project
      const updatedProject = await this.update(projectId, {
        collaborators: project.collaborators
      });
      
      return updatedProject;
    } catch (error) {
      console.error(`Error adding collaborator to project ${projectId}: ${error.message}`);
      throw error;
    }
  }

  async removeCollaborator(projectId, userId) {
    try {
      const project = await this.read(projectId);
      
      // Check if user is the owner
      if (project.owner === userId) {
        throw new Error("Cannot remove the project owner from collaborators");
      }
      
      // Remove user from collaborators
      const index = project.collaborators.indexOf(userId);
      if (index !== -1) {
        project.collaborators.splice(index, 1);
        
        // Update project
        const updatedProject = await this.update(projectId, {
          collaborators: project.collaborators
        });
        
        return updatedProject;
      }
      
      return project;
    } catch (error) {
      console.error(`Error removing collaborator from project ${projectId}: ${error.message}`);
      throw error;
    }
  }

  // Refresh project status based on dates
  async refreshProjectStatus(projectId) {
    try {
      const project = await this.read(projectId);
      const now = new Date();
      let newStatus = project.status;
      
      // Determine status based on dates
      if (project.status !== 'draft' && project.status !== 'archived') {
        if (now < new Date(project.start_date)) {
          newStatus = 'scheduled';
        } else if (now >= new Date(project.start_date) && now <= new Date(project.end_date)) {
          newStatus = 'ongoing';
        } else if (now > new Date(project.end_date)) {
          newStatus = 'completed';
        }
      }
      
      // Update if status changed
      if (newStatus !== project.status) {
        await this.update(projectId, { status: newStatus });
      }
      
      return newStatus;
    } catch (error) {
      console.error(`Error refreshing project status for ${projectId}: ${error.message}`);
      throw error;
    }
  }

  // === Atomic array operations for panels ===
  
  /**
   * Add a panel to a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} panelId - The ID of the panel to add
   * @returns {Promise<Object>} - The updated project
   */
  async addPanelAtomically(projectId, panelId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        assigned_panels: this.firestoreModule.FieldValue.arrayUnion(panelId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error adding panel to project: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a panel from a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} panelId - The ID of the panel to remove
   * @returns {Promise<Object>} - The updated project
   */
  async removePanelAtomically(projectId, panelId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        assigned_panels: this.firestoreModule.FieldValue.arrayRemove(panelId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error removing panel from project: ${error.message}`);
      throw error;
    }
  }
  
  // === Atomic array operations for users ===
  
  /**
   * Add a user to a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} userId - The ID of the user to add
   * @returns {Promise<Object>} - The updated project
   */
  async addUserAtomically(projectId, userId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        assigned_users: this.firestoreModule.FieldValue.arrayUnion(userId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error adding user to project: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a user from a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} userId - The ID of the user to remove
   * @returns {Promise<Object>} - The updated project
   */
  async removeUserAtomically(projectId, userId) {
    try {
      // Get current project to check if user is owner (fetch directly from Firestore)
      const project = await this.firestoreModule.getDocument("projects", projectId);
      
      // Don't allow removing the owner
      if (project.owner === userId) {
        throw new Error("Cannot remove the project owner");
      }
      
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        assigned_users: this.firestoreModule.FieldValue.arrayRemove(userId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error removing user from project: ${error.message}`);
      throw error;
    }
  }
  
  // === Atomic array operations for samples ===
  
  /**
   * Add a sample to a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} sampleId - The ID of the sample to add
   * @returns {Promise<Object>} - The updated project
   */
  async addSampleAtomically(projectId, sampleId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        assigned_samples: this.firestoreModule.FieldValue.arrayUnion(sampleId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error adding sample to project: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a sample from a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} sampleId - The ID of the sample to remove
   * @returns {Promise<Object>} - The updated project
   */
  async removeSampleAtomically(projectId, sampleId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        assigned_samples: this.firestoreModule.FieldValue.arrayRemove(sampleId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error removing sample from project: ${error.message}`);
      throw error;
    }
  }
  
  // === Atomic array operations for collaborators ===
  
  /**
   * Run a transaction with common error handling and consistent return pattern
   * @param {function} txFn - Function that receives the transaction and performs operations
   * @returns {Promise<any>} - Result of the transaction
   */
  async withTransaction(txFn) {
    try {
      return await this.firestoreModule.runTransaction(txFn);
    } catch (error) {
      console.error(`Transaction failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update panel and its associated product in a single transaction
   * 
   * @param {string} projectId - The ID of the project
   * @param {string} panelId - The ID of the panel
   * @param {string|null} productId - The ID of the associated product (null if not applicable)
   * @param {boolean} isAdding - Whether we're adding (true) or removing (false) the panel
   * @param {boolean} shouldRemoveProduct - Whether to remove the product when removing a panel
   * @returns {Promise<Object>} - The updated project
   */
  async updatePanelAndProductAssociation(projectId, panelId, productId, isAdding, shouldRemoveProduct = false) {
    const projectRef = this.firestoreModule.db.collection("projects").doc(projectId);
    const cacheKey = projectId;
    
    try {
      // First invalidate the cache before the transaction to prevent stale reads
      await this.redisCache.deleteProjectData(cacheKey);
      
      // Use a transaction to ensure atomic updates
      const updatedProject = await this.withTransaction(async (transaction) => {
        // Get current project data in the transaction
        const projectDoc = await transaction.get(projectRef);
        
        if (!projectDoc.exists) {
          throw new Error(`Project ${projectId} not found`);
        }
        
        const project = { ...projectDoc.data() };
        
        // Skip the update if no changes needed
        // For adding: if panel already exists
        if (isAdding && project.assigned_panels?.includes(panelId)) {
          console.log(`Panel ${panelId} already exists in project ${projectId}, no changes needed`);
          return project;
        }
        
        // For removing: if panel doesn't exist
        if (!isAdding && !project.assigned_panels?.includes(panelId)) {
          console.log(`Panel ${panelId} not found in project ${projectId}, no changes needed`);
          return project;
        }
        
        // Prepare updates object - include all changes in this single update
        const updates = {};
        
        // Handle panel association
        if (isAdding) {
          // Add panel 
          updates.assigned_panels = this.firestoreModule.FieldValue.arrayUnion(panelId);
          console.log(`Adding panel ${panelId} to project ${projectId}`);
          
          // Add product if applicable and not already present
          if (productId && !project.assigned_samples?.includes(productId)) {
            updates.assigned_samples = this.firestoreModule.FieldValue.arrayUnion(productId);
            console.log(`Adding product ${productId} to project ${projectId}`);
          }
        } else {
          // Remove panel
          updates.assigned_panels = this.firestoreModule.FieldValue.arrayRemove(panelId);
          console.log(`Removing panel ${panelId} from project ${projectId}`);
          
          // If product is provided and should be removed
          if (shouldRemoveProduct && productId && project.assigned_samples?.includes(productId)) {
            updates.assigned_samples = this.firestoreModule.FieldValue.arrayRemove(productId);
            console.log(`Removing product ${productId} from project ${projectId}`);
          }
        }
        
        // Apply the update in the transaction
        transaction.update(projectRef, updates);
        
        // Create an updated version of the document to return
        // This reflects what Firestore will look like after the transaction
        const updatedPanels = isAdding 
          ? [...(project.assigned_panels || []), panelId].filter((v, i, a) => a.indexOf(v) === i) // add unique
          : (project.assigned_panels || []).filter(id => id !== panelId); // remove
          
        const updatedSamples = isAdding && productId && !project.assigned_samples?.includes(productId)
          ? [...(project.assigned_samples || []), productId].filter((v, i, a) => a.indexOf(v) === i) // add unique
          : shouldRemoveProduct && productId
            ? (project.assigned_samples || []).filter(id => id !== productId) // remove
            : project.assigned_samples || [];
        
        // Return the projected new state
        return {
          ...project,
          assigned_panels: updatedPanels,
          assigned_samples: updatedSamples
        };
      });
      
      // Single cache update at the end
      await this.redisCache.setProjectData(cacheKey, updatedProject);
      
      // Update access records only once, after all changes are complete
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      // Attempt to refresh the cache in case of error to ensure consistency
      try {
        const latestProject = await this.firestoreModule.getDocument("projects", projectId);
        await this.redisCache.setProjectData(cacheKey, latestProject);
      } catch (cacheError) {
        console.error(`Failed to refresh cache after error: ${cacheError.message}`);
      }
      
      throw error;
    }
  }

  /**
   * Add a collaborator to a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} userId - The ID of the user to add as a collaborator
   * @returns {Promise<Object>} - The updated project
   */
  async addCollaboratorAtomically(projectId, userId) {
    try {
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        collaborators: this.firestoreModule.FieldValue.arrayUnion(userId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error adding collaborator to project: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a collaborator from a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} userId - The ID of the user to remove as collaborator
   * @returns {Promise<Object>} - The updated project
   */
  async removeCollaboratorAtomically(projectId, userId) {
    try {
      // Get current project to check if user is owner (fetch directly from Firestore)
      const project = await this.firestoreModule.getDocument("projects", projectId);
      
      // Don't allow removing the owner
      if (project.owner === userId) {
        throw new Error("Cannot remove the project owner from collaborators");
      }
      
      // Update Firestore atomically
      await this.firestoreModule.updateDocument("projects", projectId, {
        collaborators: this.firestoreModule.FieldValue.arrayRemove(userId)
      });
      
      // Invalidate cache first
      await this.redisCache.deleteProjectData(projectId);
      
      // Fetch the updated project directly from Firestore
      const updatedProject = await this.firestoreModule.getDocument("projects", projectId);
      
      // Update cache with new data
      await this.redisCache.setProjectData(projectId, updatedProject);
      
      // Update access records
      await this._updateProjectAccessRecords(updatedProject);
      
      return updatedProject;
    } catch (error) {
      console.error(`Error removing collaborator from project: ${error.message}`);
      throw error;
    }
  }

  // === Atomic operations for groups ===
  
  /**
   * Add a group to a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} groupId - The ID of the group to add
   * @param {string} groupType - Type of group (user_groups, panel_groups, or sample_groups)
   * @returns {Promise<Object>} - The updated project
   */
  async addGroupAtomically(projectId, groupId, groupType) {
    try {
      if (!['user_groups', 'panel_groups', 'sample_groups'].includes(groupType)) {
        throw new Error(`Invalid group type: ${groupType}`);
      }

      // First, validate that the group exists
      const group = await this.firestoreModule.getDocument("groups", groupId);
      if (!group) {
        throw new Error(`Group ${groupId} not found`);
      }

      // Get the project to prepare for updates
      const project = await this.read(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      // Ensure assigned_groups structure exists
      if (!project.assigned_groups) {
        project.assigned_groups = {
          user_groups: [],
          panel_groups: [],
          sample_groups: []
        };
      }

      // Check if the group is already assigned
      if (project.assigned_groups[groupType]?.includes(groupId)) {
        return project; // Already assigned, no changes needed
      }

      // Use transaction to atomically update the project and add all entities
      return await this.withTransaction(async (transaction) => {
        const projectRef = this.firestoreModule.db.collection("projects").doc(projectId);
        const projectDoc = await transaction.get(projectRef);
        
        if (!projectDoc.exists) {
          throw new Error(`Project ${projectId} not found in transaction`);
        }
        
        const currentProject = projectDoc.data();
        
        // Prepare the initial update for the group
        const updates = {
          [`assigned_groups.${groupType}`]: this.firestoreModule.FieldValue.arrayUnion(groupId),
          updated_at: new Date()
        };
        
        // Now add all entities from the group
        if ((groupType === 'user_groups' || groupType) && group.users && group.users.length > 0) {
          // Add all users not already in the project
          const usersToAdd = group.users.filter(uid => 
            !currentProject.assigned_users || !currentProject.assigned_users.includes(uid)
          );
          
          if (usersToAdd.length > 0) {
            // Need to get current array first since we can't combine arrayUnion with setting a new array
            let updatedUsers = [...(currentProject.assigned_users || [])];
            updatedUsers = [...new Set([...updatedUsers, ...usersToAdd])];
            updates.assigned_users = updatedUsers;
          }
        }
        else if ((groupType === 'panel_groups' || groupType) && group.panels && group.panels.length > 0) {
          // Add all panels not already in the project
          const panelsToAdd = group.panels.filter(pid => 
            !currentProject.assigned_panels || !currentProject.assigned_panels.includes(pid)
          );
          
          if (panelsToAdd.length > 0) {
            // Need to get current array first since we can't combine arrayUnion with setting a new array
            let updatedPanels = [...(currentProject.assigned_panels || [])];
            updatedPanels = [...new Set([...updatedPanels, ...panelsToAdd])];
            updates.assigned_panels = updatedPanels;
          }
        }
        else if ((groupType === 'sample_groups' || groupType) && group.samples && group.samples.length > 0) {
          // Add all samples not already in the project
          const samplesToAdd = group.samples.filter(sid => 
            !currentProject.assigned_samples || !currentProject.assigned_samples.includes(sid)
          );
          
          if (samplesToAdd.length > 0) {
            // Need to get current array first since we can't combine arrayUnion with setting a new array
            let updatedSamples = [...(currentProject.assigned_samples || [])];
            updatedSamples = [...new Set([...updatedSamples, ...samplesToAdd])];
            updates.assigned_samples = updatedSamples;
          }
        }
        
        // Apply all updates in one go
        transaction.update(projectRef, updates);
        
        // Return what the updated project will be (approximate)
        const updatedProject = {
          ...currentProject,
          ...updates,
          // Handle the nested assigned_groups field specially
          assigned_groups: {
            ...(currentProject.assigned_groups || {
              user_groups: [],
              panel_groups: [],
              sample_groups: []
            }),
            [groupType]: [...(currentProject.assigned_groups?.[groupType] || []), groupId]
          }
        };
        
        return updatedProject;
      });
    } catch (error) {
      console.error(`Error adding group to project: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Remove a group from a project using atomic Firestore operations
   * @param {string} projectId - The ID of the project
   * @param {string} groupId - The ID of the group to remove
   * @param {string} groupType - Type of group (user_groups, panel_groups, or sample_groups)
   * @returns {Promise<Object>} - The updated project
   */
  async removeGroupAtomically(projectId, groupId, groupType) {
    try {
      if (!['user_groups', 'panel_groups', 'sample_groups'].includes(groupType)) {
        throw new Error(`Invalid group type: ${groupType}`);
      }

      // First, validate that the group exists
      const group = await this.firestoreModule.getDocument("groups", groupId);
      if (!group) {
        throw new Error(`Group ${groupId} not found`);
      }

      // Get the project to prepare for updates
      const project = await this.read(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }
      
      // Check if the group is assigned
      if (!project.assigned_groups || !project.assigned_groups[groupType]?.includes(groupId)) {
        return project; // Not assigned, no changes needed
      }

      // Use transaction to atomically update the project and remove entities if needed
      return await this.withTransaction(async (transaction) => {
        const projectRef = this.firestoreModule.db.collection("projects").doc(projectId);
        const projectDoc = await transaction.get(projectRef);
        
        if (!projectDoc.exists) {
          throw new Error(`Project ${projectId} not found in transaction`);
        }
        
        const currentProject = projectDoc.data();
        
        // Prepare the initial update to remove the group
        const updates = {
          [`assigned_groups.${groupType}`]: this.firestoreModule.FieldValue.arrayRemove(groupId),
          updated_at: new Date()
        };
        
        // Now handle removal of entities from the group
        if (groupType === 'user_groups' && group.users && group.users.length > 0) {
          // We need to check if any users should be removed
          for (const userId of group.users) {
            // Skip if the user isn't in the project
            if (!currentProject.assigned_users?.includes(userId)) continue;
            
            // Skip if user is the owner
            if (currentProject.owner === userId) continue;
            
            // Check if user is in other groups
            const isInOtherGroup = (currentProject.assigned_groups?.user_groups || [])
              .filter(gid => gid !== groupId) // Exclude the group being removed
              .some(async (otherGroupId) => {
                try {
                  const otherGroup = await this.firestoreModule.getDocument("groups", otherGroupId);
                  return otherGroup?.users?.includes(userId);
                } catch (err) {
                  console.error(`Error checking user in other group: ${err.message}`);
                  return false;
                }
              });
            
            // Skip if directly assigned
            const isDirectlyAssigned = currentProject.assigned_users?.includes(userId);
            
            // If not in other groups and not directly assigned, remove user
            if (!isInOtherGroup && !isDirectlyAssigned) {
              updates.assigned_users = this.firestoreModule.FieldValue.arrayRemove(userId);
            }
          }
        }
        else if (groupType === 'panel_groups' && group.panels && group.panels.length > 0) {
          // We need to check if any panels should be removed
          for (const panelId of group.panels) {
            // Skip if the panel isn't in the project
            if (!currentProject.assigned_panels?.includes(panelId)) continue;
            
            // Check if panel is in other groups
            const isInOtherGroup = (currentProject.assigned_groups?.panel_groups || [])
              .filter(gid => gid !== groupId) // Exclude the group being removed
              .some(async (otherGroupId) => {
                try {
                  const otherGroup = await this.firestoreModule.getDocument("groups", otherGroupId);
                  return otherGroup?.panels?.includes(panelId);
                } catch (err) {
                  console.error(`Error checking panel in other group: ${err.message}`);
                  return false;
                }
              });
            
            // Skip if directly assigned
            const isDirectlyAssigned = currentProject.assigned_panels?.includes(panelId);
            
            // If not in other groups and not directly assigned, remove panel
            if (!isInOtherGroup && !isDirectlyAssigned) {
              updates.assigned_panels = this.firestoreModule.FieldValue.arrayRemove(panelId);
              
              // Check if we should also remove the panel's product
              try {
                const panel = await this.firestoreModule.getDocument("panels", panelId);
                const productId = panel?.product_id;
                
                if (productId && currentProject.assigned_samples?.includes(productId)) {
                  // Check if product is used by other panels
                  const isProductUsedByOtherPanels = (currentProject.assigned_panels || [])
                    .filter(pid => pid !== panelId) // Exclude the panel being removed
                    .some(async (otherPanelId) => {
                      try {
                        const otherPanel = await this.firestoreModule.getDocument("panels", otherPanelId);
                        return otherPanel?.product_id === productId;
                      } catch (err) {
                        console.error(`Error checking product in other panel: ${err.message}`);
                        return false;
                      }
                    });
                  
                  // If not used by other panels, remove the product
                  if (!isProductUsedByOtherPanels) {
                    updates.assigned_samples = this.firestoreModule.FieldValue.arrayRemove(productId);
                  }
                }
              } catch (err) {
                console.error(`Error processing panel's product: ${err.message}`);
              }
            }
          }
        }
        else if (groupType === 'sample_groups' && group.samples && group.samples.length > 0) {
          // We need to check if any samples should be removed
          for (const sampleId of group.samples) {
            // Skip if the sample isn't in the project
            if (!currentProject.assigned_samples?.includes(sampleId)) continue;
            
            // Check if sample is in other groups
            const isInOtherGroup = (currentProject.assigned_groups?.sample_groups || [])
              .filter(gid => gid !== groupId) // Exclude the group being removed
              .some(async (otherGroupId) => {
                try {
                  const otherGroup = await this.firestoreModule.getDocument("groups", otherGroupId);
                  return otherGroup?.samples?.includes(sampleId);
                } catch (err) {
                  console.error(`Error checking sample in other group: ${err.message}`);
                  return false;
                }
              });
            
            // Skip if directly assigned
            const isDirectlyAssigned = currentProject.assigned_samples?.includes(sampleId);
            
            // Skip if used by any panel
            const isUsedByPanel = (currentProject.assigned_panels || [])
              .some(async (panelId) => {
                try {
                  const panel = await this.firestoreModule.getDocument("panels", panelId);
                  return panel?.product_id === sampleId;
                } catch (err) {
                  console.error(`Error checking sample in panel: ${err.message}`);
                  return false;
                }
              });
            
            // If not in other groups, not directly assigned, and not used by panels, remove sample
            if (!isInOtherGroup && !isDirectlyAssigned && !isUsedByPanel) {
              updates.assigned_samples = this.firestoreModule.FieldValue.arrayRemove(sampleId);
            }
          }
        }
        
        // Apply all updates in one go
        transaction.update(projectRef, updates);
        
        // Fetch the updated project after transaction
        const updatedProject = await this.read(projectId);
        
        // Update cache with new data
        await this.redisCache.setProjectData(projectId, updatedProject);
        
        // Update access records
        await this._updateProjectAccessRecords(updatedProject);
        
        return updatedProject;
      });
    } catch (error) {
      console.error(`Error removing group from project: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ProjectRepository; 