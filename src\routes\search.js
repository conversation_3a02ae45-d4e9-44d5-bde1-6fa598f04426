const express = require('express');
const router = express.Router();
const { redisCache } = require('../config');
const { hydratePanel } = require('../util');
const { authenticateUser, checkPermissions } = require('../middleware');

//Search for panels
//TODO: Add pagination
//TODO: Make search actually real
router.get('/', authenticateUser, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    const userId = req.user.uid;
    const searchTerm = req.query.q;
    //Grab the first ten public panels and hydrate them
    const publicPanels = await redisCache.getPublicPanels();
    const panelIds = publicPanels.slice(0, 10);
    const hydratedPanels = await Promise.all(panelIds.map(hydratePanel));
    res.status(200).send(hydratedPanels);
  });

module.exports = router;