/**
 * @swagger
 * components:
 *   schemas:
 *     Option:
 *       type: object
 *       required:
 *         - label
 *         - organization_id
 *         - question_id
 *         - option_id
 *       properties:
 *         label:
 *           type: string
 *           description: The label of the option
 *         instructions:
 *           type: string
 *           description: Instructions for the option
 *         sliderCenter:
 *           type: string
 *           nullable: true
 *           description: The center value of the slider option
 *         sliderLeft:
 *           type: string
 *           nullable: true
 *           description: The left value of the slider option
 *         sliderRight:
 *           type: string
 *           nullable: true
 *           description: The right value of the slider option
 *         organization_id:
 *           type: string
 *           description: The organization ID of the option
 *         question_id:
 *           type: string
 *           description: The question ID the option belongs to
 *         option_id:
 *           type: string
 *           description: Unique identifier for the option
 *       example:
 *         label: "Aroma Intensity"
 *         instructions: "Rate the intensity of the aroma"
 *         sliderLeft: "Very weak"
 *         sliderCenter: "Moderate"
 *         sliderRight: "Very intense"
 *         organization_id: "org123456789"
 *         question_id: "question123456789"
 *         option_id: "option123456789"
 */

const Joi = require('joi');
const { faker } = require('@faker-js/faker');

// Define option schema using Joi for robust validation
const optionSchema = Joi.object({
    label: Joi.string()
        .required()
        .description('The label of the option'),

    instructions: Joi.string()
        .optional()
        .description('Instructions for the option'),

    sliderCenter: Joi.string()
        .optional()
        .allow(null)
        .description('The value of the option'),

    sliderLeft: Joi.string()
        .optional()
        .allow(null)
        .description('The left value of the option'),

    sliderRight: Joi.string()
        .optional()
        .allow(null)
        .description('The right value of the option'),

    organization_id: Joi.string()
        .required()
        .description('The organization ID of the option'),

    question_id: Joi.string()
        .required()
        .description('The question ID of the option'),

    option_id: Joi.string()
        .required()
        .description('Unique identifier for the option'),
});

// Generate a fake option document for testing
defaultFakeOption = () => ({
    label: faker.lorem.words(2),
    sliderCenter: faker.lorem.word(),
    sliderLeft: faker.lorem.word(),
    sliderRight: faker.lorem.word(),
    organization_id: faker.string.alphanumeric(20),
    question_id: faker.string.alphanumeric(20),
    option_id: faker.string.alphanumeric(20),
});

const generateFakeOption = (overrides = {}) => {
    const fakeOption = {
        ...defaultFakeOption(),
        ...overrides
    };

    // Only add instructions if not explicitly excluded in overrides
    if (!overrides.hasOwnProperty('instructions')) {
        fakeOption.instructions = faker.lorem.sentence();
    }

    const { error } = validateOption(fakeOption);
    if (error) {
        throw new Error(`Generated invalid option: ${error.message}`);
    }
    return fakeOption;
};

const generateFakeOptions = (count = 1, overrides = {}) => {
    return Array.from({ length: count }, () => generateFakeOption(overrides));
};

const validateOption = (option) => {
    return optionSchema.validate(option, {
        abortEarly: false,
        stripUnknown: true,
        presence: 'required'
    });
};

const fromFirestore = (snapshot) => {
    const data = snapshot.data();
    const { value, error } = validateOption(data);
    if (error) {
        throw new Error(`Invalid option data from Firestore: ${error.message}`);
    }
    return value;
};

const toFirestore = (option) => {
    const { value, error } = validateOption(option);
    if (error) {
        throw new Error(`Invalid option data for Firestore: ${error.message}`);
    }
    return value;
};

module.exports = {
    optionSchema,
    generateFakeOption,
    generateFakeOptions,
    validateOption,
    fromFirestore,
    toFirestore
};