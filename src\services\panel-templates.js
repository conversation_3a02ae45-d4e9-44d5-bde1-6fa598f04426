const BaseService = require('./base');
const PanelTemplateRepository = require('../repositories/panel-templates');

class PanelTemplateService extends BaseService {
    constructor() {
        super();
        this.panelTemplateRepository = PanelTemplateRepository;
    }

    /**
     * Get a panel template by ID
     * @param {string} templateId - The ID of the template to retrieve
     * @returns {Promise<Object>} - The panel template data
     */
    async getTemplate(templateId) {
        try {
            const template = await this.panelTemplateRepository.getTemplateById(templateId);
            return template;
        } catch (error) {
            throw new Error(`Error fetching template: ${error.message}`);
        }
    }

    /**
     * Create a new panel template
     * @param {Object} templateData - The template data to create
     * @returns {Promise<Object>} - The created template
     */
    async createPanelTemplate(templateData) {
        try {
            // Generate template ID if not provided
            if (!templateData.template_id) {
                templateData.template_id = await this.firestoreModule.getDocumentKey('panel_templates');
            }
            
            // Set default values if not provided
            templateData.active = templateData.active !== undefined ? templateData.active : true;
            templateData.deleted = false;
            
            // Create the template
            const newTemplate = await this.panelTemplateRepository.create(templateData);
            
            return {
                template_id: templateData.template_id,
                ...newTemplate
            };
        } catch (error) {
            throw new Error(`Error creating panel template: ${error.message}`);
        }
    }

    /**
     * Update a panel template
     * @param {string} templateId - The ID of the template to update
     * @param {Object} updateData - The data to update
     * @returns {Promise<Object>} - The updated template
     */
    async updatePanelTemplate(templateId, updateData) {
        try {
            // Update the template
            const updatedTemplate = await this.panelTemplateRepository.update(templateId, updateData);
            
            return {
                template_id: templateId,
                ...updatedTemplate
            };
        } catch (error) {
            throw new Error(`Error updating panel template: ${error.message}`);
        }
    }

    /**
     * Delete a panel template (soft delete)
     * @param {string} templateId - The ID of the template to delete
     * @returns {Promise<boolean>} - Success indicator
     */
    async deletePanelTemplate(templateId) {
        try {
            await this.panelTemplateRepository.delete(templateId);
            return true;
        } catch (error) {
            throw new Error(`Error deleting panel template: ${error.message}`);
        }
    }

    /**
     * Get all panel templates for an organization
     * @param {string} userId - The user ID requesting the templates
     * @param {string} organizationId - The organization ID
     * @returns {Promise<Array>} - Array of templates
     */
    async getPanelTemplates(userId, organizationId) {
        try {
            const templates = await this.panelTemplateRepository.getTemplatesByOrganization(organizationId);
            return templates;
        } catch (error) {
            throw new Error(`Error fetching panel templates: ${error.message}`);
        }
    }

    /**
     * Get all panel templates accessible by a user
     * @param {string} userId - The user ID
     * @param {string} organizationId - The organization ID (optional)
     * @returns {Promise<Array>} - Array of templates
     */
    async getAccessibleTemplates(userId, organizationId = null) {
        try {
            const templates = await this.panelTemplateRepository.getAccessibleTemplates(userId, organizationId);
            return templates;
        } catch (error) {
            throw new Error(`Error fetching accessible templates: ${error.message}`);
        }
    }

    /**
     * Clone a panel template
     * @param {string} templateId - The ID of the template to clone
     * @param {string} organizationId - The organization ID for the new template
     * @param {Object} overrides - Properties to override in the cloned template
     * @returns {Promise<Object>} - The cloned template
     */
    async clonePanelTemplate(templateId, organizationId, overrides = {}) {
        try {
            // Get the source template
            const sourceTemplate = await this.panelTemplateRepository.getTemplateById(templateId);
            
            // Create a new template ID
            const newTemplateId = await this.firestoreModule.getDocumentKey('panel_templates');
            
            // Create the cloned template data
            const clonedTemplateData = {
                ...sourceTemplate,
                template_id: newTemplateId,
                organization_id: organizationId,
                name: overrides.name || `${sourceTemplate.name} (Copy)`,
                usedBy: [],
                ...overrides
            };
            
            // Remove any properties that shouldn't be copied
            delete clonedTemplateData.id;
            
            // Create the new template
            const newTemplate = await this.createPanelTemplate(clonedTemplateData);
            
            return newTemplate;
        } catch (error) {
            throw new Error(`Error cloning panel template: ${error.message}`);
        }
    }
}

module.exports = PanelTemplateService;
