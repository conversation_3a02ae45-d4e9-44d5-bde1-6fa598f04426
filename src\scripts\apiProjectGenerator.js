#!/usr/bin/env node

require('dotenv').config();
const { Command } = require('commander');
const axios = require('axios');
const { faker } = require('@faker-js/faker');

// Dynamic import for inquirer (ESM module)
let inquirer;

const API_BASE_URL = 'http://localhost:7890';
let authToken = null;

const program = new Command();

program
  .name('api-project-generator')
  .description('Generate projects via API for development')
  .version('1.0.0');

program
  .option('-c, --count <number>', 'Number of projects to generate', '2')
  .option('-e, --email <email>', 'Email for login')
  .option('-p, --password <password>', 'Password for login')
  .option('-t, --token <token>', 'Auth token (if already authenticated)')
  .option('-o, --organization <id>', 'Specific organization ID to use')
  .option('-f, --force', 'Skip confirmation prompts', false)
  .option('-v, --verbose', 'Enable verbose output', false);

program.parse(process.argv);

const options = program.opts();

/**
 * Create axios instance with auth token
 * @returns {Object} Axios instance
 */
function getApiClient() {
  return axios.create({
    baseURL: API_BASE_URL,
    headers: authToken ? { 
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    } : {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Login to the API
 * @param {string} email - User email
 * @param {string} password - User password 
 * @returns {Promise<string>} Auth token
 */
async function login(email, password) {
  try {
    console.log(`Logging in as ${email}...`);
    const response = await axios.post(`${API_BASE_URL}/auth/login`, { 
      email, 
      password 
    });
    
    authToken = response.data.token;
    console.log('Login successful!');
    return authToken;
  } catch (error) {
    console.error('Login failed:', error.response?.data?.error || error.message);
    throw new Error('Authentication failed');
  }
}

/**
 * Fetch organizations from the API
 * @returns {Promise<Array>} Organizations
 */
async function fetchOrganizations() {
  try {
    console.log('Fetching organizations...');
    const client = getApiClient();
    const response = await client.get('/organizations');
    console.log("Organizations: ", response.data.organizations);
    console.log(`Found ${response.data.organizations.length} organizations`);
    return response.data.organizations;
  } catch (error) {
    console.error('Error fetching organizations:', error.response?.data?.error || error.message);
    throw error;
  }
}

/**
 * Fetch users from the API
 * @returns {Promise<Array>} Users
 */
async function fetchUsers() {
  try {
    console.log('Fetching users...');
    const client = getApiClient();
    const response = await client.get('/users');
    console.log(`Found ${response.data.length} users`);
    return response.data;
  } catch (error) {
    console.error('Error fetching users:', error.response?.data?.error || error.message);
    throw error;
  }
}

/**
 * Fetch panels for an organization from the API
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Array>} Panels
 */
async function fetchPanels(organizationId) {
  try {
    console.log(`Fetching panels for organization ${organizationId}...`);
    const client = getApiClient();
    const response = await client.get(`/panels?organization=${organizationId}`);
    console.log("Panels: ", response.data);
    console.log(`Found ${response.data.panels.length} panels`);
    return response.data.panels;
  } catch (error) {
    console.error('Error fetching panels:', error.response?.data?.error || error.message);
    throw error;
  }
}

/**
 * Create a project via the API
 * @param {Object} projectData - Project data
 * @returns {Promise<Object>} Created project
 */
async function createProject(projectData) {
  try {
    console.log(`Creating project "${projectData.name}"...`);
    const client = getApiClient();
    const response = await client.post('/projects', projectData);
    console.log(`Project "${projectData.name}" created with ID: ${response.data.project_id}`);
    return response.data;
  } catch (error) {
    console.error('Error creating project:', error.response?.data?.error || error.message);
    throw error;
  }
}

/**
 * Add collaborators to a project via the API
 * @param {string} projectId - Project ID
 * @param {Array} userIds - User IDs to add as collaborators
 * @returns {Promise<Array>} Results of collaborator addition
 */
async function addCollaborators(projectId, userIds) {
  try {
    console.log(`Adding ${userIds.length} collaborators to project ${projectId}...`);
    const client = getApiClient();
    const promises = userIds.map(userId => 
      client.post(`/projects/${projectId}/collaborators`, { user_id: userId })
    );
    
    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    console.log(`Added ${successful} collaborators to project ${projectId}`);
    return results;
  } catch (error) {
    console.error('Error adding collaborators:', error.response?.data?.error || error.message);
    throw error;
  }
}

/**
 * Select a random subset of items
 * @param {Array} items - Items to select from 
 * @param {number} count - Number of items to select
 * @returns {Array} Selected items
 */
function selectRandomItems(items, count) {
  return faker.helpers.arrayElements(items, Math.min(count, items.length));
}

async function selectOrganization(organizations) {
  if (organizations.length === 0) {
    console.error('No organizations found!');
    process.exit(1);
  }
  
  const choices = organizations.map(org => ({
    name: `${org.name} (${org.organization_id || org.id})`,
    value: org
  }));
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { selectedOrg } = await inquirer.prompt([
    {
      type: 'list',
      name: 'selectedOrg',
      message: 'Select an organization:',
      choices
    }
  ]);
  
  return selectedOrg;
}

async function confirmGeneration(count) {
  if (options.force) return true;
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { confirm } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirm',
      message: `You are about to generate ${count} projects via the API. Continue?`,
      default: false
    }
  ]);
  
  return confirm;
}

/**
 * Generate a project with random data
 * @param {Object} params - Generation parameters 
 * @returns {Object} Project data
 */
function generateProject(params) {
  const { organization, panels } = params;
  
  // Select random panels for the project
  const assignedPanels = selectRandomItems(
    panels.map(panel => panel.panel_id), 
    faker.number.int({ min: 1, max: 3 })
  );
  
  return {
    name: faker.company.catchPhrase(),
    description: faker.lorem.paragraph(),
    organization_id: organization.organization_id || organization.id,
    status: faker.helpers.arrayElement(['draft', 'ongoing', 'completed']),
    assigned_panels: assignedPanels,
    is_development: true  // Mark as test data
  };
}

/**
 * Generate projects for an organization
 * @param {Object} organization - Organization to create projects for
 * @param {number} count - Number of projects to create
 * @returns {Promise<Array>} Created projects
 */
async function generateProjectsForOrganization(organization, count) {
  try {
    // Fetch panels for the organization
    const panels = await fetchPanels(organization.organization_id || organization.id);
    if (panels.length === 0) {
      console.warn(`No panels found for ${organization.name}, skipping...`);
      return [];
    }
    
    // Fetch users for potential collaborators
    const users = await fetchUsers();
    
    const createdProjects = [];
    
    for (let i = 0; i < count; i++) {
      // Generate project data
      const projectData = generateProject({
        organization,
        panels
      });
      
      try {
        // Create the project
        const project = await createProject(projectData);
        
        // Add random collaborators (up to 3)
        const potentialCollaborators = users
          .filter(user => user.uid !== project.owner)  // Exclude the owner
          .map(user => user.uid);
          
        if (potentialCollaborators.length > 0) {
          const collaborators = selectRandomItems(potentialCollaborators, 3);
          if (collaborators.length > 0) {
            await addCollaborators(project.project_id, collaborators);
          }
        }
        
        createdProjects.push(project);
      } catch (error) {
        console.error(`Failed to create project ${i + 1}:`, error.message);
      }
    }
    
    return createdProjects;
  } catch (error) {
    console.error(`Error generating projects for ${organization.name}:`, error.message);
    return [];
  }
}

/**
 * Get credentials from user input
 * @returns {Promise<Object>} Credentials
 */
async function promptForCredentials() {
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const questions = [];
  
  if (!options.email) {
    questions.push({
      type: 'input',
      name: 'email',
      message: 'Enter your email:',
      validate: (input) => input.includes('@') ? true : 'Please enter a valid email'
    });
  }
  
  if (!options.password) {
    questions.push({
      type: 'password',
      name: 'password',
      message: 'Enter your password:',
      mask: '*'
    });
  }
  
  if (questions.length === 0) {
    return {
      email: options.email,
      password: options.password
    };
  }
  
  const answers = await inquirer.prompt(questions);
  
  return {
    email: options.email || answers.email,
    password: options.password || answers.password
  };
}

async function main() {
  console.log('API Project Generator');
  console.log('====================');
  
  try {
    // Make sure inquirer is loaded
    if (!inquirer) {
      inquirer = (await import('inquirer')).default;
    }
    
    const count = parseInt(options.count, 10);
    if (isNaN(count) || count <= 0) {
      console.error('Count must be a positive number');
      return;
    }
    
    // Authenticate if token not provided
    if (!options.token) {
      const credentials = await promptForCredentials();
      authToken = await login(credentials.email, credentials.password);
    } else {
      authToken = options.token;
      console.log('Using provided auth token');
    }
    
    // Fetch organizations
    const organizations = await fetchOrganizations();
    if (organizations.length === 0) {
      console.error('No organizations found. Please create an organization first.');
      return;
    }
    
    // Determine organization to use
    let organization;
    if (options.organization) {
      organization = organizations.find(org => 
        (org.organization_id || org.id) === options.organization
      );
      
      if (!organization) {
        console.error(`Organization with ID ${options.organization} not found`);
        return;
      }
    } else {
      // Interactive selection
      organization = await selectOrganization(organizations);
    }
    
    // Confirm generation
    const confirmResult = await confirmGeneration(count);
    if (!confirmResult) {
      console.log('Operation cancelled.');
      return;
    }
    
    // Generate projects
    console.log(`Generating ${count} projects for ${organization.name} via API...`);
    const projects = await generateProjectsForOrganization(organization, count);
    
    console.log(`\nSuccessfully created ${projects.length} projects:`);
    console.table(projects.map(p => ({
      id: p.project_id,
      name: p.name,
      status: p.status,
      panels: p.assigned_panels.length
    })));
    
  } catch (error) {
    console.error('Error:', error.message);
    if (options.verbose) {
      console.error(error);
    }
  }
}

// Wrap everything in an async IIFE to ensure inquirer is loaded
(async () => {
  try {
    // Load inquirer
    inquirer = (await import('inquirer')).default;
    
    // Run the main function
    await main();
  } catch (error) {
    console.error('Error:', error.message);
    if (options.verbose) {
      console.error(error);
    }
    process.exit(1);
  }
})(); 