{"name": "src", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "generate:projects": "node scripts/populateProjects.js", "generate:sample-data": "node scripts/generateSampleData.js", "generate:api-projects": "node scripts/apiProjectGenerator.js", "cleanup:dev-data": "node scripts/generateSampleData.js --cleanup --force"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.6.8", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "express-basic-auth": "^1.2.1", "firebase-admin": "^12.6.0", "ioredis": "^5.4.1", "joi": "^17.13.3", "js-yaml": "^4.1.0", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@faker-js/faker": "^9.1.0", "cli-table3": "^0.6.5", "commander": "^13.1.0", "csv-parse": "^5.5.6", "inquirer": "^12.4.2", "jest": "^29.7.0", "nodemon": "^3.1.7", "supertest": "^6.3.4", "yargs": "^17.7.2"}}