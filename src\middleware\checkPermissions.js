const { redisCache } = require('../config');

const checkPermissions = (allowedRoles) => {
    return async (req, res, next) => {
      try {
        // Assuming req.user is set by authenticateUser middleware
        const userId = req.user.uid;
        console.log("Checking permissions for user: ", userId);
        const user = req.userData || await redisCache.getUser(userId); // Reuse the user data if it's already set
        if(!req.userData){
            req.userData = user;
        }
        //console.log("User: ", user);
        // Check if user has any of the allowed roles
        if (!user.roles || !allowedRoles.some(role => user.roles.includes(role))) {
          console.log("User does not have permissions", user.roles, allowedRoles);
          return res.status(403).send('Insufficient permissions');
        }
        console.log("User has permissions");
        next();
      } catch (error) {
        res.status(500).send('Error checking permissions');
      }
    };
  };
  
  module.exports = checkPermissions;