# Development Scripts

This directory contains development utility scripts for the Panel Registration System.

## Data Generation Scripts

### Project Generator

Generates projects with realistic relationships to organizations, users, and panels.

```bash
# Run with npm
npm run generate:projects

# Run directly with options
node scripts/populateProjects.js --count 10 --organization org123 --user user456

# Options
# -c, --count <number>           Number of projects to generate (default: 5)
# -o, --organization <id>        Specific organization ID to use
# -u, --user <id>                Specific user ID to use as owner
# -f, --force                    Skip confirmation prompts
# -v, --verbose                  Enable verbose output
```

### Complete Sample Data Generator

Generates a complete sample dataset including organizations, users, products, panels, and projects. All generated data is marked with `is_development: true` for easy cleanup.

```bash
# Run with npm
npm run generate:sample-data

# Run directly with options
node scripts/generateSampleData.js --org-count 3 --users-per-org 7 --cleanup

# Options
# -o, --org-count <number>        Number of organizations to generate (default: 2)
# -u, --users-per-org <number>    Number of users per organization (default: 5)
# -p, --products-per-org <number> Number of products per organization (default: 3)
# -n, --panels-per-org <number>   Number of panels per organization (default: 4)
# -j, --projects-per-org <number> Number of projects per organization (default: 5)
# -c, --cleanup                   Clean up existing development data before generating new data
# -f, --force                     Skip confirmation prompts
# -v, --verbose                   Enable verbose output
```

### Cleanup Development Data

Removes all development data (marked with `is_development: true`) from the database.

```bash
# Run with npm
npm run cleanup:dev-data

# Run directly
node scripts/generateSampleData.js --cleanup --force
```

## Usage Tips

1. For local development, generate a small sample dataset first to ensure everything works
2. Always inspect the generated data to verify relationships
3. Use the cleanup script when you no longer need the development data
4. The `--force` flag skips confirmation prompts (useful for CI/CD)
5. Use the `--verbose` flag to see detailed output

## Safety Precautions

The generators include safety features to prevent accidental data corruption:

1. All generated data is marked with `is_development: true`
2. The cleanup script only deletes items marked as development data
3. Multiple confirmation prompts are shown before critical operations
4. Generated data uses UUIDs to avoid collisions with real data

## Example Workflows

### Create a development environment from scratch

```bash
# Clean up any existing development data
npm run cleanup:dev-data

# Generate complete sample dataset
npm run generate:sample-data --org-count 2 --users-per-org 5 --projects-per-org 10
```

### Add more projects to existing organizations

```bash
# Add 10 more projects using the interactive interface
npm run generate:projects -- --count 10
```

### Generate projects for a specific organization

```bash
# First find the organization ID you want to use
npm run generate:projects -- --verbose

# Then generate projects for that specific organization
npm run generate:projects -- --count 5 --organization org123 --user user456
``` 