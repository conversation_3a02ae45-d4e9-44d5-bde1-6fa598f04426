const Joi = require('joi');

// Define flavor schema using <PERSON><PERSON> for robust validation
const flavorSchema = Joi.object({
  name: Joi.string()
    .required()
    .description('Name of the flavor option'),
    
  type: Joi.string()
    .valid('sweet', 'prototypical', 'savory')
    .required()
    .description('Type of the flavor option'),

  priority: Joi.number()
    .integer()
    .required()
    .description('Priority ranking of the flavor'),

  option_id: Joi.string()
    .required()
    .description('Unique identifier for the flavor option'),

  color: Joi.string()
    .required()
    .description('Color of the flavor option')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake flavor document for testing
const generateFakeFlavor = (overrides = {}) => {
  const fakeFlavor = {
    name: faker.lorem.word(),
    type: faker.helpers.arrayElement(['sweet', 'prototypical', 'savory']),
    priority: faker.number.int({min: 1, max: 100}),
    option_id: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateFlavor(fakeFlavor);
  if (error) {
    throw new Error(`Generated invalid flavor: ${error.message}`);
  }

  return fakeFlavor;
};

// Generate multiple fake flavors
const generateFakeFlavors = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeFlavor(overrides));
};

// Validate flavor document against schema
const validateFlavor = (flavor) => {
  return flavorSchema.validate(flavor, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Convert Firestore document to flavor model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateFlavor(data);
  
  if (error) {
    throw new Error(`Invalid flavor data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert flavor model to Firestore document
const toFirestore = (flavor) => {
  const { value, error } = validateFlavor(flavor);
  
  if (error) {
    throw new Error(`Invalid flavor data for Firestore: ${error.message}`);
  }
  
  return value;
};

module.exports = {
  flavorSchema,
  generateFakeFlavor,
  generateFakeFlavors,
  validateFlavor,
  fromFirestore,
  toFirestore
};
