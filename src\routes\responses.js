const express = require('express');
const router = express.Router();

const { firestoreModule, redisCache } = require('../config');
const { authenticateUser, getProgress, canAccessPanel, checkPermissions } = require('../middleware');
const { validateResponseKeys } = require('../util');
const { validateResponse } = require('../models/response');
const { ResponseService } = require('../services');

router.all('*', authenticateUser);

/**
 * @swagger
 * /responses:
 *   get:
 *     summary: Get user responses
 *     description: Retrieves all responses submitted by the current user
 *     tags: [Responses]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Responses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Response'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have survey permission
 *       500:
 *         description: Server error
 */
router.get('/', checkPermissions(['survey']), async (req, res) => {
    try {
        const userId = req.user.uid;
        //const responses = await firestoreModule.getUserResponses(userId);
        // Get responses with hydrated user data
        const responses = await ResponseService.getResponsesByUserWithReferences(userId, {
            includeFullDetails: true
        });
        res.status(200).send(responses);
    } catch (error) {
        res.status(500).send('Error fetching responses: ' + error.message);
    }
});

/**
 * @swagger
 * /responses:
 *   post:
 *     summary: Submit a response
 *     description: Submits a response to a question in a panel
 *     tags: [Responses]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - panel_id
 *               - question_id
 *               - data
 *             properties:
 *               panel_id:
 *                 type: string
 *                 description: ID of the panel
 *               question_id:
 *                 type: string
 *                 description: ID of the question being answered
 *               data:
 *                 type: object
 *                 description: Response data specific to the question type
 *     responses:
 *       201:
 *         description: Response submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Updated panel progress
 *       400:
 *         description: Bad request - panel already completed, panel not active, or invalid response data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this panel or does not have survey permission
 *       500:
 *         description: Server error
 */
router.post('/', canAccessPanel, getProgress, checkPermissions(['survey']), async (req, res) => {
    try {
        const userId = req.user.uid;
        const response = req.body;

        if (response.panel_type==="Triangle Test"){
            console.log(response);
            res.status(201).send('Triangle Test Response submitted successfully');
        }

        if(req.progress.completed){
            return res.status(400).send('Panel already completed');
        }
        if(!req.panel.active){
            return res.status(400).send('Panel is not active');
        }
        console.log(req.panel);
        if(req.panel.product_id){
            response.product_id = req.panel.product_id;
        } else{
            //placeholder
            response.product_id="multiple_products";
        }
        response.organization_id = req.panel.organization_id;
        response.user_id = userId;
        const { error } = validateResponse(response);
        if (error) {
            return res.status(400).send(`Invalid response data: ${error.message}`);
        }
        console.log("Response is valid!");
        const { data, ...rest } = response;
        validateResponseKeys(data, response.question_id);
        console.log("Keys are valid!");
        console.log({ ...rest, ...data, product_id: req.panel.product_id, organization_id: req.panel.organization_id });
        let response_payload = { ...rest, ...data, product_id: req.panel.product_id, organization_id: req.panel.organization_id, user_id: userId };
        let response_id = null;
        console.log("Updating or creating response: ", req.progress);
        if (req.progress.response_ids[response.question_id]) {
            response_id = req.progress.response_ids[response.question_id];
            await firestoreModule.updateDocument('responses', response_id, response_payload);
            // await ResponseService.updateResponse(response_id, response_payload);
        } else {
            response_id = await firestoreModule.createDocument('responses', response_payload);
            response_id = response_id.id;
            // const newResponse = await ResponseService.createResponse(response_payload);
            // response_id = newResponse.id;
        }
        console.log("Response ID: ", response_id, response_id);
        let payload = { responses: firestoreModule.FieldValue.arrayUnion(response.question_id) };
        payload[response.question_id] = data;
        let progress = { ...req.progress };
        payload.response_ids = { ...payload.response_ids, [response.question_id]: response_id };

        progress[response.question_id] = data;
        progress['responses'] = [...progress.responses, response.question_id];
        //add a response array to the progress document that is a set of the question_ids
        console.log("Progress Responses: ", progress.responses);
        progress.responses = [...new Set([...progress.responses, response.question_id])];
        //payload.responses = progress.responses;
        progress.response_ids = { ...progress.response_ids, [response.question_id]: response_id };
        console.log("Progress: ", progress);
        await firestoreModule.updateDocument(`users/${userId}/panels`, response.panel_id, payload);
        await redisCache.setAnyData(`progressPanels:${userId}:${response.panel_id}`, progress);
        console.log("Progress updated in redis");
        res.status(201).send(progress);
    } catch (error) {
        res.status(500).send('Error creating response: ' + error.message);
    }
});

/**
 * @swagger
 * /responses/{panel_id}:
 *   get:
 *     summary: Get responses for a panel
 *     description: Retrieves all responses submitted by the user for a specific panel
 *     tags: [Responses]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel
 *     responses:
 *       200:
 *         description: Responses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Response details
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have survey permission
 *       500:
 *         description: Server error
 */
router.get('/:panel_id', checkPermissions(['survey']), async (req, res) => {
    try {
        const userId = req.user.uid;
        ///*
        const responseId = req.params.response_id;
        const response = await firestoreModule.getResponseDetails(userId, responseId);
        res.status(200).send(response);
        /*/
        const panelId = req.params.panel_id;
        // Get response by panel ID with all references hydrated
        const response = await ResponseService.getResponseByPanelAndUser(panelId, userId);
        
        if (!response) {
            return res.status(404).send('Response not found');
        }
        
        // Hydrate the response with references
        const hydratedResponse = await ResponseService.getResponseWithReferences(response.id, {
            includeFullDetails: true
        });
        
        
        res.status(200).send(hydratedResponse);
        //*/
    } catch (error) {
        res.status(500).send('Error fetching response details: ' + error.message);
    }
});

/**
 * @swagger
 * /responses/panel/{panel_id}/all:
 *   get:
 *     summary: Get all responses for a panel
 *     description: Retrieves all responses submitted for a specific panel, with user data
 *     tags: [Responses]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel
 *     responses:
 *       200:
 *         description: Responses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *               description: Array of responses with user data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Server error
 */
router.get('/panel/:panel_id/all', checkPermissions(['insights']), /*canAccessPanel,*/ async (req, res) => {
    try {
        const panelId = req.params.panel_id;
        const includeFullDetails = req.query.includeFullDetails === 'true';
        // Get all responses for this panel with user data
        const responses = await ResponseService.getResponsesByPanelWithReferences(panelId, {
            includeFullDetails
        });
        
        res.status(200).send(responses);
    } catch (error) {
        res.status(500).send('Error fetching panel responses: ' + error.message);
    }
});

module.exports = router;