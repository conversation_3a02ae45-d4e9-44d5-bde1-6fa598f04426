const { validateQuestion, validateQuestionUpdate } = require('../models/question');
const BaseRepository = require('./base');

class QuestionRepository extends BaseRepository {
  constructor() {
    super();
  }

  /**
   * Create a new question in the database
   * @param {Object} questionData - The question data to create
   * @returns {Promise<string>} - The ID of the created question
   */
  async create(questionData) {
    const { error } = validateQuestion(questionData);
    if (error) {
      throw new Error(`Invalid question data: ${error.message}`);
    }

    // Create question in Firestore
    await this.firestoreModule.createDocument(
      "questions",
      questionData,
      questionData.question_id
    );

    // Cache the new question data
    const cacheKey = `question:${questionData.question_id}`;
    await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(questionData));

    return questionData.question_id;
  }

  /**
   * Get a question by ID
   * @param {string} questionId - The ID of the question to retrieve
   * @returns {Promise<Object>} - The question data
   */
  async read(questionId) {
    const cacheKey = `question:${questionId}`;
    let questionData = await this.redisCache.client.get(cacheKey);

    if (!questionData) {
      // Cache miss: Fetch from Firestore
      questionData = await this.firestoreModule.getDocument('questions', questionId);
      
      if (!questionData) {
        throw new Error('Question not found');
      }
      
      // Cache the result with a TTL of 1 hour
      await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(questionData));
    } else {
      questionData = JSON.parse(questionData);
    }
    
    return questionData;
  }

  /**
   * Update a question
   * @param {string} questionId - The ID of the question to update
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object>} - The updated question
   */
  async update(questionId, updateData) {
    // Validate update data against schema
    const { error } = validateQuestionUpdate(updateData);
    if (error) {
      throw new Error(`Invalid question update data: ${error.message}`);
    }

    // Update in Firestore
    await this.firestoreModule.updateDocument(
      "questions", 
      questionId, 
      updateData
    );

    // Invalidate cache
    const cacheKey = `question:${questionId}`;
    await this.redisCache.client.del(cacheKey);

    // Get updated document
    const updatedQuestion = await this.firestoreModule.getDocument("questions", questionId);
    
    // Update cache with new data
    await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(updatedQuestion));

    return updatedQuestion;
  }

  /**
   * Delete a question (soft delete)
   * @param {string} questionId - The ID of the question to delete
   */
  async delete(questionId) {
    await this.firestoreModule.markDeleted("questions", questionId);
    
    // Invalidate cache
    const cacheKey = `question:${questionId}`;
    await this.redisCache.client.del(cacheKey);
    
    // Invalidate organization questions cache
    const question = await this.firestoreModule.getDocument("questions", questionId);
    if (question && question.organization_id) {
      const orgQuestionsKey = `org:${question.organization_id}:questions`;
      await this.redisCache.client.del(orgQuestionsKey);
    }
  }

  /**
   * Get all questions for an organization
   * @param {string} organizationId - The organization ID
   * @returns {Promise<Array>} - Array of questions
   */
  async getQuestionsByOrganization(organizationId) {
    const cacheKey = `org:${organizationId}:questions`;
    let questions = await this.redisCache.client.get(cacheKey);

    if (!questions) {
      // Cache miss: Fetch from Firestore
      const conditions = [
        ["organization_id", "==", organizationId],
        ["deleted", "!=", true]
      ];
      
      questions = await this.firestoreModule.queryDocuments("questions", conditions);
      
      // Cache the results
      if (questions.length > 0) {
        await this.redisCache.client.setex(cacheKey, 3600, JSON.stringify(questions));
      } else {
        // Cache empty results for a shorter time
        await this.redisCache.client.setex(cacheKey, 300, JSON.stringify([]));
        questions = [];
      }
    } else {
      questions = JSON.parse(questions);
    }
    
    return questions;
  }
}

module.exports = QuestionRepository; 