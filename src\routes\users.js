const express= require('express');
const router = express.Router();

const { redisCache, firestoreModule } = require('../config');
const {authenticateUser, paginationMiddleware, checkPermissions} = require ('../middleware');
const findAllUserInOrg = require('../util/findUsersInOrg');
const { OrganizationService } = require('../services');

router.all('*', authenticateUser)

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get users with insights role
 *     description: Retrieves all users with insights role from organizations accessible to the current user
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: List of users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 *       500:
 *         description: Server error
 */
router.get('/', checkPermissions(['admin','survey']), async (req,res)=>{
    try{
        let userOrgs = await OrganizationService.getOrganizationsByUserId(req.user.uid);

        let users = await findAllUserInOrg(userOrgs)
        // console.log(users);

        if(users.length){
            // Check if role query parameter exists
            const roleParam = req.query.role;
            
            if (roleParam) {
                // Handle comma-separated roles
                const roles = roleParam.split(',').map(role => role.trim());
                
                // Filter users who have at least one of the specified roles
                users = users.filter(user => 
                    roles.some(role => user.roles && user.roles.includes(role))
                );
            } else {
                // Default behavior - filter for insights role
                users = users.filter(user => user.roles && user.roles.includes('insights'));
            }
        }

        let usersWithorgData = await Promise.all(users.map(async (user)=>{
            let orgData = await OrganizationService.getOrganizationById(user.organizations[0])
            return {...user, orgData}
        }))

      res.status(200).send(usersWithorgData);

    }catch(error){
        res.status(500).send('Error fetching users: '+ error.message)
    }
})

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: Get user statistics
 *     description: Retrieves detailed statistics for a specific user
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to retrieve statistics for
 *     responses:
 *       200:
 *         description: User statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', async (req,res)=>{
    try{
        const userId = req.params.id;
        let user = await firestoreModule.getUserStats(userId);
        res.status(200).send(user);

    }catch(error){
        res.status(500).send('Error fetching users: '+ error.message)
    }
})

module.exports = router;