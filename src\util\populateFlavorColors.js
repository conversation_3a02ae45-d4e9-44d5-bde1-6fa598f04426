const admin = require('firebase-admin');
const dotenv = require('dotenv');

dotenv.config();

// Initialize Firebase Admin
const serviceAccount = require(process.env.UTIL_FIREBASE_SERVICE_ACCOUNT_PATH);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: process.env.FIREBASE_DATABASE_URL
});

const db = admin.firestore();

const flavorColors = [{"id":"W1oNbxBeFpPg4SqaoqzZ","name":"sweet","color":"rgb(247, 0, 255)"},{"id":"SDvgMC7a4BjFAYMiF5Ny","name":"fruit","color":"rgb(215, 0, 255)"},{"id":"MRna8cSXF1FHwQHhIlYX","name":"candy","color":"rgb(183, 0, 254)"},{"id":"QQuwZQUFO06PtL6vr8v5","name":"lychee","color":"rgb(151, 0, 254)"},{"id":"GQu82LKwIoTCFA7KgZte","name":"tropical","color":"rgb(119, 0, 254)"},{"id":"73l5ZmYKIaedy4T8JQhy","name":"tangerine","color":"rgb(88, 0, 253)"},{"id":"bwlMCAoSHwlXPBWTDyWs","name":"grapefruit","color":"rgb(57, 0, 253)"},{"id":"Tp3nkJ51190jdFCoaOSN","name":"coconut","color":"rgb(25, 0, 253)"},{"id":"yBF9FA7rcoTrGeWxuIu5","name":"pineapple","color":"rgb(0, 6, 252)"},{"id":"2CvQ806p1VZe67yywzeP","name":"papaya","color":"rgb(0, 37, 252)"},{"id":"5PxQtaeHeTeaxwxGZPhH","name":"guava","color":"rgb(0, 68, 252)"},{"id":"9tYDvRk0qpYARP0jUrH2","name":"banana","color":"rgb(0, 99, 251)"},{"id":"hBDvr45a99dCUr1eOLk3","name":"mango","color":"rgb(0, 130, 251)"},{"id":"58EJgfXsflgYr1JFLrcQ","name":"apricot","color":"rgb(0, 161, 251)"},{"id":"xQSQpcdN2b3XU2rwMGTz","name":"peach","color":"rgb(0, 192, 250)"},{"id":"c1S3sJgUUDd6uFjlCEFl","name":"apple","color":"rgb(0, 225, 251)"},{"id":"K9vtTy6D1IUyBLYIpMkp","name":"cherry","color":"rgb(1, 255, 242)"},{"id":"tbB9DKqOxok8cEjX14Gz","name":"strawberry","color":"rgb(5, 254, 204)"},{"id":"W2BkQCch4rqdSrQBB7ex","name":"red berry","color":"rgb(10, 254, 167)"},{"id":"1ir8dyPdhfebq5cK3UjO","name":"dark berry","color":"rgb(15, 254, 132)"},{"id":"qKpXDfhMUzcMvqhuuA3H","name":"blueberry","color":"rgb(19, 254, 98)"},{"id":"ePd5oDB9BUSPn7hOozXj","name":"berry","color":"rgb(24, 254, 66)"},{"id":"hnZMxbyYGg7RKoKzWZGi","name":"grape","color":"rgb(29, 253, 36)"},{"id":"U2QzJUWwMMes6RukBZp9","name":"melon","color":"rgb(60, 253, 33)"},{"id":"9HnIvVZ34aOgjTdCI1Qh","name":"butterscotch","color":"rgb(97, 253, 38)"},{"id":"ZyQuzUww9ACyc0Ovksju","name":"bready","color":"rgb(133, 253, 43)"},{"id":"eVvmpZV7BsdWqVHj9Zt4","name":"cereal","color":"rgb(167, 253, 47)"},{"id":"stADbburIM0MBeNk8bRK","name":"cream","color":"rgb(199, 252, 52)"},{"id":"xkAH2UvzN2Tty6mj3Wtz","name":"tart","color":"rgb(230, 252, 57)"},{"id":"LNAOYmw897AKE1xGd9Qe","name":"chocolate","color":"rgb(252, 244, 61)"},{"id":"PaIfnoNjrSrwV5BsdWcO","name":"dairy","color":"rgb(252, 226, 64)"},{"id":"9dttFjWsU8vGPR0exoZJ","name":"honey","color":"rgb(251, 224, 64)"},{"id":"ZGpHOUOpaMhEwwwGl1an","name":"vanilla","color":"rgb(250, 221, 64)"},{"id":"qFBBdrvm6BPWhOXaNvpU","name":"ripe","color":"rgb(249, 219, 64)"},{"id":"HWTtvcw5Kyw32JO5gQXL","name":"citrus","color":"rgb(248, 216, 63)"},{"id":"9UgDJZHcnIQSUs6enSvk","name":"lemon","color":"rgb(247, 214, 63)"},{"id":"wRvwcmTDE2eq8yDfy9mE","name":"lime","color":"rgb(246, 211, 63)"},{"id":"JcgXuD73syirIJ7dIQTJ","name":"sour","color":"rgb(245, 209, 63)"},{"id":"Kh3DyFeEtou76IfOghu9","name":"floral","color":"rgb(244, 207, 63)"},{"id":"SIX5pT2zYMw1RSSk5CVV","name":"diesel","color":"rgb(244, 204, 63)"},{"id":"T1iCnYnfW1AljaYqq06l","name":"gas","color":"rgb(243, 202, 63)"},{"id":"B5MqQ8mSxw4tSHNtWIUE","name":"earth","color":"rgb(242, 199, 63)"},{"id":"2mI9YSnQobWmHGB1BELf","name":"musky","color":"rgb(241, 197, 63)"},{"id":"8xz0DTcFN5IdD5iaSLCg","name":"wood","color":"rgb(240, 195, 63)"},{"id":"QS2JU8cPrEco2Eze7gHj","name":"pine","color":"rgb(239, 192, 63)"},{"id":"iuwHxzmnI3NkPMgHeeA8","name":"camphoric","color":"rgb(236, 189, 64)"},{"id":"KYMlz1KsKOCeKm9oeW9M","name":"mentholic","color":"rgb(233, 186, 67)"},{"id":"7oSY5OZYciGqJjvi8u7E","name":"terpenic","color":"rgb(229, 183, 70)"},{"id":"h0eUKjA0ceYo9IsXuLrF","name":"grass","color":"rgb(226, 179, 73)"},{"id":"j2FKrGb6sIQ1lLd4J6rs","name":"green","color":"rgb(222, 176, 76)"},{"id":"nMIcZFXiU9dUUoHqwgxG","name":"hay","color":"rgb(219, 173, 78)"},{"id":"p2rDWB2b3kOhKsflITNG","name":"tea","color":"rgb(215, 170, 81)"},{"id":"Sl3oMrHSNufu68AZuHwW","name":"herbal","color":"rgb(211, 168, 84)"},{"id":"9vcBuQkSjGi1ZgV4YkrB","name":"plant","color":"rgb(208, 165, 87)"},{"id":"kpxnRmu9UJ6XAFP9oLf6","name":"vegetables","color":"rgb(204, 163, 90)"},{"id":"P68kKl2pDfsOAQofT5XO","name":"pepper","color":"rgb(200, 160, 93)"},{"id":"hoYxwsq8i5MzlaSvUaL9","name":"mint","color":"rgb(197, 158, 96)"},{"id":"wDj4th5xI8bYwC9I7EZO","name":"rosemary","color":"rgb(193, 156, 99)"},{"id":"WjZR5upPzSENTIsiCndP","name":"spicy","color":"rgb(189, 154, 102)"},{"id":"PQu0HDpz7HAyy9sK7jmF","name":"bitter","color":"rgb(186, 152, 105)"},{"id":"4TqgdZNJYDYffjQLOV4A","name":"fatty","color":"rgb(183, 154, 106)"},{"id":"3Mz2Gd73s5GYDZjsnb01","name":"sharp","color":"rgb(180, 157, 107)"},{"id":"Z7acLOEylsDpg03OzQwn","name":"waxy","color":"rgb(180, 157, 107)"},{"id":"vE0d4RzKA8IJ1WRjYHMH","name":"sharpie","color":"rgb(180, 157, 107)"},{"id":"bmUPlGFRvXL4smjyc8YB","name":"skunk","color":"rgb(172, 162, 109)"},{"id":"1Dxrb4LEkmLqqXB98LWz","name":"sulfuric","color":"rgb(169, 163, 110)"},{"id":"DYJrM6qmVSJsKUBGI67V","name":"cheese","color":"rgb(166, 163, 110)"},{"id":"fQkIBcsI68rdCEBQ5pCH","name":"blue cheese","color":"rgb(164, 163, 111)"},{"id":"92FiHuohI0AXImhMTRjT","name":"astringent","color":"rgb(158, 161, 112)"},{"id":"KCTgVlW5K9IhTG6SjKHf","name":"chemical","color":"rgb(153, 158, 113)"},{"id":"wcFbxmNbC7xnPV7nlf1M","name":"ammonia","color":"rgb(148, 155, 114)"},{"id":"niKH1nA8MXfWNoFPL2eJ","name":"moth balls","color":"rgb(144, 152, 115)"},{"id":"CZOdm3oz52C1rccXlJ7i","name":"allacieous","color":"rgb(140, 149, 116)"},{"id":"uba45odMzlowUjbnq2NH","name":"garlic","color":"rgb(137, 146, 117)"},{"id":"LZ7AxNH66sGdlEnO7tca","name":"savory","color":"rgb(133, 143, 118)"}];

const updateFlavorColors = async () => {
  try {
    // Create batch for efficient writes
    const batch = db.batch();

    // Update each flavor document
    for (const flavor of flavorColors) {
      const flavorRef = db.collection('flavorOptions').doc(flavor.id);
      batch.update(flavorRef, {
        color: flavor.color
      });
    }

    // Commit the batch
    await batch.commit();
    console.log('Successfully updated all flavor documents with colors');

  } catch (error) {
    console.error('Error updating flavor documents:', error);
    throw error;
  }
};

// Execute the update
updateFlavorColors();
