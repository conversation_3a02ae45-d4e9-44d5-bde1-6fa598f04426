const { faker } = require('@faker-js/faker');
const { firestoreModule } = require('../../config/db');
const ProjectGenerator = require('./projectGenerator');

/**
 * Sample Data Generator - Creates a complete sample dataset for development
 * including organizations, users, panels, and projects.
 */
class SampleDataGenerator {
  constructor() {
    this.projectGenerator = new ProjectGenerator();
  }

  /**
   * Generate a sample organization
   * @returns {Object} Organization data
   */
  generateOrganization() {
    return {
      organization_id: faker.string.uuid(),
      name: faker.company.name(),
      description: faker.company.catchPhrase(),
      type: faker.helpers.arrayElement(['research', 'brand', 'agency']),
      industry: faker.company.buzzPhrase(),
      is_development: true // Mark as development data
    };
  }

  /**
   * Generate a sample user
   * @param {Array} organizationIds - Organization IDs to assign to the user
   * @returns {Object} User data
   */
  generateUser(organizationIds = []) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const email = faker.internet.email({ firstName, lastName }).toLowerCase();
    
    return {
      uid: faker.string.uuid(),
      email,
      first_name: firstName,
      last_name: lastName,
      phone: faker.phone.number(),
      role: faker.helpers.arrayElement(['admin', 'user', 'guest']),
      organizations: organizationIds,
      is_active: true,
      is_development: true // Mark as development data
    };
  }

  /**
   * Generate a sample panel
   * @param {string} organizationId - Organization ID to assign to the panel
   * @param {Array} products - Products to choose from for the panel
   * @returns {Object} Panel data
   */
  generatePanel(organizationId, products = []) {
    const productId = products.length > 0 
      ? faker.helpers.arrayElement(products).product_id 
      : faker.string.uuid();
    
    const targetResponses = faker.number.int({ min: 50, max: 500 });
    
    return {
      panel_id: faker.string.uuid(),
      name: `${faker.company.buzzAdjective()} ${faker.commerce.productName()} Panel`,
      description: faker.lorem.paragraph(),
      organization_id: organizationId,
      product_id: productId,
      target_responses: targetResponses,
      active: faker.datatype.boolean(),
      is_development: true // Mark as development data
    };
  }

  /**
   * Generate a sample product
   * @param {string} organizationId - Organization ID to assign to the product
   * @returns {Object} Product data
   */
  generateProduct(organizationId) {
    return {
      product_id: faker.string.uuid(),
      name: faker.commerce.productName(),
      description: faker.commerce.productDescription(),
      organization_id: organizationId,
      category: faker.commerce.department(),
      is_development: true // Mark as development data
    };
  }

  /**
   * Create an organization in Firestore
   * @param {Object} organizationData - Organization data
   * @returns {Promise<Object>} Created organization
   */
  async createOrganization(organizationData) {
    try {
      console.log(`Creating organization ${organizationData.name}...`);
      const orgRef = firestoreModule.collection('organizations').doc(organizationData.organization_id);
      await firestoreModule.set(orgRef, organizationData);
      console.log(`Organization ${organizationData.name} created with ID: ${organizationData.organization_id}`);
      return organizationData;
    } catch (error) {
      console.error(`Error creating organization ${organizationData.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Create a user in Firestore
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    try {
      console.log(`Creating user ${userData.first_name} ${userData.last_name}...`);
      const userRef = firestoreModule.collection('users').doc(userData.uid);
      await firestoreModule.set(userRef, userData);
      console.log(`User ${userData.first_name} ${userData.last_name} created with ID: ${userData.uid}`);
      return userData;
    } catch (error) {
      console.error(`Error creating user ${userData.first_name} ${userData.last_name}:`, error.message);
      throw error;
    }
  }

  /**
   * Create a panel in Firestore
   * @param {Object} panelData - Panel data
   * @returns {Promise<Object>} Created panel
   */
  async createPanel(panelData) {
    try {
      console.log(`Creating panel ${panelData.name}...`);
      const panelRef = firestoreModule.collection('panels').doc(panelData.panel_id);
      await firestoreModule.set(panelRef, panelData);
      console.log(`Panel ${panelData.name} created with ID: ${panelData.panel_id}`);
      return panelData;
    } catch (error) {
      console.error(`Error creating panel ${panelData.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Create a product in Firestore
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Created product
   */
  async createProduct(productData) {
    try {
      console.log(`Creating product ${productData.name}...`);
      const productRef = firestoreModule.collection('products').doc(productData.product_id);
      await firestoreModule.set(productRef, productData);
      console.log(`Product ${productData.name} created with ID: ${productData.product_id}`);
      return productData;
    } catch (error) {
      console.error(`Error creating product ${productData.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Generate a complete sample dataset
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Created entities
   */
  async generateSampleData(options = {}) {
    const {
      organizationCount = 2,
      usersPerOrganization = 5,
      productsPerOrganization = 3,
      panelsPerOrganization = 4,
      projectsPerOrganization = 5
    } = options;

    const result = {
      organizations: [],
      users: [],
      products: [],
      panels: [],
      projects: []
    };

    try {
      // Generate organizations
      for (let i = 0; i < organizationCount; i++) {
        const organization = this.generateOrganization();
        const createdOrg = await this.createOrganization(organization);
        result.organizations.push(createdOrg);

        // Generate users for this organization
        const orgUsers = [];
        for (let j = 0; j < usersPerOrganization; j++) {
          const user = this.generateUser([createdOrg.organization_id]);
          const createdUser = await this.createUser(user);
          result.users.push(createdUser);
          orgUsers.push(createdUser);
        }

        // Generate products for this organization
        const orgProducts = [];
        for (let j = 0; j < productsPerOrganization; j++) {
          const product = this.generateProduct(createdOrg.organization_id);
          const createdProduct = await this.createProduct(product);
          result.products.push(createdProduct);
          orgProducts.push(createdProduct);
        }

        // Generate panels for this organization
        const orgPanels = [];
        for (let j = 0; j < panelsPerOrganization; j++) {
          const panel = this.generatePanel(createdOrg.organization_id, orgProducts);
          const createdPanel = await this.createPanel(panel);
          result.panels.push(createdPanel);
          orgPanels.push(createdPanel);
        }

        // Only generate projects if we have users and panels
        if (orgUsers.length > 0 && orgPanels.length > 0) {
          // Generate projects for this organization
          for (let j = 0; j < projectsPerOrganization; j++) {
            const owner = faker.helpers.arrayElement(orgUsers);
            const collaborators = this.projectGenerator.selectCollaborators(orgUsers, owner.uid);
            
            const status = this.projectGenerator.generateStatus();
            const projectData = this.projectGenerator.generateProject({
              organization: createdOrg,
              owner,
              panels: orgPanels,
              collaborators,
              status
            });
            
            // Add development flag
            projectData.is_development = true;
            
            try {
              const createdProject = await this.projectGenerator.createProject(projectData);
              result.projects.push(createdProject);
            } catch (error) {
              console.error(`Failed to create project ${j + 1} for org ${createdOrg.name}:`, error.message);
            }
          }
        }
      }

      return result;
    } catch (error) {
      console.error('Error generating sample data:', error);
      throw error;
    }
  }

  /**
   * Delete all development data from Firestore
   * @returns {Promise<Object>} Deletion results
   */
  async cleanupDevelopmentData() {
    const collections = ['organizations', 'users', 'products', 'panels', 'projects'];
    const result = {};

    for (const collection of collections) {
      try {
        console.log(`Fetching development data from ${collection}...`);
        const query = await firestoreModule.collection(collection)
          .where('is_development', '==', true)
          .get();
        
        const count = query.size;
        result[collection] = count;
        
        if (count > 0) {
          console.log(`Deleting ${count} development documents from ${collection}...`);
          
          // Firestore has a limit of 500 operations per batch
          // Split into multiple batches if necessary
          const batchSize = 450;
          const batches = [];
          
          let currentBatch = firestoreModule.batch();
          let operationCount = 0;
          
          query.forEach((doc) => {
            currentBatch.delete(doc.ref);
            operationCount++;
            
            if (operationCount >= batchSize) {
              batches.push(currentBatch);
              currentBatch = firestoreModule.batch();
              operationCount = 0;
            }
          });
          
          // Add the last batch if it has operations
          if (operationCount > 0) {
            batches.push(currentBatch);
          }
          
          // Commit all batches
          const batchPromises = batches.map(batch => batch.commit());
          await Promise.all(batchPromises);
          
          console.log(`Successfully deleted ${count} documents from ${collection}.`);
        } else {
          console.log(`No development data found in ${collection}.`);
        }
      } catch (error) {
        console.error(`Error cleaning up ${collection}:`, error.message);
        result[`${collection}_error`] = error.message;
      }
    }
    
    return result;
  }
}

module.exports = SampleDataGenerator; 