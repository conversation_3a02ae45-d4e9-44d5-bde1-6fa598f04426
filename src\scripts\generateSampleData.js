#!/usr/bin/env node

require('dotenv').config();
const { Command } = require('commander');
const SampleDataGenerator = require('./dataGenerators/sampleDataGenerator');

// Dynamic import for inquirer (ESM module)
let inquirer;

const program = new Command();

program
  .name('generate-sample-data')
  .description('Generate complete sample data for development')
  .version('1.0.0');

program
  .option('-o, --org-count <number>', 'Number of organizations to generate', '2')
  .option('-u, --users-per-org <number>', 'Number of users per organization', '5')
  .option('-p, --products-per-org <number>', 'Number of products per organization', '3')
  .option('-n, --panels-per-org <number>', 'Number of panels per organization', '4')
  .option('-j, --projects-per-org <number>', 'Number of projects per organization', '5')
  .option('-c, --cleanup', 'Clean up existing development data before generating new data', false)
  .option('-f, --force', 'Skip confirmation prompts', false)
  .option('-v, --verbose', 'Enable verbose output', false);

program.parse(process.argv);

const options = program.opts();

async function confirmGeneration(settings) {
  if (options.force) return true;
  
  console.log('You are about to generate:');
  console.log(`- ${settings.organizationCount} organizations`);
  console.log(`- ${settings.organizationCount * settings.usersPerOrganization} users (${settings.usersPerOrganization} per organization)`);
  console.log(`- ${settings.organizationCount * settings.productsPerOrganization} products (${settings.productsPerOrganization} per organization)`);
  console.log(`- ${settings.organizationCount * settings.panelsPerOrganization} panels (${settings.panelsPerOrganization} per organization)`);
  console.log(`- ${settings.organizationCount * settings.projectsPerOrganization} projects (${settings.projectsPerOrganization} per organization)`);
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { confirm } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirm',
      message: 'Do you want to continue?',
      default: false
    }
  ]);
  
  return confirm;
}

async function confirmCleanup() {
  if (options.force) return true;
  
  // Make sure inquirer is loaded
  if (!inquirer) {
    inquirer = (await import('inquirer')).default;
  }
  
  const { confirm } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirm',
      message: 'This will DELETE ALL development data. Are you sure?',
      default: false
    }
  ]);
  
  return confirm;
}

async function main() {
  console.log('Sample Data Generator');
  console.log('====================');
  
  try {
    // Make sure inquirer is loaded
    if (!inquirer) {
      inquirer = (await import('inquirer')).default;
    }
    
    const generator = new SampleDataGenerator();
    
    // Parse numeric options
    const settings = {
      organizationCount: parseInt(options.orgCount, 10),
      usersPerOrganization: parseInt(options.usersPerOrg, 10),
      productsPerOrganization: parseInt(options.productsPerOrg, 10),
      panelsPerOrganization: parseInt(options.panelsPerOrg, 10),
      projectsPerOrganization: parseInt(options.projectsPerOrg, 10)
    };
    
    // Validate settings
    for (const [key, value] of Object.entries(settings)) {
      if (isNaN(value) || value < 0) {
        console.error(`Invalid value for ${key}: ${value}. Must be a non-negative number.`);
        return;
      }
    }
    
    // Clean up existing data if requested
    if (options.cleanup) {
      const shouldCleanup = await confirmCleanup();
      if (shouldCleanup) {
        console.log('Cleaning up existing development data...');
        const cleanupResults = await generator.cleanupDevelopmentData();
        console.log('Cleanup results:');
        console.table(cleanupResults);
      } else {
        console.log('Cleanup cancelled.');
      }
    }
    
    // Confirm generation
    const shouldGenerate = await confirmGeneration(settings);
    if (!shouldGenerate) {
      console.log('Operation cancelled.');
      return;
    }
    
    // Generate sample data
    console.log('Generating sample data...');
    console.log('This may take a while depending on the amount of data requested.');
    
    const startTime = Date.now();
    const results = await generator.generateSampleData(settings);
    const endTime = Date.now();
    
    console.log('\nGeneration complete!');
    console.log(`Total time: ${((endTime - startTime) / 1000).toFixed(2)} seconds`);
    console.log('\nGenerated data summary:');
    
    const summary = {
      organizations: results.organizations.length,
      users: results.users.length,
      products: results.products.length,
      panels: results.panels.length,
      projects: results.projects.length
    };
    
    console.table(summary);
    
    if (options.verbose) {
      console.log('\nGenerated organizations:');
      console.table(results.organizations.map(org => ({ id: org.organization_id, name: org.name })));
      
      console.log('\nGenerated users:');
      console.table(results.users.map(user => ({
        id: user.uid,
        name: `${user.first_name} ${user.last_name}`,
        email: user.email
      })));
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    if (options.verbose) {
      console.error(error);
    }
  }
}

// Wrap everything in an async IIFE to ensure inquirer is loaded
(async () => {
  try {
    // Load inquirer
    inquirer = (await import('inquirer')).default;
    
    // Run the main function
    await main();
  } catch (error) {
    console.error('Error:', error.message);
    if (options.verbose) {
      console.error(error);
    }
    process.exit(1);
  }
})(); 