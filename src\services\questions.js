const { QuestionRepository, OptionRepository } = require('../repositories');
const BaseService = require('./base');
const referenceHydrator = require('../util/referenceHydrator');
const { QUESTION_REFERENCES, QUESTION_FULL_REFERENCES } = require('../util/referenceMap');

class QuestionService extends BaseService {
  constructor() {
    super();
    this.questionRepository = new QuestionRepository();
    this.optionRepository = new OptionRepository();
  }

  /**
   * Create a new question with associated options
   * @param {Object} questionData - The question data
   * @param {Array} options - Array of option objects
   * @returns {Promise<Object>} - The created question with options
   */
  async createQuestion(questionData, options = []) {
    try {
      // Generate IDs if not provided
      if (!questionData.question_id) {
        questionData.question_id = await this.firestoreModule.getDocumentKey('questions');
      }

      // Initialize option IDs array
      questionData.optionIds = [];

      // First create the question
      await this.questionRepository.create(questionData);

      // If we have options to create
      if (options && options.length > 0) {
        // Add question_id and organization_id to all options
        const preparedOptions = [];
        
        for (const option of options) {
          preparedOptions.push({
            ...option,
            question_id: questionData.question_id,
            organization_id: questionData.organization_id,
            option_id: await this.firestoreModule.getDocumentKey('options')
          });
        }

        // Create all options in a batch
        console.log('preparedOptions', preparedOptions);
        const optionIds = await this.optionRepository.batchCreate(preparedOptions);

        // Update the question with option IDs
        await this.questionRepository.update(questionData.question_id, {
          optionIds: optionIds
        });

        // Return the question with hydrated options
        return this.getQuestionWithReferences(questionData.question_id, {
          includeOptions: true
        });
      }

      return questionData;
    } catch (error) {
      console.error('Error creating question:', error);
      throw error;
    }
  }

  /**
   * Get a question by ID
   * @param {string} questionId - The question ID
   * @returns {Promise<Object>} - The question data
   */
  async getQuestion(questionId) {
    return this.questionRepository.read(questionId);
  }

  /**
   * Update a question
   * @param {string} questionId - The question ID
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object>} - The updated question
   */
  async updateQuestion(questionId, updateData) {
    // We only allow updating certain fields, not options
    const allowedFields = ['name', 'stepInstructions', 'required'];
    
    const filteredUpdateData = Object.entries(updateData)
      .filter(([key]) => allowedFields.includes(key))
      .reduce((obj, [key, value]) => {
        obj[key] = value;
        return obj;
      }, {});
    
    if (Object.keys(filteredUpdateData).length === 0) {
      throw new Error('No valid fields to update');
    }
    
    return this.questionRepository.update(questionId, filteredUpdateData);
  }

  /**
   * Delete a question
   * @param {string} questionId - The question ID
   */
  async deleteQuestion(questionId) {
    await this.questionRepository.delete(questionId);
  }

  /**
   * Get all questions for an organization
   * @param {string} organizationId - The organization ID
   * @param {Object} options - Optional parameters
   * @returns {Promise<Array>} - Array of questions
   */
  async getQuestionsByOrganization(organizationId, options = {}) {
    const questions = await this.questionRepository.getQuestionsByOrganization(organizationId);
    
    if (options.includeOptions) {
      return this.getQuestionsWithReferences(questions, {
        includeOptions: true
      });
    }
    
    return questions;
  }

  /**
   * Get a question with hydrated references
   * @param {string} questionId - The question ID
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object>} - The question with hydrated references
   */
  async getQuestionWithReferences(questionId, referenceOptions = {}) {
    const question = await this.questionRepository.read(questionId);
    
    if (!question) {
      throw new Error('Question not found');
    }

    // Determine which reference map to use
    const { includeOptions = false } = referenceOptions;
    const referenceMap = includeOptions ? QUESTION_FULL_REFERENCES : QUESTION_REFERENCES;
    
    // Clear the hydrator cache to avoid stale data
    referenceHydrator.clearCache();
    
    // Hydrate references
    return referenceHydrator.hydrateDocument(question, referenceMap);
  }

  /**
   * Get multiple questions with hydrated references
   * @param {Array} questions - Array of question objects
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Array>} - Array of questions with hydrated references
   */
  async getQuestionsWithReferences(questions, referenceOptions = {}) {
    if (!questions || questions.length === 0) {
      return [];
    }
    
    // Determine which reference map to use
    const { includeOptions = false } = referenceOptions;
    const referenceMap = includeOptions ? QUESTION_FULL_REFERENCES : QUESTION_REFERENCES;
    
    // Clear the hydrator cache to avoid stale data
    referenceHydrator.clearCache();
    
    // Hydrate references for all questions
    return referenceHydrator.hydrateDocuments(questions, referenceMap);
  }
}

module.exports = QuestionService; 