/**
 * Sensory Analysis Panel Management API
 * 
 * This Node.js API server is part of a larger ecosystem for sensory analysis of products.
 * It provides endpoints for panel respondents to register, login, view their dashboard,
 * manage their responses, and participate in sensory panels.
 * 
 * The API is designed to work with a Firebase authentication system and will be consumed
 * by an Angular frontend application.
 */
const dotenv = require('dotenv');

dotenv.config();
const express = require('express');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const cors = require('cors');

const populateMJBizAggregates = require('./util/populateMJBizAggregates');

const routes = require('./routes');

const app = express();
app.use(cookieParser());
// Configure CORS
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['https://sensei.neuralresonance.icu'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
  credentials: true,
  maxAge: 86400 // 24 hours
};
app.use(cors(corsOptions));
app.use(bodyParser.json());

const port = process.env.PORT || 3000;

app.use('/', routes);

app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});


const getMetricAggregateData = async (organizationId, panelId=null) => {
  const satisfaction_key = "EPWnKabDghcaN3CzDjVI";
  const quality_key = "5SoZmBEhT3CJFO4O87BF";
  const taste_key = "u3lH3C15wFX771nZ6vyk";
  const potency_key = "nAsdrjdAA1X8yqSVoLLV";
  let filters = [
    ['organization_id', '==', organizationId],
    ["question_id", "==", "wjA1uhqbIMFuZwAaCoJ8"],//Overall/Metrics question key
  ];
  if (panelId) {
    filters.push(["panel_id", "==", panelId]);
  }
  const aggregatedResponseData = await firestoreModule.getAggregateData('responses', filters, {
    numResponses: firestoreModule.AggregateField.count(),
    averageSatisfaction: firestoreModule.AggregateField.average(satisfaction_key),
    averageQuality: firestoreModule.AggregateField.average(quality_key),
    averageTaste: firestoreModule.AggregateField.average(taste_key),
    averagePotency: firestoreModule.AggregateField.average(potency_key),
  });
  return aggregatedResponseData;
}

const getOverviewData = async (userId) => {
  const user = await redisCache.getUser(userId);
  const userOrg = "fT8BB1Pzuw6GJhdfiQcF";//user['organizations'][0]; //TODO: Make this a single "primaryOrganization" field for insights/admin users
  const metricAggregateData = await getMetricAggregateData(userOrg);
  const aggregatedPanelData = await firestoreModule.getAggregateData('panels', [
    ['organization_id', '==', userOrg],
    ['active', '==', true],
  ], {
    numPanels: firestoreModule.AggregateField.count(),
  });
  return {
    metricAggregateData,
    aggregatedPanelData
  };
  /*Get aggregated data for the organization in the following types:
    - Active Panel Count - db.collection('panels').where('organization_id', '==', userOrg).where('active', '==', true).count().get()
    - Response Count - db.collection('responses').where('organization_id', '==', userOrg).count().get()
    - Average Satisfaction Score of responses
    - Average quality score of responses
    - Average taste score of responses
    - Average Potency score of responses
  */
  /* Also need to figure out how to handle the following:
    - Responses over time
    - Demographic overview
    - Aroma Distribution across all products
  */
}

// app.get('/cms/overview', authenticateUser, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
//   try {
//     const userId = req.user.uid;
//     const overviewData = await getOverviewData(userId);
//     res.status(200).send(overviewData);
//   } catch (error) {
//     res.status(500).send('Error fetching overview data: ' + error.message);
//   }
// });

const countActiveUsers = require('./util/countActiveUsers');

app.get('/count-active-users', async (req, res) => {
  let excludePatterns = ['jmallett'];
  if (req.query.exclude) {
    excludePatterns = req.query.exclude.split(',');
  }
  const count = await countActiveUsers(excludePatterns);
  res.status(200).send(`Active users count: ${count}`);
});


(async () => {
  let excludePatterns = ['jmallett', 'ljadams', 'budboard', 'abstrax', 'elm732', 'bloomsensory', 'seedtalent','antoniox'];
  const count = await countActiveUsers(excludePatterns);
  console.log(`Active users count: ${count}`);
})();
// app.get('/populate-aggregates', async (req, res) => {
//   await populateMJBizAggregates();
//   res.status(200).send('MJBiz Aggregates populated');
// });

