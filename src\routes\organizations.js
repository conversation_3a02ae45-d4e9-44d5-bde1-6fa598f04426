const express = require('express');
const router = express.Router();

const { redisCache, firestoreModule } = require('../config');
const { authenticateUser, checkPermissions, paginationMiddleware, canAccessOrganization } = require('../middleware');
const { validateOrganization } = require('../models/organization');
const { UserService, OrganizationService } = require('../services');
router.all('*', authenticateUser);

/**
 * @swagger
 * /organizations:
 *   get:
 *     summary: Get all organizations
 *     description: Retrieves all organizations accessible to the admin user with pagination
 *     tags: [Organizations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of organizations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 organizations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Organization'
 *                 total:
 *                   type: integer
 *                   description: Total number of organizations
 *                 page:
 *                   type: integer
 *                   description: Current page number
 *                 limit:
 *                   type: integer
 *                   description: Number of items per page
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have admin permissions
 *       500:
 *         description: Server error
 */
router.get('/', checkPermissions(['admin']), paginationMiddleware, async (req, res) => {
    try {
      const userId = req.user.uid;

      const organizations = await OrganizationService.getOrganizationsByUserId(userId);

      // Apply pagination
      const start = req.pagination.startIndex;
      const end = start + req.pagination.limit;
      const paginatedOrgs = organizations.slice(start, end);
  
      res.status(200).send({
        organizations: paginatedOrgs,
        total: organizations.length,
        page: req.pagination.page,
        limit: req.pagination.limit
      });
  
    } catch (error) {
      res.status(500).send('Error fetching organizations: ' + error.message);
    }
  });
  
/**
 * @swagger
 * /organizations:
 *   post:
 *     summary: Create a new organization
 *     description: Creates a new organization with the provided data
 *     tags: [Organizations]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Organization'
 *     responses:
 *       201:
 *         description: Organization created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Organization'
 *                 - type: object
 *                   properties:
 *                     organization_id:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have admin permissions
 *       500:
 *         description: Server error
 */
  router.post('/', checkPermissions(['admin']), async (req, res) => {
    try {
      const userId = req.user.uid;
      const orgData = req.body;

      const orgId = await OrganizationService.createOrganization(userId, orgData)
  
      res.status(201).send({
        organization_id: orgId,
        ...orgData
      });
  
    } catch (error) {
      res.status(500).send('Error creating organization: ' + error.message);
    }
  });
  
/**
 * @swagger
 * /organizations/users/{org_id}:
 *   get:
 *     summary: Get users in an organization
 *     description: Retrieves all users belonging to a specific organization
 *     tags: [Organizations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: org_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the organization
 *     responses:
 *       200:
 *         description: List of users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot access this organization
 *       500:
 *         description: Server error
 */
  router.get('/users/:org_id', canAccessOrganization, checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    const orgId = req.params.org_id;
    const users = await UserService.getUsersByOrganization(orgId);
    res.status(200).send(users);
  });

/**
 * @swagger
 * /organizations/{org_id}:
 *   get:
 *     summary: Get organization details
 *     description: Retrieves detailed information about a specific organization
 *     tags: [Organizations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: org_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the organization to retrieve
 *     responses:
 *       200:
 *         description: Organization details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Organization'
 *                 - type: object
 *                   properties:
 *                     organization_id:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have required permissions
 *       500:
 *         description: Server error
 */
  router.get('/:org_id', checkPermissions(['survey', 'admin', 'insights']), async (req, res) => {
    try {
      const orgId = req.params.org_id;
      
      // Try to get org data from Redis cache
      let orgDetails = await OrganizationService.getOrganizationById(orgId)
  
      res.status(200).send({
        organization_id: orgId,
        ...orgDetails
      });
    } catch (error) {
      res.status(500).send('Error fetching organization details: ' + error.message);
    }
  });
  
/**
 * @swagger
 * /organizations/{org_id}:
 *   put:
 *     summary: Update organization details
 *     description: Updates an existing organization with new name or website data
 *     tags: [Organizations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: org_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the organization to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: New organization name
 *               website:
 *                 type: string
 *                 description: New organization website URL
 *     responses:
 *       200:
 *         description: Organization updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Organization'
 *                 - type: object
 *                   properties:
 *                     organization_id:
 *                       type: string
 *       400:
 *         description: Bad request - must provide name or website to update
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have admin/insights permissions
 *       500:
 *         description: Server error
 */
  router.put('/:org_id', checkPermissions(['admin', 'insights']), async (req, res) => {
    try {
      const orgId = req.params.org_id;
      const { name, website } = req.body;
  
      // Only allow name and website updates
      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (website !== undefined) updateData.website = website;
  
      if (Object.keys(updateData).length === 0) {
        return res.status(400).send({ message: 'Must provide name or website to update' });
      }
  
      const updatedOrg = await OrganizationService.updateOrganization(orgId, updateData)
     
      res.status(200).send({
        organization_id: orgId,
        ...updatedOrg
      });
  
    } catch (error) {
      res.status(500).send('Error updating organization: ' + error.message);
    }
  });

module.exports = router;