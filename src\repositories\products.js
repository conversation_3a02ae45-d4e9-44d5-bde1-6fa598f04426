const { validateProduct, validateProductUpdate } = require("../models/product");
const BaseRepository = require("./base");

class ProductRepository extends BaseRepository {
  constructor() {
    super();
  }

  async create(productData) {
    const { error } = validateProduct(productData);
    if (error) {
      throw new Error(`Invalid product data: ${error.message}`);
    }

    // Create product in Firestore
    const productId = await this.firestoreModule.createDocument(
      "products",
      productData,
      productData.product_id
    );
    const aggregateId = await this.firestoreModule.createDocument(
      "aggregates",
      { count: 0 },
      productData.product_id
    );

    // Add product ID to organization's product list in Redis
    await this.redisCache.addProductToOrganization(
      productData.organization_id,
      productData.product_id
    );

    // Cache the new product data
    await this.redisCache.setProductData(productData.product_id, productData);
  }

  async read(productId) {
    let productDetails = await this.redisCache.getProductData(productId);

    if (!productDetails) {
      console.log("Product not in cache, fetching from Firestore");
      // If not in cache, fetch from Firestore
      productDetails = await this.firestoreModule.getDocument("products", productId);
      if (!productDetails) {
        throw new Error("Product not found");
      }
      // Cache the result
      await this.redisCache.setProductData(productId, productDetails);
    }

    return productDetails;
  }

  async update(id, updateData) {
    // Validate update data against schema
    const { error } = validateProductUpdate(updateData);
    if (error) {
      throw new Error(`Invalid product data: ${error.message}`);
    }

    const updated = await this.firestoreModule.updateDocument(
      "products",
      id,
      updateData
    );
    if (!updated) {
      throw new Error("Product not found");
    }

    // Update Redis cache with new data
    await this.redisCache.setProductData(id, updateData);

    return updated;
  }
  async delete(id) {
    await this.firestoreModule.markDeleted(id,"products");
    await this.redisCache.deleteProduct(id);
  }
}

module.exports = ProductRepository;
