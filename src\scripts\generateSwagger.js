/**
 * <PERSON><PERSON><PERSON> to generate a Swagger documentation file from JSDoc annotations
 * Run with: node scripts/generateSwagger.js
 */

const fs = require('fs');
const path = require('path');
const swaggerJsdoc = require('swagger-jsdoc');
const YAML = require('js-yaml');

// Swagger definition options
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Sensory Analysis Panel Management API',
      description: 'API for managing sensory analysis panels, user registration, responses and panel data',
      version: '1.0.0',
    },
    servers: [
      {
        url: 'https://sensei.neuralresonance.icu',
        description: 'Remote development server',
      },
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  // Path to the API docs
  apis: [
    path.resolve(__dirname, '../models/*.js'),
    path.resolve(__dirname, '../routes/*.js')
  ],
};

// Generate the Swagger specification
const swaggerSpec = swaggerJsdoc(options);

// Output paths
const outputJsonPath = path.resolve(__dirname, '../swagger.json');
const outputYamlPath = path.resolve(__dirname, '../swagger.yaml');

try {
  // Write the specification to JSON and YAML files
  fs.writeFileSync(outputJsonPath, JSON.stringify(swaggerSpec, null, 2), 'utf8');
  console.log(`✅ Swagger JSON specification written to: ${outputJsonPath}`);
  
  fs.writeFileSync(outputYamlPath, YAML.dump(swaggerSpec), 'utf8');
  console.log(`✅ Swagger YAML specification written to: ${outputYamlPath}`);
  
  // Print summary
  const endpointCount = Object.keys(swaggerSpec.paths).length;
  const schemaCount = Object.keys(swaggerSpec.components?.schemas || {}).length;
  
  console.log('\n📊 Documentation Summary:');
  console.log(`- ${endpointCount} endpoints documented`);
  console.log(`- ${schemaCount} schemas defined`);
  
  // List all documented endpoints
  console.log('\n🛣️  Documented Endpoints:');
  Object.keys(swaggerSpec.paths).forEach(path => {
    const methods = Object.keys(swaggerSpec.paths[path]);
    methods.forEach(method => {
      console.log(`- ${method.toUpperCase()} ${path}`);
    });
  });
  
  console.log('\n🔄 To update the Swagger UI, make sure your docs.js is set up to serve these files');
} catch (error) {
  console.error('❌ Error generating Swagger documentation:', error);
  process.exit(1);
} 