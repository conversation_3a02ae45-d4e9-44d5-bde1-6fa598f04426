const PanelRepository = require('./panels');
const ProductRepository = require ('./products');
const OrganizationRepository = require ('./organizations');
const UserRepository = require('./user');
const ProjectRepository = require('./projects');
const GroupRepository = require('./groups');
const ResponseRepository = require('./responses');
const QuestionRepository = require('./questions');
const OptionRepository = require('./options');
const PanelTemplateRepository = require('./panel-templates');

module.exports = {
    PanelRepository,
    ProductRepository,
    OrganizationRepository,
    UserRepository,
    ProjectRepository,
    GroupRepository,
    ResponseRepository,
    QuestionRepository,
    OptionRepository,
    PanelTemplateRepository
}