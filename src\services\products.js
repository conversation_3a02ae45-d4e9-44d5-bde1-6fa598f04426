const { ProductRepository, UserRepository } = require("../repositories");
const BaseService = require("./base");

class ProductService extends BaseService {
  constructor() {
    super();
    this.ProductRepository = new ProductRepository();
    this.UserRepository =  UserRepository;
  }

  async getProducts(userId, organization) {
    const user = await this.UserRepository.read(userId);
    const userOrgs =
      organization && user["organizations"].includes(organization)
        ? [organization]
        : user["organizations"];

    let products = [];
    for (const orgId of userOrgs) {
      const orgProducts = await this.redisCache.getOrganizationProductIds(orgId);
      products = [...products, ...orgProducts];
    }

    return products;
  }

  async getProduct(productId) {
    const productDetails = await this.ProductRepository.read(productId);
    return productDetails;
  }

  async postProduct(productData) {
    try {
      const new_product_id = await this.firestoreModule.getDocumentKey(
        "products"
      );

      let new_product = {
        product_id: new_product_id,
        deleted: false,
        ...productData,
      };

      //Add external_id if not provided
      new_product["external_id"] = new_product["external_id"] || "";

      //TODO: Add default image
      if (!new_product["image"] || new_product["image"] === "") {
        //TODO: Add default image
        new_product["image"] =
          "https://static.thenounproject.com/png/5191452-200.png";
      } else {
        //TODO: Add image upload to blob storage and set new_product['image'] to the new image url
        //new_product['image'] = await blobStorageModule.uploadImage(new_product['image']);
        const imgURL = await this.cloudStorage.uploadImage(
          new_product["image"]
        );
        new_product["image"] = imgURL;
      }
      console.log("Checking new product data: ", new_product);
      await this.ProductRepository.create(new_product);

      return new_product;
    } catch (error) {
      console.log("here", error);
      throw new Error("Error creating product: " + error.message);
    }
  }

  async updateProduct(productId, updateData) {
    try {
        const updated = await this.ProductRepository.update(productId, updateData);
        console.log('updated', updated);
      return updated;
    } catch (error) {
      throw new Error("Error updating product: " + error.message);
    }
  }

  async removeProduct(productId) {
    try {
      await this.ProductRepository.delete(productId);
    } catch (error) {
      throw new Error("Error deleting product: " + error.message);
    }
  }
}

module.exports = new ProductService();
