const express = require("express");
const router = express.Router();

const { firestoreModule, redisCache } = require("../config");
const { authenticateFirebase, authenticateUser } = require("../middleware");
const { validateUser } = require("../models/user");
const { OrganizationService } = require("../services");

/**
 * @swagger
 * /auth/session:
 *   get:
 *     summary: Create a session
 *     description: Creates a session cookie for the authenticated user
 *     tags: [Authentication]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Session created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Failed to create session
 */
router.get("/session", authenticateFirebase, async (req, res) => {
  try {
    const idToken = req.headers.authorization;
    // Create a session cookie using the ID token
    const expiresIn = 60 * 60 * 24 * 5 * 1000; // 5 days in milliseconds
    const sessionCookie = await firestoreModule.auth.createSessionCookie(
      idToken,
      { expiresIn }
    );

    // Set cookie options
    const options = {
      maxAge: expiresIn,
      httpOnly: true,
      secure: true,
      sameSite: "None",
      path: "/",
      domain:
        process.env.NODE_ENV === "production"
          ? determineDomainFromRequest(req)
          : "",
    };

    // Set the session cookie
    res.cookie("session", sessionCookie, options);
    res.status(200).send({ user: req.user });
  } catch (error) {
    res.status(401).send("Failed to create session");
  }
});

// Helper function to determine domain from the request
function determineDomainFromRequest(req) {
  const origin = req.get("origin");
  const host = req.get("host");

  // List of allowed domain roots
  const allowedDomains = [".abstraxtech.com", ".sensorypanels.com"];

  // Check origin header first
  if (origin) {
    try {
      const hostname = new URL(origin).hostname;
      console.log("Hostname: ", hostname);
      for (const domain of allowedDomains) {
        if (hostname.endsWith(domain.substring(1))) {
          console.log("Domain: ", domain);
          return domain;
        }
      }
    } catch (e) {
      // Invalid origin URL, fall through
      console.error("Invalid origin URL: ", e);
      throw new Error("Invalid origin URL");
    }
  }

  // Check host header as fallback
  if (host) {
    for (const domain of allowedDomains) {
      if (host.endsWith(domain.substring(1))) {
        console.log("Domain: ", domain);
        return domain;
      }
    }
  }

  // Default fallback
  console.log("Default fallback: ", ".abstraxtech.com");
  return ".abstraxtech.com";
}

/**
 * @swagger
 * /auth/logout:
 *   get:
 *     summary: Logout user
 *     description: Revokes refresh tokens and clears the session cookie
 *     tags: [Authentication]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Logged out successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 */
router.get("/logout", authenticateUser, async (req, res) => {
  await firestoreModule.auth.revokeRefreshTokens(req.user.uid);
  res.clearCookie("session", {
    httpOnly: true,
    secure: true, // if using HTTPS
    sameSite: "None",
    path: "/",
    domain:
      process.env.NODE_ENV === "production"
        ? determineDomainFromRequest(req)
        : "",
  });
  res.status(200).json({ message: "Logged out successfully" });
});

/**
 * @swagger
 * /auth/session-validity:
 *   get:
 *     summary: Check session validity
 *     description: Validates if the current session is still valid
 *     tags: [Authentication]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Session is valid
 *         content:
 *           application/json:
 *             schema:
 *               type: boolean
 *       401:
 *         description: Unauthorized - session is invalid
 */
router.get("/session-validity", authenticateUser, async (req, res) => {
  res.status(200).send(true);
});

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Register new user
 *     description: Creates a new user account with the provided information
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - first_name
 *               - last_name
 *               - zipCode
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               zipCode:
 *                 type: string
 *               affiliateCode:
 *                 type: string
 *               marketingOptIn:
 *                 type: boolean
 *               isBrand:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 userId:
 *                   type: string
 *       400:
 *         description: Invalid user data or affiliate code
 *       500:
 *         description: Server error
 */
router.post("/register", async (req, res) => {
  try {
    const { email, password, affiliateCode, orgName, website, ...userData } =
      req.body;
    console.log("User data: ", req.body);
    let organizationIds = null;
    if (affiliateCode) {
      // Validate affiliate code if provided
      console.log("Affiliate code: ", affiliateCode);
      const validatedCode = await firestoreModule.validateAffiliateCode(
        affiliateCode
      );
      if (validatedCode) {
        organizationIds = validatedCode.organizations;
      } else {
        return res.status(400).send("Invalid affiliate code");
      }
    } else {
      console.log("No affiliate code provided");

      const orgData = {
        name: orgName,
        website: website,
      };

      const orgId = await OrganizationService.createOrganization(
        "",
        orgData
      );

      if (orgId) {
        console.log(orgId);
        organizationIds = [orgId];
      }
    }

    // Create user in Firebase
    const userRecord = await firestoreModule.auth.createUser({
      email: email,
      password: password,
    });

    console.log("User record: ", userRecord);
    // Prepare user data
    const userDataToStore = {
      first_name: userData.first_name,
      last_name: userData.last_name,
      email,
      zipCode: userData.zipCode,
      firstLogin: true,
      uid: userRecord.uid,
      marketingOptIn: userData.marketingOptIn || false,
      organizations: organizationIds ? organizationIds : [],
      roles: ["survey"],
    };

    if (userData.isBrand === true) {
      userDataToStore.roles.push("insights");
    }

    console.log("User data to store: ", userDataToStore);

    // let orgs  = await OrganizationService.getOrganizationsByUserId(userDataToStore.uid);
    // console.log("Orgs: ", orgs);

    // Validate user data against schema
    const isValid = validateUser(userDataToStore);
    console.log("Is valid: ", isValid);
    if (isValid.error) {
      // Clean up created Firebase user since validation failed
      await firestoreModule.auth.deleteUser(userRecord.uid);
      return res.status(400).send(`Invalid user data: ${error.message}`);
    }

    // Store validated user data in database
    await firestoreModule.storeUserData(userRecord.uid, userDataToStore);
    await redisCache.setUser(userRecord.uid, userDataToStore);

    res
      .status(201)
      .send({
        message: "User registered successfully",
        userId: userRecord.uid,
      });
  } catch (error) {
    res.status(500).send("Error registering user: " + error.message);
  }
});

module.exports = router;
