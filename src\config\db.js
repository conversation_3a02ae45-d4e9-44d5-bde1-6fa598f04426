const admin = require('firebase-admin');

class FirestoreModule {
  constructor() {
    const serviceAccount = require(process.env.FIREBASE_SERVICE_ACCOUNT_PATH);
    const databaseURL = process.env.FIREBASE_DATABASE_URL;
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: databaseURL
    });
    this.db = admin.firestore();
    this.auth = admin.auth();
    this.FieldValue = admin.firestore.FieldValue;
    this.AggregateField = admin.firestore.AggregateField;
    this.Timestamp = admin.firestore.Timestamp;
    this.db.settings({ ignoreUndefinedProperties: true })
  }

  async getDocumentKey(collection){
    return this.db.collection(collection).doc().id;
  }

  async getDocument(collection, docId){
    const docRef = this.db.collection(collection).doc(docId);
    const doc = await docRef.get();
    if (!doc.exists) {
      throw new Error('Document not found');
    }
    return { id: doc.id, ...doc.data() };
  }

  async getUsers() {
    return await this.db.collection('users').get();
  }

  async validateAffiliateCode(affiliateCode) {
    const affiliateRef = this.db.collection('affiliateCodes').where('code', '==', affiliateCode);
    const snapshot = await affiliateRef.get();
    if (!snapshot.empty) {
      const doc = snapshot.docs[0];
      let data = doc.data();
      return data;
    }
    return null;
  }

  async documentExists(collection, docId) {
    const docRef = this.db.collection(collection).doc(docId);
    const doc = await docRef.get();
    return doc.exists;
  }

  async fetchDataFromFirestore(type, id){
    return await this.db.collection(type).doc(id).get();
  }

  //generalized update function for all collections
  async updateDocument(collection, id, data) {
    //console.log("Updating document: ", collection, id, data);
    data.updated = this.FieldValue.serverTimestamp();
    return this.db.collection(collection).doc(id).update(data);
  }

  //generalized create function for all collections
  async createDocument(collection, data, key=null) {
    data.updated = this.FieldValue.serverTimestamp();
    data.created = this.FieldValue.serverTimestamp();
    data.deleted = false;
    if(key){
      return this.db.collection(collection).doc(key).set(data);
    } else {
      return this.db.collection(collection).add(data);
    }
  }

  async storeUserData(uid, userData) {
    await this.createDocument('users', userData, uid);
  }

  async getUserStats(userId) {
    const userRef = this.db.collection('users').doc(userId);
    const doc = await userRef.get();
    if (!doc.exists) {
      throw new Error('User not found');
    }
    return doc.data();
  }

  async getUserResponses(userId) {
    const responsesRef = this.db.collection('responses').where('userId', '==', userId);
    const snapshot = await responsesRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async getResponseDetails(userId, responseId) {
    const responseRef = this.db.collection('responses').doc(responseId);
    const doc = await responseRef.get();
    if (!doc.exists || doc.data().userId !== userId) {
      throw new Error('Response not found or unauthorized');
    }
    return { id: doc.id, ...doc.data() };
  }

  async getAvailablePanels(userId) {
    const userRef = this.db.collection('users').doc(userId);
    const userDoc = await userRef.get();
    const userData = userDoc.data();
    //console.log("User data: ", userData);
    const userOrganizations = userData?.organizations || [];

    const MAX_IN_QUERY_ELEMENTS = 10; // Firestore limit for 'in' queries
    let allPanels = [];

    // If userOrganizations exceeds the Firestore limit, we need to batch the queries
    for (let i = 0; i < userOrganizations.length; i += MAX_IN_QUERY_ELEMENTS) {
      const batch = userOrganizations.slice(i, i + MAX_IN_QUERY_ELEMENTS);
      const panelsRef = this.db.collection('panels')
        .where('active', '==', true)
        .where('organization_id', 'in', batch)
        .where('deleted', '==', false);
      const snapshot = await panelsRef.get();
      allPanels = allPanels.concat(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
    }

    return allPanels;
  }

  async getPanelDetails(panelId) {
    const panelRef = this.db.collection('panels').doc(panelId);
    const doc = await panelRef.get();
    if (!doc.exists) {
      return null;
    }
    return { id: doc.id, ...doc.data() };
  }

  async updateUserPanelAccess(userId, newPanelIds) {
    await this.updateDocument('users', userId, { accessiblePanels: newPanelIds });
  }

  async markDeleted(documentId, collection) {
    await this.updateDocument(collection, documentId, { deleted: true });
  }

  //Delete a panel from the database
  async deletePanelData(panelId) {
    await this.db.collection('panels').doc(panelId).delete();
  }

  async fetchUserFromFirestore(userId) {
    const userRef = this.db.collection('users').doc(userId);
    const doc = await userRef.get();
    return doc.data();
  }

  async fetchInProgressPanel(userId, panelId) {
    return await this.fetchCompletedPanel(userId, panelId);
    // const panelRef = this.db.collection('users').doc(userId).collection("panels").doc(panelId);
    // const doc = await panelRef.get();
    // return doc.data();
  }

  async countPanelsUsingProduct(productId){
    const panelsRef = this.db.collection('panels').where('product_id', '==', productId).where('deleted', '==', false);
    const aggregateQuery = panelsRef.aggregate({
      count: this.AggregateField.count()
    });
    const snapshot = await aggregateQuery.get();
    return snapshot.data().count;
  }

  async countPanelResponses(panelId) {
    try {
      const responsesRef = this.db.collection('responses').where('panel_id', '==', panelId);
      const aggregateQuery = responsesRef.aggregate({
        count: this.AggregateField.count()
      });
      const snapshot = await aggregateQuery.get();
      return snapshot.data().count;
    } catch (error) {
      console.error(`Error counting responses for panel ${panelId}: ${error.message}`);
      return 0;
    }
  }

  //TODO: Add canAccess Middleware
  async getAggregateData(collection, filters, aggregateObject) {
    let collectionRef = this.db.collection(collection);//.where('organization_id', '==', organizationId);
    for(const filter of filters){
      collectionRef = collectionRef.where(filter[0], filter[1], filter[2]);
    }
    const aggregateQuery = collectionRef.aggregate(aggregateObject);
    const snapshot = await aggregateQuery.get();
    return snapshot.data();
  }

  async fetchCompletedPanels(userId) {
    const panelsRef = this.db.collection('users').doc(userId).collection("panels").where('completed', '==', true);
    const snapshot = await panelsRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async fetchCompletedPanel(userId, panelId) {
    const panelRef = this.db.collection('users').doc(userId).collection("panels").doc(panelId);
    const doc = await panelRef.get();
    if (!doc.exists) {
      return null;
    }
    return { id: doc.id, ...doc.data() };
  }

  async fetchInProgressPanels(userId) {
    const panelsRef = this.db.collection('users').doc(userId).collection("panels").where('completed', '==', false);
    const snapshot = await panelsRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id }));
  }

  async fetchPublicPanelsFromFirestore() {
    const panelsRef = this.db.collection('panels').where('isPublic', '==', true).where('deleted', '==', false);
    const snapshot = await panelsRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id }));
  }

  async fetchCommentsByPanelId(panelId, questionId = "awja0ameTALoE0D4GlWD") {
    const commentsRef = this.db.collection('responses').where('panel_id', '==', panelId).where('question_id', '==', questionId);
    const snapshot = await commentsRef.select('comment', 'created').get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async fetchCommentsByProductId(productId, questionId = "awja0ameTALoE0D4GlWD") {
    const commentsRef = this.db.collection('responses').where('product_id', '==', productId).where('question_id', '==', questionId);
    const snapshot = await commentsRef.select('comment', 'created').get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async fetchOrganizationProductsFromFirestore(organizationId) {
    const productsRef = this.db.collection('products').where('organization_id', '==', organizationId).where('deleted', '==', false);
    const snapshot = await productsRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async fetchOrganizationPanelsFromFirestore(organizationId) {
    console.log("Fetching organization panels from Firestore for organization: ", organizationId);
    const panelsRef = this.db.collection('panels').where('organization_id', '==', organizationId).where('active', '==', true).where('deleted', '==', false);
    const snapshot = await panelsRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async fetchUserPanelIdsFromFirestore(userId) {
    const panels = await this.getAvailablePanels(userId);
    return panels.map(panel => panel.id);
  }

  async fetchPanelDataFromFirestore(panelId) {
    return await this.getPanelDetails(panelId);
  }

  async getDocumentsByCollection(collection, limit = 100) {
    const snapshot = await this.db.collection(collection).limit(limit).get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async getDocumentsByQuery(collection, queries = [], limit = 100) {
    let ref = this.db.collection(collection);
    for (const [field, operator, value] of queries) {
      ref = ref.where(field, operator, value);
    }
    if (limit) ref = ref.limit(limit);
    const snapshot = await ref.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async softDeleteDocument(collection, docId, additionalData = {}) {
    const data = {
      deleted: true,
      deletedAt: this.FieldValue.serverTimestamp(),
      ...additionalData
    };
    return this.updateDocument(collection, docId, data);
  }

  async restoreDocument(collection, docId) {
    const data = {
      deleted: false,
      deletedAt: null,
      restoredAt: this.FieldValue.serverTimestamp()
    };
    return this.updateDocument(collection, docId, data);
  }

  async getProductByExternalId(externalId) {
    // Unfortunately, Firestore doesn't support OR queries directly
    // We need to perform two separate queries and combine the results
    
    // First query: check for exact match with external_id field
    const exactMatchRef = this.db.collection('products').where('external_id', '==', externalId);
    const exactMatchSnapshot = await exactMatchRef.get();
    
    // Second query: check if externalId is in the external_ids array
    const arrayMatchRef = this.db.collection('products').where('external_ids', 'array-contains', externalId);
    const arrayMatchSnapshot = await arrayMatchRef.get();
    
    // Combine results from both queries
    const results = [
      ...exactMatchSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })),
      ...arrayMatchSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    ];
    
    // Remove duplicates (in case a product matches both conditions)
    const uniqueResults = Array.from(new Map(results.map(item => [item.id, item])).values());
    
    return uniqueResults;
  }

  async getDocumentHistory(collection, docId, limit = 100) {
    const historyRef = this.db.collection(`${collection}_history`)
      .where('documentId', '==', docId)
      .orderBy('timestamp', 'desc')
      .limit(limit);
    const snapshot = await historyRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async bulkUpdateDocuments(collection, query, updates) {
    const batch = this.db.batch();
    const snapshot = await this.db.collection(collection).where(...query).get();
    
    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        ...updates,
        updated: this.FieldValue.serverTimestamp()
      });
    });

    return batch.commit();
  }

  async getUsersByOrganization(organizationId) {
    const usersRef = this.db.collection('users')
      .where('organizations', 'array-contains', organizationId);
      // .where('deleted', '!=', true);
    
    const snapshot = await usersRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async getPanelResponseUsers(panelId) {
    // Get unique user IDs from responses for this panel
    const responsesRef = this.db.collection('responses')
      .where('panel_id', '==', panelId)
      .select('user_id');  // Only fetch the userId field for efficiency
    
    const snapshot = await responsesRef.get();
    const userIds = [...new Set(snapshot.docs.map(doc => doc.data().user_id))];

    if (userIds.length === 0) {
      return [];
    }
    console.log("User IDs: ", userIds);

    // Fetch user details for these IDs
    // Firestore has a limit of 10 items in 'in' queries, so we need to batch
    const batchSize = 10;
    let users = [];
    
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);
      const usersRef = this.db.collection('users')
        .where('uid', 'in', batch);
      
      const userSnapshot = await usersRef.get();
      users.push(...userSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
    }

    return users;
  }

  async fetchOrganizationProjectsFromFirestore(organizationId) {
    const projectsRef = this.db.collection('projects')
      .where('organization_id', '==', organizationId)
      .where('deleted', '==', false);
    const snapshot = await projectsRef.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  async batchCreateDocuments(collection, documents, idField = null) {
    const batch = this.db.batch();
    
    for (const doc of documents) {
      const docRef = this.db.collection(collection).doc(idField ? doc[idField] : null);
      doc.created = this.FieldValue.serverTimestamp();
      doc.updated = this.FieldValue.serverTimestamp();
      doc.deleted = false;
      batch.set(docRef, doc);
    }
    
    return batch.commit();
  }

  async deleteDocumentsWhere(collection, whereConditions) {
    // Build the query with conditions
    let query = this.db.collection(collection);
    for (const condition of whereConditions) {
      query = query.where(condition[0], condition[1], condition[2]);
    }
    
    // Get documents matching the query
    const snapshot = await query.get();
    
    // If no documents, return early
    if (snapshot.empty) {
      return;
    }
    
    // Delete the documents in batches of 500 (Firestore limit)
    const batchSize = 500;
    const batches = [];
    let batch = this.db.batch();
    let operationCount = 0;
    
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
      operationCount++;
      
      if (operationCount >= batchSize) {
        batches.push(batch.commit());
        batch = this.db.batch();
        operationCount = 0;
      }
    });
    
    // Commit any remaining operations
    if (operationCount > 0) {
      batches.push(batch.commit());
    }
    
    // Wait for all batches to complete
    await Promise.all(batches);
  }

  async queryDocuments(collection, whereConditions, orderBy = null, limit = null) {
    // Build the query with conditions
    let query = this.db.collection(collection);
    for (const condition of whereConditions) {
      query = query.where(condition[0], condition[1], condition[2]);
    }
    
    // Add optional orderBy
    if (orderBy) {
      query = query.orderBy(orderBy.field, orderBy.direction || 'asc');
    }
    
    // Add optional limit
    if (limit) {
      query = query.limit(limit);
    }
    
    // Execute the query
    const snapshot = await query.get();
    
    // Return the documents
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  //Get all documents from a collection
  async getCollection(collection) {
    const snapshot = await this.db.collection(collection).get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  /**
   * Run multiple operations in a transaction
   * @param {Function} transactionFn - Function that receives the transaction object and executes operations
   * @returns {Promise<any>} - Result of the transaction
   */
  async runTransaction(transactionFn) {
    try {
      return await this.db.runTransaction(transactionFn);
    } catch (error) {
      console.error(`Transaction failed: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new FirestoreModule();