const { firestoreModule } = require('../config');

async function populateAggregates(){
    const panels = await firestoreModule.db.collection('panels').get();
    for(let panel of panels.docs){
        await firestoreModule.createDocument("aggregates", { count: 0 }, panel.id);
    }
    const products = await firestoreModule.db.collection('products').get();
    for(let product of products.docs){
        await firestoreModule.createDocument("aggregates", { count: 0 }, product.id);
    }
}

module.exports = populateAggregates;