const ResponseRepository = require("../repositories/responses");
const { UserRepository, PanelRepository, ProductRepository } = require("../repositories");
const BaseService = require("./base");
const referenceHydrator = require('../util/referenceHydrator');
const { RESPONSE_REFERENCES, RESPONSE_FULL_REFERENCES } = require('../util/referenceMap');

class ResponseService extends BaseService {
  constructor() {
    super();
    this.ResponseRepository = new ResponseRepository();
    this.UserRepository = UserRepository;
    this.PanelRepository = PanelRepository;
    this.ProductRepository = new ProductRepository();
  }

  async createResponse(responseData) {
    try {
      // Verify that the related entities exist
      await Promise.all([
        this.UserRepository.read(responseData.user_id),
        this.PanelRepository.read(responseData.panel_id),
        this.ProductRepository.read(responseData.product_id)
      ]);

      // Set creation timestamp
      responseData.created_at = this.firestoreModule.Timestamp.now();
      
      // Create the response
      const responseId = await this.ResponseRepository.create(responseData);
      
      return { ...responseData, id: responseId };
    } catch (error) {
      console.error(error);
      throw new Error(`Error creating response: ${error.message}`);
    }
  }

  async getResponse(responseId) {
    try {
      const responseDetails = await this.ResponseRepository.read(responseId);
      return responseDetails;
    } catch (error) {
      throw new Error(`Error getting response: ${error.message}`);
    }
  }

  async updateResponse(responseId, updateData) {
    try {
      // Update the updated_at timestamp
      updateData.updated_at = this.firestoreModule.Timestamp.now();

      const updated = await this.ResponseRepository.update(responseId, updateData);
      return updated;
    } catch (error) {
      throw new Error(`Error updating response: ${error.message}`);
    }
  }

  async deleteResponse(responseId) {
    try {
      await this.ResponseRepository.delete(responseId);
    } catch (error) {
      throw new Error(`Error deleting response: ${error.message}`);
    }
  }

  async getResponsesByUser(userId) {
    try {
      return await this.ResponseRepository.getResponsesByUser(userId);
    } catch (error) {
      throw new Error(`Error getting responses by user: ${error.message}`);
    }
  }

  async getResponsesByPanel(panelId) {
    try {
      return await this.ResponseRepository.getResponsesByPanel(panelId);
    } catch (error) {
      throw new Error(`Error getting responses by panel: ${error.message}`);
    }
  }

  async getResponsesByProduct(productId) {
    try {
      return await this.ResponseRepository.getResponsesByProduct(productId);
    } catch (error) {
      throw new Error(`Error getting responses by product: ${error.message}`);
    }
  }

  async getResponseByPanelAndUser(panelId, userId) {
    try {
      return await this.ResponseRepository.getResponseByPanelAndUser(panelId, userId);
    } catch (error) {
      throw new Error(`Error getting response by panel and user: ${error.message}`);
    }
  }

  /**
   * Get response with hydrated references
   * @param {string} responseId - The response ID
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object>} - Hydrated response
   */
  async getResponseWithReferences(responseId, referenceOptions = {}) {
    try {
      // Get base response
      const response = await this.ResponseRepository.read(responseId);
      if (!response) {
        throw new Error("Response not found");
      }
      
      // Determine which references to hydrate
      const { 
        references = RESPONSE_REFERENCES,
        includeFullDetails = false
      } = referenceOptions;
      
      // Use the full reference map if requested
      const referenceMap = includeFullDetails ? RESPONSE_FULL_REFERENCES : references;
      
      // Hydrate references
      const hydratedResponse = await referenceHydrator.hydrateDocument(response, referenceMap);
      
      return hydratedResponse;
    } catch (error) {
      console.error(`Error fetching response with references: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get responses with hydrated references
   * @param {Array} responses - Array of response objects
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object[]>} - Hydrated responses
   */
  async getResponsesWithReferences(responses, referenceOptions = {}) {
    try {
      if (!responses || responses.length === 0) {
        return [];
      }
      
      // Determine which references to hydrate
      const { 
        references = RESPONSE_REFERENCES,
        includeFullDetails = false,
        includeFields = null  // Optional fields to include in result
      } = referenceOptions;
      
      // Use the full reference map if requested
      const referenceMap = includeFullDetails ? RESPONSE_FULL_REFERENCES : references;
      
      // Hydrate all responses in parallel
      const hydratedResponses = await referenceHydrator.hydrateDocuments(responses, referenceMap);
      
      // Filter fields if requested
      if (includeFields) {
        return hydratedResponses.map(response => {
          return includeFields.reduce((result, field) => {
            if (response[field] !== undefined) {
              result[field] = response[field];
            }
            return result;
          }, {});
        });
      }
      
      return hydratedResponses;
    } catch (error) {
      console.error(`Error fetching responses with references: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get responses by panel with hydrated references
   * @param {string} panelId - Panel ID
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object[]>} - Hydrated responses
   */
  async getResponsesByPanelWithReferences(panelId, referenceOptions = {}) {
    try {
      const responses = await this.ResponseRepository.getResponsesByPanel(panelId);
      return this.getResponsesWithReferences(responses, referenceOptions);
    } catch (error) {
      throw new Error(`Error getting responses by panel with references: ${error.message}`);
    }
  }

  /**
   * Get responses by user with hydrated references
   * @param {string} userId - User ID
   * @param {Object} referenceOptions - Options for reference hydration
   * @returns {Promise<Object[]>} - Hydrated responses
   */
  async getResponsesByUserWithReferences(userId, referenceOptions = {}) {
    try {
      const responses = await this.ResponseRepository.getResponsesByUser(userId);
      return this.getResponsesWithReferences(responses, referenceOptions);
    } catch (error) {
      throw new Error(`Error getting responses by user with references: ${error.message}`);
    }
  }
}

module.exports = ResponseService; 