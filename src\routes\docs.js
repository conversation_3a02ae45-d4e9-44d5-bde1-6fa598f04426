const express = require('express');
const router = express.Router();

const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');
const { swaggerAuth } = require('../middleware');

// Swagger definition
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Sensory Analysis Panel Management API',
      description: 'API for managing sensory analysis panels, user registration, responses and panel data',
      version: '1.0.0',
    },
    servers: [
      {
        url: 'https://sensei.neuralresonance.icu',
        description: 'Remote development server',
      },
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  // Path to the API docs - finds all JSDoc annotated routes in the 'routes' directory
  apis: ['./models/*.js', './routes/*.js'],
};

const swaggerSpec = swaggerJsdoc(options);

router.use('/', swaggerAuth, swaggerUi.serve, swaggerUi.setup(swaggerSpec));

module.exports = router;