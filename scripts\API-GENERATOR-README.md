# API Project Generator

This tool creates projects by communicating with the Panel Registration API rather than directly accessing the database. It's useful for testing the API endpoints and ensuring that project creation works through the API.

## Prerequisites

- The Panel Registration API must be running (default: http://localhost:7890)
- You need a valid user account with API access
- The user must have permission to create projects and access organizations

## Installation

The script uses `axios` for API communication, which should be installed as a dependency:

```bash
npm install
```

## Usage

### Basic usage:

```bash
# Using npm script
npm run generate:api-projects

# Or direct command
node scripts/apiProjectGenerator.js
```

### Command-line options:

```bash
# Generate 5 projects
npm run generate:api-projects -- --count 5

# Use specific credentials
npm run generate:api-projects -- --email <EMAIL> --password mypassword

# Use specific organization
npm run generate:api-projects -- --organization org123

# Skip confirmation prompts
npm run generate:api-projects -- --force
```

### All available options:

| Option | Description | Default |
|--------|-------------|---------|
| `-c, --count <number>` | Number of projects to generate | 2 |
| `-e, --email <email>` | Email for login | (prompt) |
| `-p, --password <password>` | Password for login | (prompt) |
| `-t, --token <token>` | Auth token (if already authenticated) | (none) |
| `-o, --organization <id>` | Specific organization ID to use | (interactive selection) |
| `-f, --force` | Skip confirmation prompts | false |
| `-v, --verbose` | Enable verbose output | false |

## How It Works

1. **Authentication**: The script authenticates with the API using provided credentials or prompts for them
2. **Organization Selection**: It fetches organizations from the API and lets you select one (or uses the provided ID)
3. **Panel Selection**: It fetches panels associated with the organization to assign to projects
4. **Project Creation**: It generates projects with random data and creates them via the API
5. **Collaborator Assignment**: It adds random collaborators to each project using the API

## Examples

### Generate 3 projects for a specific organization with verbose output:

```bash
npm run generate:api-projects -- --count 3 --organization org123 --verbose
```

### Use a pre-authenticated token:

```bash
npm run generate:api-projects -- --token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Generate 10 projects non-interactively:

```bash
npm run generate:api-projects -- --count 10 --email <EMAIL> --password secret --force
```

## Troubleshooting

If you encounter issues:

1. Make sure the API is running and accessible at http://localhost:7890
2. Check that your user account has the necessary permissions
3. Use the `--verbose` flag to get more detailed error information
4. Ensure the API endpoints conform to the expected structure
5. If you get authentication errors, try generating a fresh token

## API Endpoints Used

The script interacts with these API endpoints:

- POST `/auth/login` - Authentication
- GET `/organizations` - Fetch organizations
- GET `/users` - Fetch users
- GET `/panels?organization={id}` - Fetch panels for an organization
- POST `/projects` - Create a project
- POST `/projects/{id}/collaborators` - Add collaborators to a project 