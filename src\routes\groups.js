const express = require("express");
const router = express.Router();

const { redisCache, firestoreModule } = require("../config");
const { authenticateUser, checkPermissions, paginationMiddleware } = require("../middleware");
const { validateGroup, validateGroupUpdate } = require("../models/group");
const { GroupService, UserService } = require("../services");
const paginateGroups = require("../util/paginateGroups");

router.all(
  "*",
  authenticateUser,
  checkPermissions(["survey", "admin", "insights"])
);

/**
 * @swagger
 * /groups:
 *   post:
 *     summary: Create a new group
 *     description: Creates a new group with the current user as creator
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Group'
 *     responses:
 *       201:
 *         description: Group created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Group'
 *                 - type: object
 *                   properties:
 *                     group_id:
 *                       type: string
 *                     created:
 *                       type: object
 *                     updated:
 *                       type: object
 *       400:
 *         description: Invalid group data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post("/", async (req, res) => {
  try {
    const userId = req.user.uid;
    let user = await UserService.getUserById(userId);
    const groupData = {
      ...req.body,
      createdBy: userId,
      organization_id: user.organizations[0],
    };

    console.log("User ID: ", userId);

    const groupId = await GroupService.createGroup(groupData);


    res.status(201).send({
      group_id: groupId,
      ...groupData,
      created: {
        _seconds: new Date().getTime() / 1000,
        _nanoseconds: 0,
      },
      updated: {
        _seconds: new Date().getTime() / 1000,
        _nanoseconds: 0,
      },
    });
  } catch (error) {
    res.status(500).send("Error creating group: " + error.message);
  }
});

/**
 * @swagger
 * /groups:
 *   get:
 *     summary: Get all groups for the current user
 *     description: Retrieves all groups that the authenticated user has created or has access to
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Groups retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get("/", paginationMiddleware, async (req, res) => {
  try {
    const userId = req.user.uid;
    let groups = await GroupService.getGroupsByUser(userId);

    // Apply pagination
    const paginatedGroups = await paginateGroups(groups, req.pagination.page, req.pagination.limit, req.pagination.startIndex);

    res.status(200).send(paginatedGroups);
  } catch (error) {
    res.status(500).send("Error fetching groups: " + error.message);
  }
});

/**
 * @swagger
 * /groups/{id}:
 *   get:
 *     summary: Get a group by ID
 *     description: Retrieves details for a specific group, with optional reference hydration
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group to retrieve
 *       - in: query
 *         name: includeReferences
 *         schema:
 *           type: boolean
 *         description: Whether to include hydrated references
 *       - in: query
 *         name: includeFullDetails
 *         schema:
 *           type: boolean
 *         description: Whether to include full details for all references
 *     responses:
 *       200:
 *         description: Group details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.get('/:id', authenticateUser, async (req, res) => {
    try {
        const { id } = req.params;
        const { includeReferences, includeFullDetails } = req.query;

        if (includeReferences) {
            const group = await GroupService.getGroupWithReferences(id, {
                includeFullDetails: includeFullDetails === 'true'
            });
            return res.json(group);
        }

        const group = await GroupService.read(id);
        res.json(group);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * @swagger
 * /groups/user/{userId}:
 *   get:
 *     summary: Get groups by user ID
 *     description: Retrieves all groups associated with a specific user, with optional reference hydration
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user
 *       - in: query
 *         name: includeReferences
 *         schema:
 *           type: boolean
 *         description: Whether to include hydrated references
 *       - in: query
 *         name: includeFullDetails
 *         schema:
 *           type: boolean
 *         description: Whether to include full details for all references
 *     responses:
 *       200:
 *         description: Groups retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/user/:userId', authenticateUser, async (req, res) => {
    try {
        const { userId } = req.params;
        const { includeReferences, includeFullDetails } = req.query;

        if (includeReferences) {
            const groups = await GroupService.getGroupsByUserWithReferences(userId, {
                includeFullDetails: includeFullDetails === 'true'
            });
            return res.json(groups);
        }

        const groups = await GroupService.getGroupsByUser(userId);
        res.json(groups);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * @swagger
 * /groups/organization/{organizationId}:
 *   get:
 *     summary: Get groups by organization ID
 *     description: Retrieves all groups associated with a specific organization, with optional reference hydration
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the organization
 *       - in: query
 *         name: includeReferences
 *         schema:
 *           type: boolean
 *         description: Whether to include hydrated references
 *       - in: query
 *         name: includeFullDetails
 *         schema:
 *           type: boolean
 *         description: Whether to include full details for all references
 *     responses:
 *       200:
 *         description: Groups retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/organization/:organizationId', authenticateUser, async (req, res) => {
    try {
        const { organizationId, userId } = req.params;
        const { includeReferences, includeFullDetails } = req.query;

        if (includeReferences) {
            const groups = await GroupService.getGroupsByOrganizationWithReferences(organizationId, {
                includeFullDetails: includeFullDetails === 'true'
            });
            return res.json(groups);
        }

        const groups = await GroupService.getByOrganization(organizationId);
        // let filtered = groups.filter((group) => group.createdBy === userId);
        res.json(groups);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * @swagger
 * /groups/{group_id}:
 *   put:
 *     summary: Update a group
 *     description: Updates an existing group with the provided data
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Group'
 *     responses:
 *       200:
 *         description: Group updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.put("/:group_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const groupId = req.params.group_id;
    const updateData = req.body;

    const group = await GroupService.getGroupbyGroupId(groupId)

    if(!group){
        return res.status(404).send("Group not found");
    }

    if (group.createdBy !== userId) {
        return res.status(403).send("User does not have access to this group");
    }

    await GroupService.updateGroup(groupId, updateData);

    res.status(200).send({ message: "Group updated successfully" });
  } catch (error) {
    res.status(500).send("Error updating group: " + error.message);
  }
});

/**
 * @swagger
 * /groups/{group_id}:
 *   delete:
 *     summary: Delete a group
 *     description: Deletes an existing group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group to delete
 *     responses:
 *       200:
 *         description: Group deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.delete("/:group_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const groupId = req.params.group_id;
    // const group = await firestoreModule.getDocument("groups", groupId);
    const group = await GroupService.getGroupbyGroupId(groupId);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }

    await GroupService.deleteGroup(groupId);

    res.status(200).send({ message: "Group deleted successfully" });
  } catch (error) {
    res.status(500).send("Error deleting group: " + error.message);
  }
});

// === User Management Routes ===

/**
 * @swagger
 * /groups/{group_id}/users/{user_id}:
 *   post:
 *     summary: Add a user to a group
 *     description: Associates a user with a group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group
 *       - in: path
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to add
 *     responses:
 *       200:
 *         description: User added to group successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.post("/:group_id/users/:user_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const { group_id, user_id } = req.params;
    
    // Check if user has access to the group
    const group = await GroupService.getGroupbyGroupId(group_id);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }
    
    const updatedGroup = await GroupService.addUserToGroup(group_id, user_id);
    
    res.status(200).send(updatedGroup);
  } catch (error) {
    res.status(500).send("Error adding user to group: " + error.message);
  }
});

/**
 * @swagger
 * /groups/{group_id}/users/{user_id}:
 *   delete:
 *     summary: Remove a user from a group
 *     description: Removes the association between a user and a group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group
 *       - in: path
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to remove
 *     responses:
 *       200:
 *         description: User removed from group successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.delete("/:group_id/users/:user_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const { group_id, user_id } = req.params;
    
    // Check if user has access to the group
    const group = await GroupService.getGroupbyGroupId(group_id);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }
    
    const updatedGroup = await GroupService.removeUserFromGroup(group_id, user_id);
    
    res.status(200).send(updatedGroup);
  } catch (error) {
    res.status(500).send("Error removing user from group: " + error.message);
  }
});

// === Panel Management Routes ===

/**
 * @swagger
 * /groups/{group_id}/panels/{panel_id}:
 *   post:
 *     summary: Add a panel to a group
 *     description: Associates a panel with a group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to add
 *     responses:
 *       200:
 *         description: Panel added to group successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.post("/:group_id/panels/:panel_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const { group_id, panel_id } = req.params;
    
    // Check if user has access to the group
    const group = await GroupService.getGroupbyGroupId(group_id);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }
    
    const updatedGroup = await GroupService.addPanelToGroup(group_id, panel_id);
    
    res.status(200).send(updatedGroup);
  } catch (error) {
    res.status(500).send("Error adding panel to group: " + error.message);
  }
});

/**
 * @swagger
 * /groups/{group_id}/panels/{panel_id}:
 *   delete:
 *     summary: Remove a panel from a group
 *     description: Removes the association between a panel and a group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group
 *       - in: path
 *         name: panel_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the panel to remove
 *     responses:
 *       200:
 *         description: Panel removed from group successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.delete("/:group_id/panels/:panel_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const { group_id, panel_id } = req.params;
    
    // Check if user has access to the group
    const group = await GroupService.getGroupbyGroupId(group_id);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }
    
    const updatedGroup = await GroupService.removePanelFromGroup(group_id, panel_id);
    
    res.status(200).send(updatedGroup);
  } catch (error) {
    res.status(500).send("Error removing panel from group: " + error.message);
  }
});

// === Sample Management Routes ===

/**
 * @swagger
 * /groups/{group_id}/samples/{sample_id}:
 *   post:
 *     summary: Add a sample to a group
 *     description: Associates a sample with a group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group
 *       - in: path
 *         name: sample_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the sample to add
 *     responses:
 *       200:
 *         description: Sample added to group successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.post("/:group_id/samples/:sample_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const { group_id, sample_id } = req.params;
    
    // Check if user has access to the group
    const group = await GroupService.getGroupbyGroupId(group_id);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }
    
    const updatedGroup = await GroupService.addSampleToGroup(group_id, sample_id);
    
    res.status(200).send(updatedGroup);
  } catch (error) {
    res.status(500).send("Error adding sample to group: " + error.message);
  }
});

/**
 * @swagger
 * /groups/{group_id}/samples/{sample_id}:
 *   delete:
 *     summary: Remove a sample from a group
 *     description: Removes the association between a sample and a group
 *     tags: [Groups]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the group
 *       - in: path
 *         name: sample_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the sample to remove
 *     responses:
 *       200:
 *         description: Sample removed from group successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have access to this group
 *       500:
 *         description: Server error
 */
router.delete("/:group_id/samples/:sample_id", async (req, res) => {
  try {
    const userId = req.user.uid;
    const { group_id, sample_id } = req.params;
    
    // Check if user has access to the group
    const group = await GroupService.getGroupbyGroupId(group_id);
    if (group.createdBy !== userId) {
      return res.status(403).send("User does not have access to this group");
    }
    
    const updatedGroup = await GroupService.removeSampleFromGroup(group_id, sample_id);
    
    res.status(200).send(updatedGroup);
  } catch (error) {
    res.status(500).send("Error removing sample from group: " + error.message);
  }
});

module.exports = router;

