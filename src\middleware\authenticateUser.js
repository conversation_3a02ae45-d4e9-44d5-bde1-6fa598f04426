const { firestoreModule } = require('../config');
// Middleware to verify Firebase token
const authenticateUser = async (req, res, next) => {
  if(process.env.NODE_ENV === 'development') {
    // req.user = { uid: 'sXyBm8Rl3NhcHApMeGNE8v4gkjC2' };
    req.user = { uid: '3ZwbNosYk8RUg8URxCvTgGmdsnA2' };
    next();
  } else {
    let status = req.path === '/session-validity' ? 400 : 401;
    console.log("Path: ", req.path);
    //console.log("Session cookie: ", req.cookies);
    const sessionCookie = req.cookies.session;
    if (!sessionCookie) return res.status(status).send('No session cookie found');
  
    try {
      const decodedClaims = await firestoreModule.auth.verifySessionCookie(sessionCookie, true);
      req.user = {
        uid: decodedClaims.uid,
        claims: decodedClaims
      };
      next();
    } catch (error) {
      res.status(status).send('Invalid session');
    }
  }
};

module.exports = authenticateUser;