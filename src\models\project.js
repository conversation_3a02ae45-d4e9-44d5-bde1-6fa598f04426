const Joi = require('joi');
const Table = require('cli-table3');

/**
 * @swagger
 * components:
 *   schemas:
 *     Project:
 *       type: object
 *       required:
 *         - project_id
 *         - name
 *         - description
 *         - organization_id
 *         - status
 *         - start_date
 *         - end_date
 *         - created_by
 *         - owner
 *       properties:
 *         project_id:
 *           type: string
 *           description: Unique identifier for the project
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           description: Display name of the project
 *         description:
 *           type: string
 *           minLength: 2
 *           maxLength: 500
 *           description: Description of the project
 *         organization_id:
 *           type: string
 *           description: ID of the organization that owns this project
 *         assigned_panels:
 *           type: array
 *           items:
 *             type: string
 *           description: List of panel IDs directly assigned to this project
 *         assigned_users:
 *           type: array
 *           items:
 *             type: string
 *           description: List of user IDs directly assigned to this project
 *         assigned_samples:
 *           type: array
 *           items:
 *             type: string
 *           description: List of sample/product IDs directly assigned to this project
 *         assigned_groups:
 *           type: object
 *           properties:
 *             user_groups:
 *               type: array
 *               items:
 *                 type: string
 *             panel_groups:
 *               type: array
 *               items:
 *                 type: string
 *             sample_groups:
 *               type: array
 *               items:
 *                 type: string
 *           description: Groups assigned to this project
 *         collaborators:
 *           type: array
 *           items:
 *             type: string
 *           description: List of user IDs who are collaborators on this project
 *         status:
 *           type: string
 *           enum: [draft, ongoing, scheduled, completed, archived]
 *           description: Current status of the project
 *         is_public:
 *           type: boolean
 *           default: false
 *           description: Whether the project is public
 *         completion_percentage:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           default: 0
 *           description: Percentage of project completion
 *         total_responses:
 *           type: number
 *           default: 0
 *           description: Total number of responses across all panels in the project
 *         start_date:
 *           type: string
 *           format: date-time
 *           description: Date and time when the project was scheduled to start
 *         end_date:
 *           type: string
 *           format: date-time
 *           description: Date and time when the project was scheduled to end
 *         created_by:
 *           type: string
 *           description: ID of the user who created the project
 *         owner:
 *           type: string
 *           description: ID of the user who owns/manages the project
 */

// Define project schema using Joi for robust validation
const projectSchema = Joi.object({
  project_id: Joi.string()
    .required()
    .description('Unique identifier for the project'),
    
  name: Joi.string()
    .required()
    .min(2)
    .max(100)
    .description('Display name of the project'),
    
  description: Joi.string()
    .required()
    .min(2)
    .max(500)
    .description('Description of the project'),
    
  organization_id: Joi.string()
    .required()
    .description('ID of the organization that owns this project'),
    
  // Direct assignments
  assigned_panels: Joi.array()
    .items(Joi.string())
    .default([])
    .description('List of panel IDs directly assigned to this project'),
    
  assigned_users: Joi.array()
    .items(Joi.string())
    .default([])
    .description('List of user IDs directly assigned to this project'),
    
  assigned_samples: Joi.array()
    .items(Joi.string())
    .default([])
    .description('List of sample/product IDs directly assigned to this project'),
  
  // Group assignments
  assigned_groups: Joi.object({
    user_groups: Joi.array().items(Joi.string()).default([]),
    panel_groups: Joi.array().items(Joi.string()).default([]),
    sample_groups: Joi.array().items(Joi.string()).default([])
  })
    .default({ user_groups: [], panel_groups: [], sample_groups: [] })
    .description('Groups assigned to this project'),
  
  // Collaborators - people who can view and manage the project
  collaborators: Joi.array()
    .items(Joi.string())
    .default([])
    .description('List of user IDs who are collaborators on this project'),
    
  status: Joi.string()
    .valid('draft', 'ongoing', 'scheduled', 'completed', 'archived')
    .required()
    .description('Current status of the project'),

  is_public: Joi.boolean()
    .default(false)
    .description('Whether the project is public'),

  completion_percentage: Joi.number()
    .min(0)
    .max(100)
    .default(0)
    .description('Percentage of project completion'),

  total_responses: Joi.number()
    .default(0)
    .description('Total number of responses across all panels in the project'),

  start_date: Joi.date()
    .required()
    .description('Date and time when the project was scheduled to start'),
    
  end_date: Joi.date()
    .required()
    .description('Date and time when the project was scheduled to end'),
    
  created_by: Joi.string()
    .required()
    .description('ID of the user who created the project'),
    
  owner: Joi.string()
    .required()
    .description('ID of the user who owns/manages the project')
});

// Faker library for generating realistic test data
const { faker } = require('@faker-js/faker');

// Generate a fake project document for testing
const generateFakeProject = (overrides = {}) => {
  const fakeProject = {
    project_id: faker.string.alphanumeric(20),
    name: faker.company.name() + ' Project',
    description: faker.lorem.paragraph(),
    organization_id: faker.string.alphanumeric(20),
    assigned_panels: [faker.string.alphanumeric(20), faker.string.alphanumeric(20)],
    assigned_users: [faker.string.alphanumeric(20)],
    assigned_samples: [faker.string.alphanumeric(20), faker.string.alphanumeric(20)],
    assigned_groups: {
      user_groups: [faker.string.alphanumeric(20)],
      panel_groups: [],
      sample_groups: [faker.string.alphanumeric(20)]
    },
    collaborators: [faker.string.alphanumeric(20), faker.string.alphanumeric(20)],
    status: faker.helpers.arrayElement(['draft', 'ongoing', 'scheduled', 'completed', 'archived']),
    is_public: faker.datatype.boolean(),
    completion_percentage: faker.number.int({ min: 0, max: 100 }),
    total_responses: faker.number.int({ min: 0, max: 500 }),
    start_date: faker.date.recent(),
    end_date: faker.date.future(),
    created_by: faker.string.alphanumeric(20),
    owner: faker.string.alphanumeric(20),
    ...overrides
  };

  // Validate the generated data
  const { error } = validateProject(fakeProject);
  if (error) {
    throw new Error(`Generated invalid project: ${error.message}`);
  }

  return fakeProject;
};

// Generate multiple fake projects
const generateFakeProjects = (count = 1, overrides = {}) => {
  return Array.from({ length: count }, () => generateFakeProject(overrides));
};

// Validate project document against schema
const validateProject = (project) => {
  return projectSchema.validate(project, {
    abortEarly: false,
    stripUnknown: true,
    presence: 'required'
  });
};

// Add validation function for updates
const validateProjectUpdate = (data) => {
  // Create a schema that makes all fields optional for updates
  const updateSchema = Joi.object(
    Object.entries(projectSchema.describe().keys).reduce((acc, [key, value]) => {
      // Convert each field to be optional
      acc[key] = Joi.any().optional();
      return acc;
    }, {})
  );

  return updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Convert Firestore document to project model
const fromFirestore = (snapshot) => {
  const data = snapshot.data();
  const { value, error } = validateProject(data);
  
  if (error) {
    throw new Error(`Invalid project data from Firestore: ${error.message}`);
  }
  
  return value;
};

// Convert project model to Firestore document
const toFirestore = (project) => {
  const { value, error } = validateProject(project);
  
  if (error) {
    throw new Error(`Invalid project data for Firestore: ${error.message}`);
  }
  
  return value;
};

// Format projects data into a CLI-friendly table
const formatProjectsTable = (projects) => {
  const table = new Table({
    head: ['Name', 'Organization', 'Status', 'Privacy', 'Panels', 'Completion %', 'End Date'],
    colWidths: [30, 25, 12, 10, 10, 12, 15]
  });

  projects.forEach(project => {
    table.push([
      project.name,
      project.organization_id,
      project.status,
      project.is_public ? 'Public' : 'Private',
      project.assigned_panels.length,
      project.completion_percentage + '%',
      project.end_date?.toDateString() || 'N/A'
    ]);
  });

  return table.toString();
};

// Format detailed project information
const formatProjectDetails = (project) => {
  const table = new Table();

  table.push(
    { 'Name': project.name },
    { 'Project ID': project.project_id },
    { 'Organization ID': project.organization_id },
    { 'Description': project.description },
    { 'Status': project.status },
    { 'Privacy': project.is_public ? 'Public' : 'Private' },
    { 'Completion': project.completion_percentage + '%' },
    { 'Responses': project.total_responses },
    { 'Owner': project.owner },
    { 'Created By': project.created_by },
    { 'Start Date': project.start_date?.toDate?.() || project.start_date },
    { 'End Date': project.end_date?.toDate?.() || project.end_date },
    { 'Assigned Panels': project.assigned_panels.join(', ') },
    { 'Assigned Users': project.assigned_users.join(', ') },
    { 'Collaborators': project.collaborators.join(', ') },
    { 'Assigned Samples': project.assigned_samples.join(', ') },
    { 'User Groups': project.assigned_groups.user_groups.join(', ') },
    { 'Panel Groups': project.assigned_groups.panel_groups.join(', ') },
    { 'Sample Groups': project.assigned_groups.sample_groups.join(', ') }
  );

  return table.toString();
};

module.exports = {
  projectSchema,
  generateFakeProject,
  generateFakeProjects,
  validateProject,
  validateProjectUpdate,
  fromFirestore,
  toFirestore,
  formatProjectsTable,
  formatProjectDetails
}; 